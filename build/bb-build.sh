#!/bin/bash
###
 # @Author: youweizhi
 # @LastEditors: youweizhi
### 
set -e

# 用于构建组件

# 当前目录为仓库中的build目录 

# 加载容器环境变量和函数
source bb-env.sh

# 切换到仓库所在的根目录
# pushd ..

# 执行容器内的构建命令
#bb_mvn -U package
# bb_mvn_package -U

# for npm 
# bb_npm_install && bb_npm_run_build

export RTE_SDK=$PREP_BUILD_ROOT/dpdk-stable/dpdk-stable-17.08.1/

# PWD=`pwd`
# LD_LIBRARY_PATH="${PWD}/../src/hw/gw_parser/core:${PWD}/../src/hw/gw_parser/liblicutils_c_sdk:${PWD}/../src/hw/gw_parser/libaws_api_c++/lib64:${PWD}/../src/hw/gw_parser/nacos_c++:${PWD}/../src/hw/gw_parser/utils/file_type"${LD_LIBRARY_PATH:+:$LD_LIBRARY_PATH}
# echo $LD_LIBRARY_PATH
# export LD_LIBRARY_PATH

# # 单元测试 
# pushd ${PWD}/../src/hw/gw_parser/
# make BUILD_SCHEME=CiUnitTes

# # Parser http
# pushd test/parser/http_parser/
# make BUILD_SCHEME=CiUnitTes run_test
# popd

# # Parser ftp
# pushd test/parser/ftp_parser/
# make BUILD_SCHEME=CiUnitTes run_test
# popd

# # Parser mail
# pushd test/parser/mail_parser/
# make BUILD_SCHEME=CiUnitTes run_test
# popd

# # Parser ssl https
# pushd test/parser/ssl_parser/https_parser
# make BUILD_SCHEME=CiUnitTes run_test
# popd

# popd

./build.sh