#!/bin/sh


#
# 在CentOS7运行环境中，构建安装包


BASEDIR=$(dirname $0)/
if [[ "${BASEDIR}" = "/" ]]; then
	echo "basedir error"
	exit 0
fi



# 函数调用日志
function func_log()
{
	func=$1
	shift
	# ${func} $*
	# return
	echo Enter ${func} ...
	${func} $*
	echo Leave ${func} .
	echo
}


function compile_py()
{
	python -m py_compile $@
}


function filter_sh_comment()
{
	sed -i -e 's/^[ \t]*#$//g' -e 's/^[ \t]*#[^!*].*$//g'  $@
}

# 检查下载的文件
function build_chk_soft()
{
	url=$3
	filepath=$2
	md5=$1
	ua="Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.63 Safari/537.36"
	if [[ "${url}" = "" ]]; then
		return
	fi

	# download file
	if [[ ! -f "${filepath}" ]]; then
		echo "download file ${filepath}"
		#curl --user-agent "${UA}"  -o "${filepath}"  "${url}"
		# for 3xx
		wget --user-agent "${UA}" -O "${filepath}"  "${url}"
	fi

	#check md5
	if [[ `(md5sum "${filepath}" 2> /dev/null || md5 "${filepath}"  2> /dev/null) | grep ${md5} | wc -l` -ne 1 ]]; then
		echo "file md5 error  ${filepath}"
		exit 11
	fi

}



# 编译环境准备
function build_env()
{
	# 编译环境检测
	if [[ `cat /etc/redhat-release 2> /dev/null | grep CentOS | wc -l` -eq 0 ]]; then
		echo 'Error: Must in Centos 7'
		exit 10
	fi

	if [[ ! -d "${BASEDIR}./soft/" ]]; then
		mkdir -p "${BASEDIR}./soft/"
	fi

	# # https://fast.dpdk.org/rel/dpdk-17.05.2.tar.xz
	# # 	37afc9ce410d8e6945a1beb173074003
	# build_chk_soft 37afc9ce410d8e6945a1beb173074003 "${BASEDIR}./soft/dpdk-17.05.2.tar.xz"  "https://fast.dpdk.org/rel/dpdk-17.05.2.tar.xz"

	# https://fast.dpdk.org/rel/dpdk-17.08.1.tar.xz
	# 	faea23aa2899508b1738999d6f12502c
	build_chk_soft faea23aa2899508b1738999d6f12502c "${BASEDIR}./soft/dpdk-17.08.1.tar.xz"  "https://fast.dpdk.org/rel/dpdk-17.08.1.tar.xz"

	# http://rpmfind.net/linux/epel/7/x86_64/Packages/l/lz4-1.7.3-1.el7.x86_64.rpm
	# 	896e28d4ebbc3dfd0e720af42292fa7b
	build_chk_soft 777edcd5b165000ccc4051696e7419ca "${BASEDIR}./soft/lz4-1.7.5-2.el7.x86_64.rpm"  "http://mirror.centos.org/centos/7/os/x86_64/Packages/lz4-1.7.5-2.el7.x86_64.rpm"

	# # http://rpmfind.net/linux/epel/7/x86_64/Packages/l/librdkafka-0.11.1-1.el7.x86_64.rpm
	# # 	832d4671e35aef649c449ab4211cb50a
	# build_chk_soft 832d4671e35aef649c449ab4211cb50a "${BASEDIR}./soft/librdkafka-0.11.1-1.el7.x86_64.rpm"  "http://rpmfind.net/linux/epel/7/x86_64/Packages/l/librdkafka-0.11.1-1.el7.x86_64.rpm"

	# http://rpmfind.net/linux/epel/7/x86_64/Packages/l/librdkafka-0.11.3-1.el7.x86_64.rpm
	# 	96324aa9e7b41bbd50ec002251716bc2
	build_chk_soft 1420b52c50892f85b4c4fa53dcdeb67b  "${BASEDIR}./soft/librdkafka-0.11.5-1.el7.x86_64.rpm"  "http://rpmfind.net/linux/epel/7/x86_64/Packages/l/librdkafka-0.11.5-1.el7.x86_64.rpm"

	build_chk_soft 2f6bbb1a88791bcbc76a6b00066d9172 "${BASEDIR}./soft/gperftools-libs-2.6.1-1.el7.x86_64.rpm" "ftp://ftp.ntua.gr//pub/linux/centos/7.6.1810/os/x86_64/Packages/gperftools-libs-2.6.1-1.el7.x86_64.rpm"

	# https://bootstrap.pypa.io/get-pip.py
	# 	3b74f5cd0740a05802a23b019ce579a3
	build_chk_soft 3b74f5cd0740a05802a23b019ce579a3 "${BASEDIR}./soft/get-pip.py"  "https://bootstrap.pypa.io/get-pip.py"

	if [[ ! -d "${BASEDIR}./soft/pip_dp/" ]]; then
		mkdir -p "${BASEDIR}./soft/pip_dp/"
	fi

	#pip download -d "${BASEDIR}./soft/pip_dp/"  pip setuptools  wheel
	if [[ -f "${BASEDIR}./soft/pip_dp/pip-9.0.1-py2.py3-none-any.whl" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" --no-index --find-links="${BASEDIR}./soft/pip_dp/" pip==9.0.1 setuptools==36.6.0 wheel==0.30.0
	else
		pip download -d "${BASEDIR}./soft/pip_dp/" pip==9.0.1 setuptools==36.6.0 wheel==0.30.0
	fi
	# pip download -d "${BASEDIR}./soft/pip_dp/" supervisor
	if [[ -f "${BASEDIR}./soft/pip_dp/supervisor-3.3.3.tar.gz" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" --no-index --find-links="${BASEDIR}./soft/pip_dp/" supervisor==3.3.3 meld3==1.0.2
	else
		pip download -d "${BASEDIR}./soft/pip_dp/" supervisor==3.3.3 meld3==1.0.2
	fi

	# pip download -d "${BASEDIR}./soft/pip_dp/" flask requests flask-gzip Flask-GzipBomb httpbin
	if [[ -f "${BASEDIR}./soft/pip_dp/Flask-0.12.2-py2.py3-none-any.whl" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" --no-index --find-links="${BASEDIR}./soft/pip_dp/" blinker==1.4 brotlipy==0.7.0 certifi==2017.7.27.1 cffi==1.11.2 chardet==3.0.4 click==6.7 colorama==0.3.9 contextlib2==0.5.5 crayons==0.1.2 dateparser==0.6.0 decorator==4.1.2 enum34==1.1.6 Flask==0.12.2 Flask-Cache==0.13.1 Flask-Common==0.2.0 Flask-gzip==0.1 Flask-GzipBomb==0.1.0 Flask-Limiter==******* greenlet==0.4.12 gunicorn==19.7.1 httpbin==0.6.2 humanize==0.5.1 idna==2.6 itsdangerous==0.24 Jinja2==2.9.6 limits==1.2.1 MarkupSafe==1.0 maya==0.3.3 meinheld==0.6.1 pendulum==1.3.1 pycparser==2.18 python-dateutil==2.6.1 pytz==2017.3 pytzdata==2017.3 raven==6.3.0 regex==2017.9.23 requests==2.18.4 ruamel.ordereddict==0.4.13 ruamel.yaml==0.15.34 six==1.11.0 tzlocal==1.4 urllib3==1.22 Werkzeug==0.12.2 whitenoise==3.3.1
	else
		pip download -d "${BASEDIR}./soft/pip_dp/" blinker==1.4 brotlipy==0.7.0 certifi==2017.7.27.1 cffi==1.11.2 chardet==3.0.4 click==6.7 colorama==0.3.9 contextlib2==0.5.5 crayons==0.1.2 dateparser==0.6.0 decorator==4.1.2 enum34==1.1.6 Flask==0.12.2 Flask-Cache==0.13.1 Flask-Common==0.2.0 Flask-gzip==0.1 Flask-GzipBomb==0.1.0 Flask-Limiter==******* greenlet==0.4.12 gunicorn==19.7.1 httpbin==0.6.2 humanize==0.5.1 idna==2.6 itsdangerous==0.24 Jinja2==2.9.6 limits==1.2.1 MarkupSafe==1.0 maya==0.3.3 meinheld==0.6.1 pendulum==1.3.1 pycparser==2.18 python-dateutil==2.6.1 pytz==2017.3 pytzdata==2017.3 raven==6.3.0 regex==2017.9.23 requests==2.18.4 ruamel.ordereddict==0.4.13 ruamel.yaml==0.15.34 six==1.11.0 tzlocal==1.4 urllib3==1.22 Werkzeug==0.12.2 whitenoise==3.3.1
	fi

	# pip download -d "${BASEDIR}./soft/pip_dp/" Flask-HTTPAuth
	# Flask-HTTPAuth-3.2.3.tar.gz
	if [[ -f "${BASEDIR}./soft/pip_dp/Flask_HTTPAuth-3.2.3-py2.py3-none-any.whl" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" --no-index --find-links="${BASEDIR}./soft/pip_dp/" Flask-HTTPAuth==3.2.3
	else
		pip download -d "${BASEDIR}./soft/pip_dp/" Flask-HTTPAuth==3.2.3
	fi

	build_chk_soft 9495126aafd2659d357ea66a969c3fe1 "${BASEDIR}./soft/openssl-1.1.0i.tar.gz"  "https://www.openssl.org/source/openssl-1.1.0i.tar.gz"
	build_chk_soft 44d667c142d7cda120332623eab69f40 "${BASEDIR}./soft/zlib-1.2.8.tar.gz"  "https://nchc.dl.sourceforge.net/project/libpng/zlib/1.2.8/zlib-1.2.8.tar.gz"

}

function filter_sh_comment()
{
	sed -i -e 's/^[ \t]*#$//g' -e 's/^[ \t]*#[^!*].*$//g'  $@
}

function build_version()
{
  GW_VER_FILE="../src/hw/gw_parser/include/gw_ver.h"
  GW_VER_MAJOR=`grep "GW_VER_MAJOR" ${GW_VER_FILE} | awk -F'[ ]' '{print $3}'`
  GW_VER_MINOR=`grep "GW_VER_MINOR" ${GW_VER_FILE} | awk -F'[ ]' '{print $3}'`
  GW_VER_REVISION=`grep "GW_VER_REVISION" ${GW_VER_FILE} | awk -F'[ ]' '{print $3}'`
  GW_VER=${GW_VER_MAJOR}.${GW_VER_MINOR}.${GW_VER_REVISION}
  echo "${GW_VER}" > ${BASEDIR}./hw/inst/version.txt
  #echo "${GW_VER}" > "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"version.txt
  #echo "${GW_VER}" > "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"version.txt
}

function copy_py_dynamic_lib()
{
	# flask requests flask-gzip Flask-GzipBomb httpbin
	#blinker==1.4 brotlipy==0.7.0 certifi==2017.7.27.1 cffi==1.11.2 chardet==3.0.4 click==6.7 colorama==0.3.9 contextlib2==0.5.5 crayons==0.1.2 dateparser==0.6.0 decorator==4.1.2 enum34==1.1.6 Flask==0.12.2 Flask-Cache==0.13.1 Flask-Common==0.2.0 Flask-gzip==0.1 Flask-GzipBomb==0.1.0 Flask-Limiter==******* greenlet==0.4.12 gunicorn==19.7.1 httpbin==0.6.2 humanize==0.5.1 idna==2.6 itsdangerous==0.24 Jinja2==2.9.6 limits==1.2.1 MarkupSafe==1.0 maya==0.3.3 meinheld==0.6.1 pendulum==1.3.1 pycparser==2.18 python-dateutil==2.6.1 pytz==2017.3 pytzdata==2017.3 raven==6.3.0 regex==2017.9.23 requests==2.18.4 ruamel.ordereddict==0.4.13 ruamel.yaml==0.15.34 six==1.11.0 tzlocal==1.4 urllib3==1.22 Werkzeug==0.12.2 whitenoise==3.3.1
	cp -f "${BASEDIR}./soft/pip_dp/"click-6.7-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-0.12.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"itsdangerous-0.24.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Jinja2-2.9.6-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"MarkupSafe-1.0.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Werkzeug-0.12.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"blinker-1.4.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"brotlipy-0.7.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"certifi-2017.7.27.1-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"cffi-1.11.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"chardet-3.0.4-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"colorama-0.3.9-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"contextlib2-0.5.5-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"crayons-0.1.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"dateparser-0.6.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"decorator-4.1.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"enum34-1.1.6-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-Cache-0.13.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-Common-0.2.0.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-gzip-0.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask_GzipBomb-0.1.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-Limiter-*******.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"greenlet-0.4.12-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"gunicorn-19.7.1-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"httpbin-0.6.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"humanize-0.5.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"idna-2.6-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"limits-1.2.1-py2-none-any.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"MarkupSafe-1.0.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"maya-0.3.3-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"meinheld-0.6.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pendulum-1.3.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pycparser-2.18.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"python_dateutil-2.6.1-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pytz-2017.3-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pytzdata-2017.3-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"raven-6.3.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"regex-2017.09.23.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"requests-2.18.4-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"ruamel.ordereddict-0.4.13-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"ruamel.yaml-0.15.34-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"six-1.11.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"tzlocal-1.4.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"urllib3-1.22-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Werkzeug-0.12.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"whitenoise-3.3.1-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	#Flask-HTTPAuth==3.2.3
	cp -f "${BASEDIR}./soft/pip_dp/"Flask_HTTPAuth-3.2.3-py2.py3-none-any.whl "${BASEDIR}./hw/inst/pip_dp"

	#cp -f "${BASEDIR}./soft/pip_dp/"click-6.7-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-0.12.2-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"itsdangerous-0.24.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Jinja2-2.9.6-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"MarkupSafe-1.0.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Werkzeug-0.12.2-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"blinker-1.4.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"brotlipy-0.7.0-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"certifi-2017.7.27.1-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"cffi-1.11.2-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"chardet-3.0.4-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"colorama-0.3.9-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"contextlib2-0.5.5-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"crayons-0.1.2-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"dateparser-0.6.0-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"decorator-4.1.2-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"enum34-1.1.6-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-Cache-0.13.1.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-Common-0.2.0.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-gzip-0.1.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask_GzipBomb-0.1.0-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-Limiter-*******.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"greenlet-0.4.12-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"gunicorn-19.7.1-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"httpbin-0.6.2-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"humanize-0.5.1.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"idna-2.6-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"limits-1.2.1-py2-none-any.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"MarkupSafe-1.0.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"maya-0.3.3-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"meinheld-0.6.1.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"pendulum-1.3.1.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"pycparser-2.18.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"python_dateutil-2.6.1-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"pytz-2017.3-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"pytzdata-2017.3-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"raven-6.3.0-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"regex-2017.09.23.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"requests-2.18.4-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"ruamel.ordereddict-0.4.13-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"ruamel.yaml-0.15.34-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"six-1.11.0-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"tzlocal-1.4.tar.gz "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"urllib3-1.22-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Werkzeug-0.12.2-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"whitenoise-3.3.1-*.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask_HTTPAuth-3.2.3-py2.py3-none-any.whl "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp"

	#cp -f "${BASEDIR}./soft/pip_dp/"click-6.7-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-0.12.2-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"itsdangerous-0.24.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Jinja2-2.9.6-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"MarkupSafe-1.0.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Werkzeug-0.12.2-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"blinker-1.4.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"brotlipy-0.7.0-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"certifi-2017.7.27.1-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"cffi-1.11.2-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"chardet-3.0.4-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"colorama-0.3.9-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"contextlib2-0.5.5-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"crayons-0.1.2-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"dateparser-0.6.0-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"decorator-4.1.2-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"enum34-1.1.6-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-Cache-0.13.1.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-Common-0.2.0.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-gzip-0.1.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask_GzipBomb-0.1.0-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask-Limiter-*******.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"greenlet-0.4.12-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"gunicorn-19.7.1-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"httpbin-0.6.2-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"humanize-0.5.1.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"idna-2.6-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"limits-1.2.1-py2-none-any.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"MarkupSafe-1.0.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"maya-0.3.3-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"meinheld-0.6.1.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"pendulum-1.3.1.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"pycparser-2.18.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"python_dateutil-2.6.1-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"pytz-2017.3-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"pytzdata-2017.3-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"raven-6.3.0-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"regex-2017.09.23.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"requests-2.18.4-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"ruamel.ordereddict-0.4.13-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"ruamel.yaml-0.15.34-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"six-1.11.0-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"tzlocal-1.4.tar.gz "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"urllib3-1.22-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Werkzeug-0.12.2-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"whitenoise-3.3.1-*.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/pip_dp/"Flask_HTTPAuth-3.2.3-py2.py3-none-any.whl "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp"
}

function build_hw()
{
	VER=$(cat "${BASEDIR}./../src/inst/version_hw.txt" | head -n1)
	VER_FULL=$(cat "${BASEDIR}./../src/inst/version_hw.txt" | head -n2 | tail -n1)
	if [[ "${VER}" == "" ]]; then
		echo hw invalid version info
		return
	fi
	# 增加GIT版本号
	#GIT_VER_REVISION=`git rev-list HEAD | wc -l | awk '{print $1}'`
	#GIT_VER_REVISION=$(expr $(git rev-list --all | wc -l) "+" "1" "-" $(git rev-list --all  | grep -n $(git rev-parse HEAD) | cut -d: -f1))
	GIT_VER_REVISION=
	GIT_VER_SHA_FULL=`git rev-parse HEAD`
	GIT_VER_SHA_SHORT=`git rev-parse --short HEAD`
	if [[ ! "${GIT_VER_REVISION}" == "" ]]; then
		VER=${VER}.${GIT_VER_REVISION}
	fi
	if [[ ! "${GIT_VER_SHA_SHORT}" == "" ]]; then
		VER=${VER}-${GIT_VER_SHA_SHORT}
	fi
	#echo hw_v${VER}
	#DPDK_DIR=~/dpdk-stable/dpdk-stable-17.05.2/
	DPDK_DIR=~/dpdk-stable_virtual/dpdk-stable-17.08.1/

	rm -rf "${BASEDIR}./hw_vir/"

	mkdir -p "${BASEDIR}./hw_vir/tmp/"
	mkdir -p "${BASEDIR}./hw_vir/inst/"
    mkdir -p "${BASEDIR}./hw_vir/inst/stats/"
	mkdir -p "${BASEDIR}./hw_vir/inst/lib/"
	mkdir -p "${BASEDIR}./hw_vir/inst/kmod/"
	mkdir -p "${BASEDIR}./hw_vir/inst/gw_operation/"

	#mkdir -p "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#mkdir -p "${BASEDIT}./hw/gw_v1.3.7_to_1.10.0_update/stats/"          #状态显示服务目录
	#mkdir -p "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/lib/"            #存放动态库
	#mkdir -p "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/kmod/"           #存放驱动

	#mkdir -p "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	#mkdir -p "${BASEDIT}./hw/gw_v1.5.4_to_1.10.0_update/stats/"          #状态显示服务目录
	#mkdir -p "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/lib/"            #存放动态库
	#mkdir -p "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/kmod/"           #存放驱动
  	build_version

	# get-pip
	mkdir -p "${BASEDIR}./hw_vir/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/get-pip.py" "${BASEDIR}./hw_vir/inst/"
	if [[   true ]]; then
		compile_py "${BASEDIR}./hw_vir/inst/"*.py
		rm -f "${BASEDIR}./hw_vir/inst/"*.py
	fi

	#1.3.7升级到1.10.0,需要安装提供给显示状态服务的python库，在升级包中需要包含，进行安装
	#mkdir -p "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/get-pip.py" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#if [[ true ]];then
		#compile_py "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"*.py
		#rm -f "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"*.py
	#fi

	#1.3.5升级到1.5.1,需要安装提供给显示状态服务的python库，在升级包中需要包含，进行安装
	#mkdir -p "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/pip_dp/"
	#cp -f "${BASEDIR}./soft/get-pip.py" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	#if [[ true ]];then
		#compile_py "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"*.py
		#rm -f "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"*.py
	#fi


	# pip==9.0.1 setuptools==36.6.0 wheel==0.30.0
	cp -f "${BASEDIR}./soft/pip_dp/"wheel-0.30.0-*.whl "${BASEDIR}./hw_vir/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"setuptools-36.6.0-*.whl "${BASEDIR}./hw_vir/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pip-9.0.1-*.whl "${BASEDIR}./hw_vir/inst/pip_dp/"

	# supervisor==3.3.3 meld3==1.0.2
	cp -f "${BASEDIR}./soft/pip_dp/"supervisor-3.3.3.tar.gz "${BASEDIR}./hw_vir/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"meld3-1.0.2-*.whl "${BASEDIR}./hw_vir/inst/pip_dp/"

	# librdkafka
	#cp -f "${BASEDIR}./soft/librdkafka-0.11.1-1.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/librdkafka-0.11.5-1.el7.x86_64.rpm" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}./soft/lz4-1.7.5-2.el7.x86_64.rpm" "${BASEDIR}./hw_vir/inst/"

	cp -f "${BASEDIR}./soft/gperftools-libs-2.6.1-1.el7.x86_64.rpm" "${BASEDIR}./hw_vir/inst/"
	#cp -f "${BASEDIR}./soft/gperftools-libs-2.6.1-1.el7.x86_64.rpm" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}./soft/gperftools-libs-2.6.1-1.el7.x86_64.rpm" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"

	copy_py_dynamic_lib

	#获取产品序列号
	if [[ -z ${PRODUCT_ID} ]];then
	    PRODUCT_ID=`cat "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h" | grep GW_PRODUCT_SERIAL_ID | awk '{print $3}' | awk -F '["]' '{print $2}'`
	else
		sed -i "s,GW_PRODUCT_SERIAL_ID.*,GW_PRODUCT_SERIAL_ID \"${PRODUCT_ID}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"
	fi

	# 替换GIT版本号
	#sed -i  "s,GW_VER_REVISION.*,GW_VER_REVISION ${GIT_VER_REVISION}," "${BASEDIR}../src/hw/gw_parser/gw_ver.h"
	sed -i  "s,GW_GIT_VER_SHA.*,GW_GIT_VER_SHA \"${GIT_VER_SHA_FULL}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"
	sed -i  "s,GW_GIT_VER_SHORT_SHA.*,GW_GIT_VER_SHORT_SHA \"${GIT_VER_SHA_SHORT}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"

	BLD_DIR=`pwd`
	export RTE_SDK=${DPDK_DIR};
	export RTE_TARGET=build
	# 指定内核版本
	if [[ -d /usr/src/kernels/3.10.0-693.17.1.el7.x86_64/ ]]; then
		export RTE_KERNELDIR=/usr/src/kernels/3.10.0-693.17.1.el7.x86_64/
	else
		echo ERROR: Not found kernel-devel-3.10.0-693.17.1
		echo Please install kernel-devel-3.10.0-693.17.1.el7.x86_64.rpm
		exit -1
	fi

	# 编译 dpdk库
	if [[ ! -d "${DPDK_DIR}./" ]]; then
		mkdir -p "${DPDK_DIR}./"
	fi
	if [[ ! -d "${DPDK_DIR}./"/build ]]; then
		# sudo yum install -y gcc kernel-devel libpcap-devel
		# sudo yum install -y numactl-devel
		# TODO patch max_rx_pktlen;
		#tar xvf "${BASEDIR}./soft/dpdk-17.05.2.tar.xz" -C "${DPDK_DIR}./../"
		tar xvf "${BASEDIR}./soft/dpdk-17.08.1.tar.xz" -C "${DPDK_DIR}./../"
		cd "${DPDK_DIR}./"
		make config T=x86_64-native-linuxapp-gcc

        # for old cpu ex. dl580 g7 E7540
        sed -ri 's,(CONFIG_RTE_MACHINE=).*,\1"default",' build/.config
        sed -ri 's,(CONFIG_RTE_ENABLE_AVX=).*,\1n,' build/.config # no avx

		sed -ri 's,(PMD_PCAP=).*,\1y,' build/.config
		sed -ri 's,(SHARED_LIB=).*,\1y,' build/.config
		make
		cd "${BLD_DIR}"
	fi
	if [[ ! -f "${RTE_SDK}./build/lib/libdpdk.so" ]]; then
		echo "dpdk compile error!";
		return
	fi
	if [[ `ls "${RTE_SDK}./build/kmod/"*.ko 2>/dev/null | wc -l` -eq 0 ]]; then
		echo "dpdk compile error!";
		return
	fi
	if [[ `ls "${RTE_SDK}./build/lib/"librte_*.so.* 2>/dev/null | wc -l` -eq 0 ]]; then
		echo "dpdk compile error!";
		return
	fi

	# 编译 openssl 1.1.0
	OPENSSL_BLD_DIR="${BASEDIR}./openssl-1.1.0i/"
	tar xf "${BASEDIR}./soft/openssl-1.1.0i.tar.gz"
	cd openssl-1.1.0i
	./config --prefix=/opt/openssl
	make
	sudo make install
	cd "${BLD_DIR}"

	# 编译 qat 1.7
	sudo yum install -y boost-devel systemd-devel gcc gcc-c++
	QAT_BLD_DIR="${BASEDIR}./QAT1.7.Upstream.L.1.0.3_42/"
	tar xf "${BASEDIR}./soft/QAT1.7.Upstream.L.1.0.3_42.tar.gz"
	cd QAT1.7.Upstream.L.1.0.3_42
	./configure --enable-dc-dyn
	if [[ -d /usr/src/kernels/3.10.0-693.17.1.el7.x86_64/ ]]; then
		export KERNEL_SOURCE_ROOT=/usr/src/kernels/3.10.0-693.17.1.el7.x86_64/
	else
		echo ERROR: Not found kernel-devel-3.10.0-693.17.1
		echo Please install kernel-devel-3.10.0-693.17.1.el7.x86_64.rpm
		exit -1
	fi
	make
	cd "${BLD_DIR}"

	# 编译 QAT_Engine 0.5.37
	sudo yum install -y libtool automake
	QAT_ENGINE_BLD_DIR="${BASEDIR}./QAT_Engine-0.5.37/"
	tar xf "${BASEDIR}./soft/QAT_Engine-0.5.37.tar.gz"
	cd QAT_Engine-0.5.37
	sh autogen.sh
	./configure --with-qat_dir=`pwd`/../QAT1.7.Upstream.L.1.0.3_42 --with-openssl_dir=`pwd`/../openssl-1.1.0i --with-openssl_install_dir=/opt/openssl --enable-upstream_driver --enable-usdm
	make
	cd "${BLD_DIR}"

	# 编译 zlibshim 0.4.10
	# ZLIBSHIM_BLD_DIR="${BASEDIR}./zlibshim0.4.10/"
	# mkdir zlibshim0.4.10
	# tar xf "${BASEDIR}./soft/zlibshim0.4.10-001.tar.gz" -C zlibshim0.4.10
	# cp "${BASEDIR}./soft/zlib-1.2.8.tar.gz" zlibshim0.4.10/
	# export LD_LIBRARY_PATH="/opt/openssl/lib/:`pwd`/./build/zlib-1.2.8/"
	# cd zlibshim0.4.10
	# sudo modprobe intel_qat
	# rm -rf zlib-1.2.8
	# ( echo 1; echo; echo 1; echo 0 ) | LIBRARY_PATH="`pwd`/../QAT1.7.Upstream.L.1.0.3_42/build/:/opt/openssl/lib/:`pwd`/./zlib-1.2.8/" KERNEL_SOURCE_ROOT=/usr/src/kernels/3.10.0-693.17.1.el7.x86_64/ PATH_AUTOCNF=include/generated/ ICP_ROOT="`pwd`/../QAT1.7.Upstream.L.1.0.3_42" ICP_BUILD_OUTPUT="`pwd`/../QAT1.7.Upstream.L.1.0.3_42/build" loopB=0  ./install.sh
	# md5sum zlib-1.2.8/libz.so.1.2.8.f-Intel-QAT-ver-0-4-10-001
	# cd "${BLD_DIR}"

	# 编译 libcapdpdk
	#cd "${BASEDIR}../src/hw/capdpdk/"
	#make clean;make
	#if [[ ! -f "./build/lib/libcapdpdk.so" ]]; then
	#	echo "capdpdk compile error!";
	#	return
	#fi
	echo "BLD_DIR=${BLD_DIR}"
	# 编译 gw_parser主程序
	cd "../src/hw/gw_parser/"
	make clean;
	BUILD_SCHEME=Release CPLUS_INCLUDE_PATH=/opt/Python3/include/python3.7m LIBRARY_PATH=/opt/Python3/lib  make
	if [[ ! -f "./gw_parser" ]]; then
		echo "gw_parser compile error!";
		return
	fi

	#编译 watch_process程序
	#cd "../watch_process"
	#make clean;make
	#if [[ ! -f "./watch_process" ]]; then
	#	echo "watch process compile error!";
	#	return
	#fi

	cd "${BLD_DIR}"
	#编译 stats python 服务  src/hw/gw_stats_srv
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"usr_cfg.json  "${BASEDIR}./hw_vir/inst/stats"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"gw_stat_srv.py "${BASEDIR}./hw_vir/inst/stats/"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"gw_stat_srv_license.py "${BASEDIR}./hw_vir/inst/stats/"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"_licutils.so "${BASEDIR}./hw_vir/inst/stats/"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"licutils.pyc "${BASEDIR}./hw_vir/inst/stats/"
	if [[ true ]]; then
		compile_py "${BASEDIR}./hw_vir/inst/stats/"*.py
			rm -f "${BASEDIR}./hw_vir/inst/stats/"*.py
	fi

	#cp -f "${BASEDIR}../src/hw/gw_stats_srv/"usr_cfg.json  "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/stats"
	#cp -f "${BASEDIR}../src/hw/gw_stats_srv/"gw_stat_srv.py "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/stats/"
	#if [[ true ]]; then
		#compile_py "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/stats/"*.py
		#rm -f "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/stats/"*.py
	#fi

	#cp -f "${BASEDIR}../src/hw/gw_stats_srv/"usr_cfg.json  "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/stats"
	#cp -f "${BASEDIR}../src/hw/gw_stats_srv/"gw_stat_srv.py "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/stats/"
	#if [[ true ]]; then
		#compile_py "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/stats/"*.py
		#rm -f "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/stats/"*.py
	#fi

	if [[ true ]]; then
		compile_py "${BASEDIR}./hw_vir/inst/gw_operation/"*.py
			rm -f "${BASEDIR}./hw_vir/inst/gw_operation/"*.py
	fi
	cp -f "${BASEDIR}../src/gw_operation/"gw_operation.service "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/gw_operation/"supervisord_gw_operation.conf "${BASEDIR}./hw_vir/inst/gw_operation/"

	# openssl
	OPENSSL_TAR_GZ="`pwd`/hw/inst/openssl-bin-1.1.0i.tar.gz"
	cd /opt/openssl/
	tar czf "${OPENSSL_TAR_GZ}" .
	cd -

	#cp -f  "${BASEDIR}/hw/inst/openssl-bin-1.1.0i.tar.gz" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f  "${BASEDIR}/hw/inst/openssl-bin-1.1.0i.tar.gz" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"

	# qat
	mkdir "${BASEDIR}./hw_vir/inst/qat/"
	cp -f "${QAT_BLD_DIR}./build/"* "${BASEDIR}./hw_vir/inst/qat/"
	#mkdir "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/qat/"
	#cp -f "${QAT_BLD_DIR}./build/"* "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/qat/"
	#mkdir "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/qat/"
	#cp -f "${QAT_BLD_DIR}./build/"* "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/qat/"

	# QAT_Engine
	# mkdir "${BASEDIR}./hw_vir/inst/qat_engine/"
	# cp -f "${QAT_ENGINE_BLD_DIR}./.libs/libqat.so.0.0.0" "${BASEDIR}./hw_vir/inst/qat_engine/"
	# cp -f "${BASEDIR}./../src/inst/dh895xcc_dev0.conf" "${BASEDIR}./hw_vir/inst/qat_engine/"

	#mkdir "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/qat_engine/"
	#cp -f "${QAT_ENGINE_BLD_DIR}./.libs/libqat.so.0.0.0" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/qat_engine/"
	#cp -f "${BASEDIR}./../src/inst/dh895xcc_dev0.conf" "${BASEDIR}./hw/inst/qat_engine/"

	#mkdir "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/qat_engine/"
	#cp -f "${QAT_ENGINE_BLD_DIR}./.libs/libqat.so.0.0.0" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/qat_engine/"
	#cp -f "${BASEDIR}./../src/inst/dh895xcc_dev0.conf" "${BASEDIR}./hw/inst/qat_engine/"

	# zlibshim
	# mkdir "${BASEDIR}./hw_vir/inst/zlibshim/"
	# cp -f "${ZLIBSHIM_BLD_DIR}./zlib-1.2.8/libz.so.1.2.8.f-Intel-QAT-ver-0-4-10-001" "${BASEDIR}./hw_vir/inst/zlibshim/"
	#mkdir "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/zlibshim/"
	#cp -f "${ZLIBSHIM_BLD_DIR}./zlib-1.2.8/libz.so.1.2.8.f-Intel-QAT-ver-0-4-10-001" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/zlibshim/"
	#mkdir "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/zlibshim/"
	#cp -f "${ZLIBSHIM_BLD_DIR}./zlib-1.2.8/libz.so.1.2.8.f-Intel-QAT-ver-0-4-10-001" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/zlibshim/"
	cp -f "${BASEDIR}../src/tools/dpdk-devbind" "${BASEDIR}./hw/inst/"


	# rte_kni.ko igb_uio.ko
	cp -f "${RTE_SDK}./build/kmod/"*.ko "${BASEDIR}./hw_vir/inst/kmod/"
	#cp -f "${RTE_SDK}./build/kmod/"*.ko "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/kmod/"
	#cp -f "${RTE_SDK}./build/kmod/"*.ko "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/kmod/"
 	# 安装时SO库需要进行符号连接
	cp -f "${RTE_SDK}./build/lib/"librte_*.so.* "${BASEDIR}./hw_vir/inst/lib/"
	#cp -f "${RTE_SDK}./build/lib/"librte_*.so.* "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/lib/"
	#cp -f "${RTE_SDK}./build/lib/"librte_*.so.* "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/lib/"
	# dpdk动态库
	cp -f "${RTE_SDK}./build/lib/libdpdk.so" "${BASEDIR}./hw_vir/inst/lib/"
	#cp -f "${RTE_SDK}./build/lib/libdpdk.so" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/lib/"
	#cp -f "${RTE_SDK}./build/lib/libdpdk.so" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/lib/"
	# 捕获数据包动态库
	#cp -f "${BASEDIR}../src/hw/capdpdk/build/lib/libcapdpdk.so" "${BASEDIR}./hw/inst/"
	#cp -f "${BASEDIR}../src/hw/capdpdk/build/lib/libcapdpdk.so" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/hw/capdpdk/build/lib/libcapdpdk.so" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	# 授权动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/liblicutils_c_sdk/liblicutils.so" "${BASEDIR}./hw_vir/inst/"
	#cp -f "${BASEDIR}../src/hw/gw_parser/liblicutils_c_sdk/liblicutils.so" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/hw/gw_parser/liblicutils_c_sdk/liblicutils.so" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	# 网关主程序
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser" "${BASEDIR}./hw_vir/inst/"
	#cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"

	#网关parser模块
	if [ ! -d  "${BASEDIR}./hw_vir/inst/parser" ]; then
		mkdir -p "${BASEDIR}./hw_vir/inst/parser"
	fi

	#if [ ! -d  "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/parser" ]; then
		#mkdir -p "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/parser"
	#fi

	#if [ ! -d  "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/parser" ]; then
		#mkdir -p "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/parser"
	#fi

	parser_sos="ftp_parser/ftp_parser.so hbase_parser/hbase_parser.so hdfs_parser/hdfs_parser.so hive_parser/hive_parser.so http_parser/http_parser.so mongo_parser/mongo_parser.so ssl_parser/ssl_parser.so"
	for parser_so in $parser_sos
	do
    	cp -f "${BASEDIR}../src/hw/gw_parser/parser/$parser_so" "${BASEDIR}./hw_vir/inst/parser"
		#cp -f "${BASEDIR}../src/hw/gw_parser/parser/$parser_so" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/parser"
		#cp -f "${BASEDIR}../src/hw/gw_parser/parser/$parser_so" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/parser"
	done


	#网关source模块
	if [ ! -d  "${BASEDIR}./hw_vir/inst/source" ]; then
		mkdir -p "${BASEDIR}./hw_vir/inst/source"
	fi

	#if [ ! -d  "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/source" ]; then
		#mkdir -p "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/source"
	#fi

	#if [ ! -d  "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/source" ]; then
		#mkdir -p "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/source"
	#fi

	src_sos="file_source/file_source.so dpdk_source/dpdk_source.so"

	for src_so in $src_sos
	do
		cp -f "${BASEDIR}../src/hw/gw_parser/source/$src_so" "${BASEDIR}./hw_vir/inst/source"
		#cp -f "${BASEDIR}../src/hw/gw_parser/source/$src_so" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/source"
		#cp -f "${BASEDIR}../src/hw/gw_parser/source/$src_so" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/source"
	done

	#网关upload模块
	if [ ! -d  "${BASEDIR}./hw_vir/inst/upload" ]; then
		mkdir -p "${BASEDIR}./hw_vir/inst/upload"
	fi

	#if [ ! -d  "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/upload" ]; then
		#mkdir -p "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/upload"
	#fi

	#if [ ! -d  "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/upload" ]; then
		#mkdir -p "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/upload"
	#fi

	upload_sos="log_upload/log_upload.so  kafka_upload/kafka_upload.so"

	for upload_so in $upload_sos
	do
		cp -f "${BASEDIR}../src/hw/gw_parser/upload/$upload_so" "${BASEDIR}./hw_vir/inst/upload"
		#cp -f "${BASEDIR}../src/hw/gw_parser/upload/$upload_so" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/upload"
		#cp -f "${BASEDIR}../src/hw/gw_parser/upload/$upload_so" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/upload"
	done


	# 监控网关状态可执行程序
	cp -f "${BASEDIR}../src/hw/watch_process/watch_process" "${BASEDIR}./hw_vir/inst/"
	#cp -f "${BASEDIR}../src/hw/watch_process/watch_process" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/hw/watch_process/watch_process" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"

	# 网关配置文件
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy.conf" "${BASEDIR}./hw_vir/inst/"gw_parser.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_5g.conf" "${BASEDIR}./hw_vir/inst/"gw_parser_5g.conf

	#cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy.conf" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"gw_parser.conf
	#cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_5g.conf" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"gw_parser_5g.conf

	#cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy.conf" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"gw_parser.conf
	#cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_5g.conf" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"gw_parser_5g.conf

	cp -f "${BASEDIR}../src/hw/gw_parser/forward_info_rule_deploy.conf" "${BASEDIR}./hw_vir/inst/"forward_info_rule.conf
	#cp -f "${BASEDIR}../src/hw/gw_parser/forward_info_rule_deploy.conf" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"forward_info_rule.conf
	#cp -f "${BASEDIR}../src/hw/gw_parser/forward_info_rule_deploy.conf" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"forward_info_rule.conf

	cp -f "${BASEDIR}../src/hw/gw_parser/user_info_rule_deploy.conf" "${BASEDIR}./hw_vir/inst/"user_info_rule.conf
	#cp -f "${BASEDIR}../src/hw/gw_parser/user_info_rule_deploy.conf" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"user_info_rule.conf
	#cp -f "${BASEDIR}../src/hw/gw_parser/user_info_rule_deploy.conf" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"user_info_rule.conf
	# supervisor服务配置文件
	cp -f "${BASEDIR}../src/hw/supervisord_hw.conf" "${BASEDIR}./hw_vir/inst/"
	#cp -f "${BASEDIR}../src/hw/supervisord_hw.conf" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/hw/supervisord_hw.conf" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"

	# 绑定网卡脚本文件
	cp -f "${BASEDIR}../src/hw/init_dpdk.sh" "${BASEDIR}./hw_vir/inst/"
	filter_sh_comment "${BASEDIR}./hw_vir/inst/init_dpdk.sh"
	# 解绑网卡脚本文件
	cp -f "${BASEDIR}../src/hw/dpdk_unbind.sh" "${BASEDIR}./hw_vir/inst/"
	filter_sh_comment "${BASEDIR}./hw_vir/inst/dpdk_unbind.sh"

	#cp -f "${BASEDIR}../src/hw/init_dpdk.sh" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#filter_sh_comment "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/init_dpdk.sh"
	#cp -f "${BASEDIR}../src/hw/init_dpdk.sh" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	#filter_sh_comment "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/init_dpdk.sh"

	# qat驱动初始化
	cp -f "${BASEDIR}../src/hw/init_qat.sh" "${BASEDIR}./hw_vir/inst/"
	filter_sh_comment "${BASEDIR}./hw_vir/inst/init_qat.sh"

	#cp -f "${BASEDIR}../src/hw/init_qat.sh" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#filter_sh_comment "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/init_qat.sh"

	#cp -f "${BASEDIR}../src/hw/init_qat.sh" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	#filter_sh_comment "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/init_qat.sh"

	cp -f "${BASEDIR}../src/hw/gwhw.service" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/tools/uninstall.sh" "${BASEDIR}./hw_vir/inst/"uninst_def_svr.sh
	filter_sh_comment "${BASEDIR}./hw_vir/inst/uninst_def_svr.sh"

	cp -f "${BASEDIR}../src/tools/file_monitor.sh" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/tools/burnintest.sh" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/tools/daemon.sh" "${BASEDIR}./hw_vir/inst/"


	cp -f "${BASEDIR}../src/tools/update_gwhw.sh" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/tools/rollback_gwhw.sh" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/tools/filter_sample.sh" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/inst/uninstall_gw.sh" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/tools/gw_sys_info.sh" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/tools/agent_upload_quota.sh" "${BASEDIR}./hw_vir/inst/"
	cp -f "${BASEDIR}../src/tools/troubleshooting_tool.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/web_uninst.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/renice_sshd.sh" "${BASEDIR}./hw/inst/"

	cp -f "${BASEDIR}../src/inst/kernel-3.10.0-693.17.1.el7.x86_64.rpm" "${BASEDIR}./hw_vir/inst/"

	#filter_sh_comment "${BASEDIR}./hw/inst/file_monitor.sh"
	#cp -f "${BASEDIR}../src/tools/file_monitor.sh" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/tools/burnintest.sh" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/tools/flowdetect.sh" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/tools/daemon.sh" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/inst/uninstall_gw.sh" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"

	#cp -f "${BASEDIR}../src/tools/file_monitor.sh" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/tools/burnintest.sh" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/tools/flowdetect.sh" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/tools/daemon.sh" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/inst/uninstall_gw.sh" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"


	#filter_sh_comment "${BASEDIR}./hw/gw_v1.0_to_v1.1_update/file_monitor.sh"
	#cp -f "${BASEDIR}../src/hw/update_gw_parser/v1.3.7_to_v1.10.0/gw_parser_update.sh" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/update.sh"
	#cp -f "${BASEDIR}../src/hw/update_gw_parser/v1.3.7_to_v1.10.0/parser_conf.py" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/hw/update_gw_parser/v1.3.7_to_v1.10.0/guide.conf" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/"

	#cp -f "${BASEDIR}../src/hw/update_gw_parser/v1.5.4_to_v1.10.0/gw_parser_update.sh" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/update.sh"
	#cp -f "${BASEDIR}../src/hw/update_gw_parser/v1.5.4_to_v1.10.0/parser_conf.py" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"
	#cp -f "${BASEDIR}../src/hw/update_gw_parser/v1.5.4_to_v1.10.0/guide.conf" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/"

	# 去除文件中注释
	cp -f "${BASEDIR}./../src/inst/inst_hw.sh" "${BASEDIR}./hw_vir/inst/inst.sh"
	filter_sh_comment "${BASEDIR}./hw_vir/inst/inst.sh"
	cp -f "${BASEDIR}./../src/inst/readme_hw.txt" "${BASEDIR}./hw_vir/inst/readme.txt"
	#cp -f "${BASEDIR}./../src/inst/readme_hw.txt" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/readme.txt"
	#cp -f "${BASEDIR}./../src/inst/readme_hw.txt" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/readme.txt"
	cp -f "${BASEDIR}./../src/inst/Changelog_hw" "${BASEDIR}./hw_vir/inst/Changelog"
	#cp -f "${BASEDIR}./../src/inst/Changelog_hw" "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update/Changelog"
	#cp -f "${BASEDIR}./../src/inst/Changelog_hw" "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update/Changelog"

	cd "${BASEDIR}./hw_vir/"
	#find "${BASEDIR}./hw/inst/" -type f -name .DS_Store  -exec rm -f {} \;
	# version info
	#zip -r inst.zip inst/  -x .DS_Store -x \*/.DS_Store
	zip -r inst_vir-${VER}-${PRODUCT_ID}-$(date -u '+%Y%m%dT%H%M%SZ').zip inst/  -x .DS_Store -x \*/.DS_Store

	#cd "gw_v1.3.7_to_1.10.0_update"
	#tar -zcvf gw_v1.3.7_to_1.10.0_update-${VER}-$(date -u '+%Y%m%dT%H%M%SZ').tar.gz gw_v1.3.7_to_1.10.0_update/

	#cd "../gw_v1.5.4_to_1.10.0_update"
	#tar -zcvf gw_v1.5.4_to_1.10.0_update-${VER}-$(date -u '+%Y%m%dT%H%M%SZ').tar.gz gw_v1.5.4_to_1.10.0_update/
	cd "${BLD_DIR}"
}



function build_cp()
{
	if [[ -f "$1" ]]; then
		echo copy "$1" "$2"
		cp -f "$1" "$2"
	fi
}

function build_cp_new()
{
	echo copy "$1" "$2"
	cp -f $1 "$2"
}

function build_out()
{
	mkdir -p "${BASEDIR}./out"

	# build_cp "${BASEDIR}./hw/inst.zip" "${BASEDIR}./out/hw_inst.zip"
	# time and version info
	if [[ `ls "${BASEDIR}./hw_vir/inst_vir"*.zip 2>/dev/null | wc -l` -eq 1 ]]; then
		build_cp "${BASEDIR}./hw_vir/inst_vir"*.zip "${BASEDIR}./out/hw_vir_inst.zip"
		build_cp_new "${BASEDIR}./hw_vir/inst_vir"*.zip "${BASEDIR}./out/"
		rename './out/inst' './out/hw_inst' "${BASEDIR}./out/inst_vir"*.zip
	fi

	# 最新版本文件
	if [[ -f "${BASEDIR}./out/hw_inst.zip" ]]; then
		touch "${BASEDIR}./out/hw_inst.zip"
	fi

	#if [[ `ls "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update"*.tar.gz 2>/dev/null | wc -l` -eq 1 ]]; then
		#build_cp "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update"*.tar.gz "${BASEDIR}./out/gw_v1.3.7_to_1.10.0_update.tar.gz"
		#build_cp_new "${BASEDIR}./hw/gw_v1.3.7_to_1.10.0_update"*.tar.gz "${BASEDIR}./out/"
		#rename './out/inst' './out/hw_inst' "${BASEDIR}./out/inst"*.zip
	#fi

	# 最新版本文件
	#if [[ -f "${BASEDIR}./out/gw_v1.3.7_to_1.10.0_update.tar.gz" ]]; then
		#touch "${BASEDIR}./out/gw_v1.3.7_to_1.10.0_update.tar.gz"
	#fi

	#if [[ `ls "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update"*.tar.gz 2>/dev/null | wc -l` -eq 1 ]]; then
		#build_cp "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update"*.tar.gz "${BASEDIR}./out/gw_v1.5.4_to_1.10.0_update.tar.gz"
		#build_cp_new "${BASEDIR}./hw/gw_v1.5.4_to_1.10.0_update"*.tar.gz "${BASEDIR}./out/"
		#rename './out/inst' './out/hw_inst' "${BASEDIR}./out/inst"*.zip
	#fi

	# 最新版本文件
	#if [[ -f "${BASEDIR}./out/gw_v1.5.4_to_1.10.0_update.tar.gz" ]]; then
		#touch "${BASEDIR}./out/gw_v1.5.4_to_1.10.0_update.tar.gz"
	#fi
	return
}

function main()
{
	echo build begin $(date "+%F %T")
	echo "product id  $PRODUCT_ID "
	func_log  build_env

	func_log  build_hw

	func_log  build_out

	echo build end $(date "+%F %T")
	return
}

if [ $# -eq 0 ]; then
  BUILD_TYPE=hw
elif [ $# -eq 1 ];then
  if [ "$1" != "hw" -a "$1" != "pcap" ];then
	PRODUCT_ID=$1
	BUILD_TYPE=hw
  else
	BUILD_TYPE=$1
  fi
elif [ $# -ge 2 ];then
  if [ "$1" != "hw" -a "$1" != "pcap" ];then
    echo "Usage: $0 [hw|pcap]"
	echo "  [hw]    hardware version, with dpdk"
	echo "  [pcap] software version, without dpdk, for pcap"
 	exit 1
  else
    BUILD_TYPE=$1
    PRODUCT_ID=$2
  fi
fi

export BUILD_TYPE=$BUILD_TYPE

echo "product id $PRODUCT_ID"
main $PRODUCT_ID
