#!/bin/bash

# 用于上传文件

# 当前目录为仓库中的build目录 

# 加载容器环境变量和函数
source bb-env.sh


# 切换到产品目录
pushd out

NAS_TARGET_DIR="$DIND_NAS_ALIAS/gwhw/publish/"

ci_mc  mb --ignore-existing "$NAS_TARGET_DIR"

time ci_mc  cp -- hw_inst-*.zip "$NAS_TARGET_DIR"

ci_mc  ls "$NAS_TARGET_DIR"


for f in hw_inst-*.zip; do
    ci_mc --json share download --expire=120h  "${NAS_TARGET_DIR}$f"
    ci_gen_shorter_url  "$NAS_TARGET_DIR$f"
    echo "shorter-url: ${SHORTER_URL}"
done

