
# 本文件用于配置仓库的构建阶段，支持阶段内多种任务的依赖和并发管理

# 当前目录为仓库的build目录


.PHONY: prep build test sign deploy docker-push helm-push   default


MAKE_DEFAULT_EXIT_CODE=1


#BASH_ARGS=-euxo pipefail
BASH_ARGS=-euo pipefail


default:
# 	@exit ${MAKE_DEFAULT_EXIT_CODE}
	@echo default

prep:
	@test -f bb-$@.sh && bash ${BASH_ARGS} bb-$@.sh

# 编译组件
build:
	@test -f bb-$@.sh && bash ${BASH_ARGS} bb-$@.sh

upload:
	@test -f bb-$@.sh && bash ${BASH_ARGS} bb-$@.sh

test:
	@test -f bb-$@.sh && bash ${BASH_ARGS} bb-$@.sh
