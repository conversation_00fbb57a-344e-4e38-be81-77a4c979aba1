#!/bin/sh

BASEDIR=$(dirname $0)/
if [[ "${BASEDIR}" = "/" ]]; then
	echo "basedir error"
	exit 0
fi

# 函数调用日志
function func_log()
{
	func=$1
	shift
	echo Enter ${func} ...
	${func} $*
	echo Leave ${func} .
	echo
}

function filter_sh_comment()
{
	sed -i -e 's/^[ \t]*#$//g' -e 's/^[ \t]*#[^!*].*$//g'  $@
}

function build_version()
{
  GW_VER_FILE="../src/hw/gw_parser/include/gw_ver.h"
  GW_VER_MAJOR=`grep "GW_VER_MAJOR" ${GW_VER_FILE} | awk -F'[ ]' '{print $3}'`
  GW_VER_MINOR=`grep "GW_VER_MINOR" ${GW_VER_FILE} | awk -F'[ ]' '{print $3}'`
  GW_VER_REVISION=`grep "GW_VER_REVISION" ${GW_VER_FILE} | awk -F'[ ]' '{print $3}'`
  GW_VER=${GW_VER_MAJOR}.${GW_VER_MINOR}.${GW_VER_REVISION}
  echo "${GW_VER}" > ${BASEDIR}./hw/inst/version.txt
}

function build_cp()
{
	if [[ -f "$1" ]]; then
		echo copy "$1" "$2"
		cp -f "$1" "$2"
	fi
}

function build_cp_new()
{
	echo copy "$1" "$2"
	cp -f $1 "$2"
}

function build_out()
{
	mkdir -p "${BASEDIR}./out"

	# build_cp "${BASEDIR}./hw/inst.zip" "${BASEDIR}./out/hw_inst.zip"
	# time and version info
	if [[ `ls "${BASEDIR}./hw/inst"*.zip 2>/dev/null | wc -l` -eq 1 ]]; then
		build_cp "${BASEDIR}./hw/inst"*.zip "${BASEDIR}./out/hw_inst.zip"
		build_cp_new "${BASEDIR}./hw/inst"*.zip "${BASEDIR}./out/"
		rename './out/inst' './out/hw_inst' "${BASEDIR}./out/inst"*.zip
	fi

	# 最新版本文件
	if [[ -f "${BASEDIR}./out/hw_inst.zip" ]]; then
		touch "${BASEDIR}./out/hw_inst.zip"
	fi
	return
}
