#!/bin/sh
source ./build_function.sh

#
# 在CentOS7运行环境中，构建安装包


CENTOS_RPM_SOURCE="https://mirrors.aliyun.com/centos"
EPEL_RPM_SOURCE="https://mirrors.aliyun.com/epel"

PIP_ARGS="-i  https://mirrors.aliyun.com/pypi/simple/"
if [[ x"$PREP_BUILD_ROOT" == x"" ]];then
	PREP_BUILD_ROOT=/home/<USER>/src
	export PREP_BUILD_ROOT=/home/<USER>/src
fi

#function compile_py()
#{
#	python -m py_compile $@
#}

# 检查下载的文件
#function build_chk_soft()
#{
#	arg=$4
#	url=$3
#	filepath=$2
#	md5=$1
#	ua="Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.63 Safari/537.36"
#	if [[ "${url}" = "" ]]; then
#		return
#	fi
#
#	# download file
#	if [[ ! -f "${filepath}" ]]; then
#		echo "download file ${filepath}"
#		#curl --user-agent "${UA}"  -o "${filepath}"  "${url}"
#		# for 3xx
#		wget --user-agent "${ua}" -O "${filepath}"  "${url}" "$arg"
#	fi
#
#	#check md5
#	if [[ `(md5sum "${filepath}" 2> /dev/null || md5 "${filepath}"  2> /dev/null) | grep ${md5} | wc -l` -ne 1 ]]; then
#		echo "file md5 error  ${filepath}"
#		md5sum ${filepath}
#		rm -f ${filepath}
#		exit 11
#	fi
#
#}

function build_copy_soft()
{
	file_name=$1
	type=$2
	cp "/home/<USER>/${type}/${file_name}" "${BASEDIR}./soft/${file_name}"
}

# 编译环境准备
function build_env()
{
	if [[ ! -d "${BASEDIR}./soft/" ]]; then
		mkdir -p "${BASEDIR}./soft/"
	fi

}

function build_hw()
{
	VER=$(cat "${BASEDIR}./../src/inst/version_hw.txt" | head -n1)
	VER_FULL=$(cat "${BASEDIR}./../src/inst/version_hw.txt" | head -n2 | tail -n1)
	if [[ "${VER}" == "" ]]; then
		echo hw invalid version info
		return
	fi
	# 增加GIT版本号
 	GIT_VER_REVISION=
	GIT_VER_SHA_FULL=`git rev-parse HEAD`
	GIT_VER_SHA_SHORT=`git rev-parse --short HEAD`
	if [[ ! "${GIT_VER_REVISION}" == "" ]]; then
		VER=${VER}.${GIT_VER_REVISION}
	fi
	if [[ ! "${GIT_VER_SHA_SHORT}" == "" ]]; then
		VER=${VER}-${GIT_VER_SHA_SHORT}
	fi

	rm -rf "${BASEDIR}./hw/"

	mkdir -p "${BASEDIR}./hw/tmp/"
	mkdir -p "${BASEDIR}./hw/inst/"
    mkdir -p "${BASEDIR}./hw/inst/stats/"
	mkdir -p "${BASEDIR}./hw/inst/lib/"
	mkdir -p "${BASEDIR}./hw/inst/gw_operation/"

  	build_version

	#获取产品序列号
	if [[ -z ${PRODUCT_ID} ]];then
	    PRODUCT_ID=`cat "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h" | grep GW_PRODUCT_SERIAL_ID | awk '{print $3}' | awk -F '["]' '{print $2}'`
	else
		sed -i "s,GW_PRODUCT_SERIAL_ID.*,GW_PRODUCT_SERIAL_ID \"${PRODUCT_ID}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"
	fi

	# 替换GIT版本号
	#sed -i  "s,GW_VER_REVISION.*,GW_VER_REVISION ${GIT_VER_REVISION}," "${BASEDIR}../src/hw/gw_parser/gw_ver.h"
	sed -i  "s,GW_GIT_VER_SHA.*,GW_GIT_VER_SHA \"${GIT_VER_SHA_FULL}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"
	sed -i  "s,GW_GIT_VER_SHORT_SHA.*,GW_GIT_VER_SHORT_SHA \"${GIT_VER_SHA_SHORT}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"

	BLD_DIR=`pwd`
	cd "${BLD_DIR}"
	# 编译libgpg-error
	if [[ ! -d /opt/gcrypt/libgpg-error/ ]];then
		tar -jxf "${BASEDIR}./soft/libgpg-error-1.36.tar.bz2"
		cd libgpg-error-1.36
		./configure --prefix=/opt/gcrypt/libgpg-error
		make -j4
		sudo make install
		cd "${BLD_DIR}"
	fi

	# 编译libgcrypt
	if [[ ! -d /opt/gcrypt/libgcrypt/ ]];then
		tar -jxf  "${BASEDIR}./soft/libgcrypt-1.8.5.tar.bz2"
		cd libgcrypt-1.8.5
		./configure --prefix=/opt/gcrypt/libgcrypt --with-libgpg-error-prefix=/opt/gcrypt/libgpg-error
		make -j4
		sudo make install
		cd "${BLD_DIR}"
	fi

	echo "BLD_DIR=${BLD_DIR}"
	# 编译 gw_parser主程序
	cd "../src/hw/gw_parser/"
	make clean;
	#BUILD_SCHEME=Release CPLUS_INCLUDE_PATH=/opt/Python3/include/python3.7m LIBRARY_PATH=/opt/Python3/lib  make -j4
	BUILD_SCHEME=Release_x86 ARCH_LIB=/home/<USER>
	if [[ ! -f "./gw_parser" ]]; then
		echo "gw_parser compile error!";
		return
	fi

	#编译 watch_process程序
	#cd "../watch_process"
	#make clean;make -j4
	#if [[ ! -f "./watch_process" ]]; then
		#echo "watch process compile error!";
		#return
	#fi

	cd "${BLD_DIR}"

    #编译 stats python 服务  src/hw/gw_stats_srv
	# cd "${BASEDIR}../src/hw/gw_stats_srv/"
	# pyinstaller --clean -F --runtime-tmpdir /opt/apigw/gwhw/stats_srv/tmpdir/ -p /usr/lib/python2.7/site-packages/  --add-data app/:app/ --add-data ./:./ gw_stat_srv.py
	# cd -

	# cp -f "${BASEDIR}../src/hw/gw_stats_srv/dist/gw_stat_srv" "${BASEDIR}./hw/inst/stats/"
	# cp -f "${BASEDIR}../src/hw/gw_stats_srv/"*.conf "${BASEDIR}./hw/inst/stats/"
	# cp -f "${BASEDIR}../src/hw/gw_stats_srv/"*.sh "${BASEDIR}./hw/inst/stats/"

	# 编译 stats oatpp 服务 src/hw/gw_stats_srv_oatpp
    cd "${BASEDIR}../src/hw/gw_stats_srv_oatpp/"
	if [ -d "build" ]; then
		rm -rf "build"
	fi
	mkdir "build";cd build
	#cmake .. -D CMAKE_BUILD_TYPE=Release
	cmake .. -D CMAKE_BUILD_TYPE=Release -DBUILD_ARCH=x86 -DCMAKE_C_COMPILER=gcc -DCMAKE_CXX_COMPILER=g++
	make -j4
	cd "${BLD_DIR}"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv_oatpp/build/gw_stats_srv-exe" "${BASEDIR}./hw/inst/stats/"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv_oatpp/"*.conf "${BASEDIR}./hw/inst/stats/"

	# openssl
	OPENSSL_TAR_GZ="`pwd`/hw/inst/openssl-bin-1.1.1w.zip"
	cd /opt/openssl/
	zip -qr "${OPENSSL_TAR_GZ}" *
	cd -

	cp -rf "/home/<USER>/dpdk/kmod/xinchuang/" "${BASEDIR}./hw/inst/kmod/xinchuang/"
 	# 安装时SO库需要进行符号连接
	cp -f "/home/<USER>/dpdk/lib/"* "${BASEDIR}./hw/inst/lib/"
	# dpdk动态库

	#pf_ring动态库
	cp -f "/home/<USER>/pfring/kmod/"*.ko "${BASEDIR}./hw/inst/kmod/"
	cp -f "/home/<USER>/pfring/lib/libpfring.so" "${BASEDIR}./hw/inst/lib/"
	cp -f "/home/<USER>/pfring/lib/libpcap.so.1.9.1" "${BASEDIR}./hw/inst/lib/"

	cp -f "/home/<USER>/lib/"* "${BASEDIR}./hw/inst/lib"

	# 授权动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/liblicutils_c_sdk/liblicutils.so" "${BASEDIR}./hw/inst/"

	#文件格式解析动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/utils/file_type/libfile_type.so" "${BASEDIR}./hw/inst/"

	#nacos_c++动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/nacos_c++/lib/libnacos-cli.so" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/gw_parser/nacos_c++/lib/libcurl.so" "${BASEDIR}./hw/inst/"

	#libaws-api-c++动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/libaws_api_c++/lib64/"libaws-cpp-sdk*.so "${BASEDIR}./hw/inst"

	cp -f "/opt/gcrypt/libgpg-error/lib/"libgpg-error.so.0.27.0 "${BASEDIR}./hw/inst/lib"
	cp -f "/opt/gcrypt/libgcrypt/lib/"libgcrypt.so.20.2.5 "${BASEDIR}./hw/inst/lib"

	#kafka动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/kafka/librdkafka.so" "${BASEDIR}./hw/inst"

    # libmagic
    cp -f "${BASEDIR}../src/hw/gw_parser/libmagic/lib/"libmagic.so.1 "${BASEDIR}./hw/inst/lib"
	# protobuf动态库
	cp -f /usr/local/lib/libprotobuf.so.31.0.1 "${BASEDIR}./hw/inst/lib"

	# brotli动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/parser/http2_parser/brotli/libbrotli.so" "${BASEDIR}./hw/inst"

	# atomic动态库
	cp -f "${BASEDIR}../src/hw/gw_stats_srv_oatpp/lib/libatomic.so.1" "${BASEDIR}./hw/inst/lib"

	# 网关主程序
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser" "${BASEDIR}./hw/inst/"

	#网关parser模块
	if [ ! -d  "${BASEDIR}./hw/inst/parser" ]; then
		mkdir -p "${BASEDIR}./hw/inst/parser"
	fi

	parser_sos="ftp_parser/ftp_parser.so hbase_parser/hbase_parser.so hdfs_parser/hdfs_parser.so hive_parser/hive_parser.so http_parser/http_parser.so http2_parser/http2_parser.so grpc_parser/grpc_parser.so mongo_parser/mongo_parser.so ssl_parser/ssl_parser.so oracle_parser/oracle_parser.so mail_parser/mail_parser.so smb_parser/smb_parser.so nfs_parser/nfs_parser.so"
	for parser_so in $parser_sos
	do
    	cp -f "${BASEDIR}../src/hw/gw_parser/parser/$parser_so" "${BASEDIR}./hw/inst/parser"
	done


	#网关source模块
	if [ ! -d  "${BASEDIR}./hw/inst/source" ]; then
		mkdir -p "${BASEDIR}./hw/inst/source"
	fi

	src_sos="file_source/file_source.so dpdk_source/dpdk_source.so nic_source/nic_source.so pcap_source/pcap_source.so"

	for src_so in $src_sos
	do
		cp -f "${BASEDIR}../src/hw/gw_parser/source/$src_so" "${BASEDIR}./hw/inst/source"
	done

	#网关upload模块
	if [ ! -d  "${BASEDIR}./hw/inst/upload" ]; then
		mkdir -p "${BASEDIR}./hw/inst/upload"
	fi

	upload_sos="log_upload/log_upload.so  kafka_upload/kafka_upload.so diy_upload/diy_upload.so"

	for upload_so in $upload_sos
	do
		cp -f "${BASEDIR}../src/hw/gw_parser/upload/$upload_so" "${BASEDIR}./hw/inst/upload"
	done


	# 监控网关状态可执行程序
	#cp -f "${BASEDIR}../src/hw/watch_process/watch_process" "${BASEDIR}./hw/inst/"

	# 网关配置文件
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy.conf" "${BASEDIR}./hw/inst/"gw_parser.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api_1.4.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api_1.4.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api_2.0.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api_2.0.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api_2.4.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api_2.4.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api_3.0.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api_3.0.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_api_3.1.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_api_3.1.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_audit.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_audit.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_audit_2.2.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_audit_2.2.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_audit_2.7.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_audit_2.7.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_tools_task.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_tools_task.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_tools.conf" "${BASEDIR}./hw/inst/"gw_parser_deploy_tools.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_5g.conf" "${BASEDIR}./hw/inst/"gw_parser_5g.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_tools.conf" "${BASEDIR}./hw/inst/"gw_parser_tools.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/forward_info_rule_deploy.conf" "${BASEDIR}./hw/inst/"forward_info_rule.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/user_info_rule_deploy.conf" "${BASEDIR}./hw/inst/"user_info_rule.conf
	# supervisor服务配置文件
	cp -f "${BASEDIR}../src/hw/supervisord" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/supervisord_hw.conf" "${BASEDIR}./hw/inst/"
	# 绑定网卡脚本文件
	cp -f "${BASEDIR}../src/hw/init_dpdk.sh" "${BASEDIR}./hw/inst/"
	filter_sh_comment "${BASEDIR}./hw/inst/init_dpdk.sh"
	# 解绑网卡脚本文件
	cp -f "${BASEDIR}../src/hw/dpdk_unbind.sh" "${BASEDIR}./hw/inst/"
	filter_sh_comment "${BASEDIR}./hw/inst/dpdk_unbind.sh"


	# qat驱动初始化
	cp -f "${BASEDIR}../src/hw/init_qat.sh" "${BASEDIR}./hw/inst/"
	filter_sh_comment "${BASEDIR}./hw/inst/init_qat.sh"

	cp -f "${BASEDIR}../src/hw/gwhw.service" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/rc-local.service" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_black_list.txt" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_white_list.txt" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_black_list_tools.txt" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_alloc_GB" "${BASEDIR}./hw/inst"
	cp -f "${BASEDIR}../src/tools/uninstall.sh" "${BASEDIR}./hw/inst/"uninst_def_svr.sh
	filter_sh_comment "${BASEDIR}./hw/inst/uninst_def_svr.sh"

	cp -f "${BASEDIR}../src/tools/file_monitor.sh" "${BASEDIR}./hw/inst/"

	cp -f "${BASEDIR}../src/tools/update_gwhw.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/telnet" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/tcpreplay" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/rollback_gwhw.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/filter_sample.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/inst/uninstall_gw.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/gw_sys_info.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/agent_upload_quota.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/load_driver.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/set_irq_affinity" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/data_analyze.py" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/troubleshooting_tool.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/web_uninst.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/renice_sshd.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/licutils_demo" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/fuse.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/check.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/bind_eth.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/checklist.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/dpdk-devbind_x86" "${BASEDIR}./hw/inst/dpdk-devbind"
	cp -f "${BASEDIR}../src/tools/status_check.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/watch_dog.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/telnet" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/tcpreplay" "${BASEDIR}./hw/inst/"

	# 拷贝url过滤库文件
	cp -f "${BASEDIR}../src/hw/url_filter_base.file" "${BASEDIR}./hw/inst/"

	# 去除文件中注释
	cp -f "${BASEDIR}./../src/inst/inst_hw.sh" "${BASEDIR}./hw/inst/inst.sh"
	filter_sh_comment "${BASEDIR}./hw/inst/inst.sh"
	cp -f "${BASEDIR}./../src/inst/inst_function.sh" "${BASEDIR}./hw/inst/"
	filter_sh_comment "${BASEDIR}./hw/inst/inst_function.sh"
	cp -f "${BASEDIR}./../src/inst/inst_node_exporter.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./../src/inst/readme_hw.txt" "${BASEDIR}./hw/inst/readme.txt"
	cp -f "${BASEDIR}./../src/inst/Changelog_hw" "${BASEDIR}./hw/inst/Changelog"
	cp -f "${BASEDIR}./../src/inst/inst_web" "${BASEDIR}./hw/inst/inst_web"
	cp -f "${BASEDIR}./../src/inst/inst-config.yaml" "${BASEDIR}./hw/inst/inst-config.yaml"
	cp -rf "${BASEDIR}./../src/inst/dist" "${BASEDIR}./hw/inst/dist"
	chmod +x "${BASEDIR}./hw/inst/inst_web"

    cp "${BASEDIR}./../src/hw/gw_parser/libmagic/mgc/magic.mgc" "${BASEDIR}./hw/inst/magic.mgc"

	wget https://installq.oss-cn-shanghai.aliyuncs.com/agent-v10/qzkj_agent_server-v1.4.0_20250515144656.zip -O "${BASEDIR}./hw/inst/qzkj_agent_server.zip"

	cd "${BASEDIR}./hw/"

	zip -r inst-${VER}-${PRODUCT_ID}-$(date -u '+%Y%m%dT%H%M%SZ').zip inst/  -x .DS_Store -x \*/.DS_Store
	cd "${BLD_DIR}"
}

function main()
{
	echo build begin $(date "+%F %T")
	echo "product id  $PRODUCT_ID "
	func_log  build_env

	func_log  build_hw

	func_log  build_out

	echo build end $(date "+%F %T")
	return
}

if [ $# -eq 0 ]; then
  BUILD_TYPE=hw
elif [ $# -eq 1 ];then
  if [ "$1" != "hw" -a "$1" != "pcap" ];then
	PRODUCT_ID=$1
	BUILD_TYPE=hw
  else
	BUILD_TYPE=$1
  fi
elif [ $# -ge 2 ];then
  if [ "$1" != "hw" -a "$1" != "pcap" ];then
    echo "Usage: $0 [hw|pcap]"
	echo "  [hw]    hardware version, with dpdk"
	echo "  [pcap] software version, without dpdk, for pcap"
 	exit 1
  else
    BUILD_TYPE=$1
    PRODUCT_ID=$2
  fi
fi

export BUILD_TYPE=$BUILD_TYPE

echo "product id $PRODUCT_ID"
main $PRODUCT_ID
