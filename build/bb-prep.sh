#!/bin/bash

# 用于构建组件

# 当前目录为仓库中的prep目录 

# 加载容器环境变量和函数
source bb-env.sh

# 切换到仓库所在的根目录
# pushd ..

# 执行容器内的构建命令
#bb_mvn -U package
# bb_mvn_package -U

# for npm 
# bb_npm_install && bb_npm_run_build
SOFT_DIR=soft

ll $SOFT_DIR &>/dev/null || mkdir -p $SOFT_DIR
curl http://*************/isos/mirrors/soft/dpdk-17.08.1.tar.xz -o ${SOFT_DIR}/dpdk-17.08.1.tar.xz
curl http://*************/isos/mirrors/soft/zlib-1.2.8.tar.gz -o ${SOFT_DIR}/zlib-1.2.8.tar.gz
# curl http://*************/isos/mirrors/soft/get-pip.py -o ${SOFT_DIR}/get-pip.py
curl http://*************/isos/mirrors/soft/libgcrypt-1.8.5.tar.bz2 -o ${SOFT_DIR}/libgcrypt-1.8.5.tar.bz2
curl http://*************/isos/mirrors/soft/libgpg-error-1.36.tar.bz2 -o ${SOFT_DIR}/libgpg-error-1.36.tar.bz2
curl http://*************/isos/mirrors/soft/openssl-1.1.0i.tar.gz -o ${SOFT_DIR}/openssl-1.1.0i.tar.gz


# 使用内网pip源
# echo "[global]\nindex-url = http://************:8181/repository/aliyun-pypi/" > ~/.pip/pip.conf

# 检查环境变量

echo RTE_SDK=$RTE_SDK
echo PREP_BUILD_ROOT=$PREP_BUILD_ROOT
echo `ls /tmp`