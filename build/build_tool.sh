#!/bin/sh


#
# 在CentOS7运行环境中，构建安装包


BASEDIR=$(dirname $0)/
if [[ "${BASEDIR}" = "/" ]]; then
	echo "basedir error"
	exit 0
fi

CENTOS_RPM_SOURCE="https://mirrors.aliyun.com/centos"
EPEL_RPM_SOURCE="https://mirrors.aliyun.com/epel"


# 函数调用日志
function func_log()
{
	func=$1
	shift
	# ${func} $*
	# return
	echo Enter ${func} ...
	${func} $*
	echo Leave ${func} .
	echo
}


function compile_py()
{
	python -m py_compile $@
}


function filter_sh_comment()
{
	sed -i -e 's/^[ \t]*#$//g' -e 's/^[ \t]*#[^!*].*$//g'  $@
}

# 检查下载的文件
function build_chk_soft()
{
	url=$3
	filepath=$2
	md5=$1
	ua="Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/31.0.1650.63 Safari/537.36"
	if [[ "${url}" = "" ]]; then
		return
	fi

	# download file
	if [[ ! -f "${filepath}" ]]; then
		echo "download file ${filepath}"
		#curl --user-agent "${UA}"  -o "${filepath}"  "${url}"
		# for 3xx
		wget --user-agent "${UA}" -O "${filepath}"  "${url}"
	fi

	#check md5
	if [[ `(md5sum "${filepath}" 2> /dev/null || md5 "${filepath}"  2> /dev/null) | grep ${md5} | wc -l` -ne 1 ]]; then
		echo "file md5 error  ${filepath}"
		exit 11
	fi

}



# 编译环境准备
function build_env()
{
	# 编译环境检测
	if [[ `cat /etc/redhat-release 2> /dev/null | grep CentOS | wc -l` -eq 0 ]]; then
		echo 'Error: Must in Centos 7'
		exit 10
	fi

	if [[ ! -d "${BASEDIR}./soft/" ]]; then
		mkdir -p "${BASEDIR}./soft/"
	fi

	# 	faea23aa2899508b1738999d6f12502c
	build_chk_soft faea23aa2899508b1738999d6f12502c "${BASEDIR}./soft/dpdk-17.08.1.tar.xz"  "https://fast.dpdk.org/rel/dpdk-17.08.1.tar.xz"

	build_chk_soft 1ab6eaa57f86f2bacddfee44601d7c65 "${BASEDIR}./soft/lz4-1.7.5-3.el7.x86_64.rpm"  "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/lz4-1.7.5-3.el7.x86_64.rpm"

	# 	96324aa9e7b41bbd50ec002251716bc2
	build_chk_soft 1420b52c50892f85b4c4fa53dcdeb67b  "${BASEDIR}./soft/librdkafka-0.11.5-1.el7.x86_64.rpm"  "${EPEL_RPM_SOURCE}/7/x86_64/Packages/l/librdkafka-0.11.5-1.el7.x86_64.rpm"

	build_chk_soft d4544796f239918d703e380517519c5b "${BASEDIR}./soft/gperftools-libs-2.6.1-1.el7.x86_64.rpm" "ftp://ftp.icm.edu.pl/vol/rzm3/linux-scientificlinux/7.5/x86_64/os/Packages/gperftools-libs-2.6.1-1.el7.x86_64.rpm"

	build_chk_soft  3f4cccfac46a575628c190ee30fbc36f "${BASEDIR}./soft/numactl-2.0.12-5.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/numactl-2.0.12-5.el7.x86_64.rpm"
	build_chk_soft  996c865ba30c93e196f6a9a6d58d7584 "${BASEDIR}./soft/numactl-libs-2.0.12-5.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/numactl-libs-2.0.12-5.el7.x86_64.rpm"

	#libpcap
	build_chk_soft c2b8be2980d377b0dd5b9bb826e2edfa "${BASEDIR}./soft/libpcap-1.5.3-12.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/libpcap-1.5.3-12.el7.x86_64.rpm"

	# jq
	build_chk_soft afd8f299fcb36ba08635aa9808b78b5c  "${BASEDIR}./soft/jq-1.6-1.el7.x86_64.rpm"  "${EPEL_RPM_SOURCE}/7/x86_64/Packages/j/jq-1.6-1.el7.x86_64.rpm"
	build_chk_soft 11d8d7a200da4e50a770dc8266ae9804  "${BASEDIR}./soft/oniguruma-5.9.5-3.el7.x86_64.rpm"  "${EPEL_RPM_SOURCE}/7/x86_64/Packages/o/oniguruma-5.9.5-3.el7.x86_64.rpm"

	#gdb
	build_chk_soft 9260893fb4cacabbf405dfcb47cd3cf5 "${BASEDIR}./soft/gdb-7.6.1-119.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/gdb-7.6.1-119.el7.x86_64.rpm"

	#libnet
	build_chk_soft 213e50e61a94c7eaa4df3a66437dba3e "${BASEDIR}./soft/libnet-1.1.6-7.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/libnet-1.1.6-7.el7.x86_64.rpm"

	#pciutils
	build_chk_soft 6e84072172e4c3635ef4296dbf11c54a "${BASEDIR}./soft/pciutils-libs-3.5.1-3.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/pciutils-libs-3.5.1-3.el7.x86_64.rpm"
	build_chk_soft 6f788fa4438fb4e0087799dee70cc8e7 "${BASEDIR}./soft/pciutils-3.5.1-3.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/pciutils-3.5.1-3.el7.x86_64.rpm"

	#python_devel
	build_chk_soft 7e9dadaac62738df577ba3ed5773781d "${BASEDIR}./soft/python-devel-2.7.5-88.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/python-devel-2.7.5-88.el7.x86_64.rpm"
	build_chk_soft 10cf15dff22d78451d97fdb72b2a5a79 "${BASEDIR}./soft/python-rpm-macros-3-32.el7.noarch.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/python-rpm-macros-3-32.el7.noarch.rpm"
	build_chk_soft b87170544b1ff7ee265f1399937b1412 "${BASEDIR}./soft/python-srpm-macros-3-32.el7.noarch.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/python-srpm-macros-3-32.el7.noarch.rpm"
	build_chk_soft bfcf4df1d6fc05e2a34d200d28a8e483 "${BASEDIR}./soft/python2-rpm-macros-3-32.el7.noarch.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/python2-rpm-macros-3-32.el7.noarch.rpm"
	build_chk_soft 52b67ab9e1e0c022d16454cb5d2daed6 "${BASEDIR}./soft/python-2.7.5-88.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/python-2.7.5-88.el7.x86_64.rpm"
	build_chk_soft 77f65ed361550ad6c97125d75a473152 "${BASEDIR}./soft/python-libs-2.7.5-88.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/python-libs-2.7.5-88.el7.x86_64.rpm"

	#boost-chrono
	build_chk_soft 43db9028b64f1d5e7c4bdc5f07f3a54c "${BASEDIR}./soft/boost-chrono-1.53.0-28.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/boost-chrono-1.53.0-28.el7.x86_64.rpm"
	build_chk_soft ca13962e16aad77fda62b7650f35c5a0 "${BASEDIR}./soft/boost-system-1.53.0-28.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/boost-system-1.53.0-28.el7.x86_64.rpm"

	#libicu
	build_chk_soft 5e9ba80a3ce145bee8834b5abfceb6e4 "${BASEDIR}./soft/libicu-50.2-3.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/libicu-50.2-3.el7.x86_64.rpm"

	#boost-locale
	build_chk_soft b91efc2b438c22b4632b89608b3cf5be "${BASEDIR}./soft/boost-locale-1.53.0-28.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/boost-locale-1.53.0-28.el7.x86_64.rpm"
	build_chk_soft f9b0377bac7801f3d26c133f52f0b1d7 "${BASEDIR}./soft/boost-thread-1.53.0-28.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/boost-thread-1.53.0-28.el7.x86_64.rpm"

	#tcpdump
	build_chk_soft c09490974f9842bc6f72dc0e79ca6963 "${BASEDIR}./soft/tcpdump-4.9.2-4.el7_7.1.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/tcpdump-4.9.2-4.el7_7.1.x86_64.rpm"

	#telnet
	build_chk_soft 0715fe21f3769567973015c3a277b59a "${BASEDIR}./soft/telnet-0.17-64.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/telnet-0.17-64.el7.x86_64.rpm"

	#psmisc
	build_chk_soft 70ff5f4593cfb3feff121dc4321510dd "${BASEDIR}./soft/psmisc-22.20-16.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/psmisc-22.20-16.el7.x86_64.rpm"

	#htop
	build_chk_soft ba786f23353fd7c22cdb6fa69b476542 "${BASEDIR}./soft/htop-2.2.0-3.el7.x86_64.rpm" "${EPEL_RPM_SOURCE}/7/x86_64/Packages/h/htop-2.2.0-3.el7.x86_64.rpm"

	#vsftp
	build_chk_soft 60b728727efc1fcc39f84ff32f9c0616 "${BASEDIR}./soft/vsftpd-3.0.2-27.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/vsftpd-3.0.2-27.el7.x86_64.rpm"

	#iconv
	build_chk_soft 5035be45b0b9b0b8fcb62f04939bb265 "${BASEDIR}./soft/libiconv-1.15-1.el7.x86_64.rpm" "https://forensics.cert.org/centos/cert/7/x86_64//libiconv-1.15-1.el7.x86_64.rpm"

	#gcc
	build_chk_soft 62d5fcff49a6efdc10c2fae57c4209b1 "${BASEDIR}./soft/gcc-4.8.5-39.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/gcc-4.8.5-39.el7.x86_64.rpm"
	build_chk_soft bf43634586db8586de76fec3fd461896 "${BASEDIR}./soft/cpp-4.8.5-39.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/cpp-4.8.5-39.el7.x86_64.rpm"
	build_chk_soft d58c15cc1467436f6a28ff2d028f56e8 "${BASEDIR}./soft/glibc-devel-2.17-307.el7.1.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/glibc-devel-2.17-307.el7.1.x86_64.rpm"
	build_chk_soft 03dd81294c2b69ceca97d10cdc12883c "${BASEDIR}./soft/glibc-headers-2.17-307.el7.1.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/glibc-headers-2.17-307.el7.1.x86_64.rpm"
	build_chk_soft d30648dd8c6ca8b68c1d85254fcf012c "${BASEDIR}./soft/kernel-headers-3.10.0-1127.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/kernel-headers-3.10.0-1127.el7.x86_64.rpm"
	build_chk_soft d828a8cfe6b71d68b97dfdb377e18f0c "${BASEDIR}./soft/libmpc-1.0.1-3.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/libmpc-1.0.1-3.el7.x86_64.rpm"
	build_chk_soft fc9e39143a34eb3b7c2c0cedb7f62521 "${BASEDIR}./soft/mpfr-3.1.1-4.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/mpfr-3.1.1-4.el7.x86_64.rpm"
	build_chk_soft 362ddb43b1f72b5728da02a1ae069f34 "${BASEDIR}./soft/glibc-2.17-307.el7.1.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/glibc-2.17-307.el7.1.x86_64.rpm"
	#build_chk_soft 0578f8394dd2c33ce6d364512c988aed "${BASEDIR}./soft/glibc-2.17-307.el7.1.i686.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/glibc-2.17-307.el7.1.i686.rpm"
	build_chk_soft 911f8a79802d2628a7c8f0e5cc6d6b4b "${BASEDIR}./soft/glibc-common-2.17-307.el7.1.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/glibc-common-2.17-307.el7.1.x86_64.rpm"
	build_chk_soft 707afe937af4519d31a5955ac1d0dc48 "${BASEDIR}./soft/libgcc-4.8.5-39.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/libgcc-4.8.5-39.el7.x86_64.rpm"
	build_chk_soft 6e848180e1926fac0933bca23306229d "${BASEDIR}./soft/libgomp-4.8.5-39.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/libgomp-4.8.5-39.el7.x86_64.rpm"
	build_chk_soft 955561c54f9f7fc5c54fbf65a84b9f54 "${BASEDIR}./soft/unzip-6.0-21.el7.x86_64.rpm" "${CENTOS_RPM_SOURCE}/7/os/x86_64/Packages/unzip-6.0-21.el7.x86_64.rpm"
	build_chk_soft eff437f397e858a9127b76c0d87fa5ed "${BASEDIR}./soft/libgpg-error-1.36.tar.bz2" "https://www.gnupg.org/ftp/gcrypt/libgpg-error/libgpg-error-1.36.tar.bz2"
	build_chk_soft 348cc4601ca34307fc6cd6c945467743 "${BASEDIR}./soft/libgcrypt-1.8.5.tar.bz2" "https://www.gnupg.org/ftp/gcrypt/libgcrypt/libgcrypt-1.8.5.tar.bz2"

	if [[ ! -d /root/pfring-stable ]];then
		mkdir -p /root/pfring-stable
		git clone https://github.com/ntop/PF_RING.git  /root/pfring-stable/  -b  7.4.0-stable --single-branch
		if [[ $? -ne 0 ]];then
			rm -rf /root/pfring-stable
		fi
	fi

	if [[ ! -d /root/librdkafka ]];then
		mkdir -p /root/librdkafka
		git clone https://github.com/edenhill/librdkafka.git  /root/librdkafka/ -b v1.1.0 --single-branch
		if [[ $? -ne 0 ]];then
			rm -rf /root/librdkafka
		fi
	fi

	build_chk_soft 0eb3a38f91ca1231eaa00250ee0bdf78 "${BASEDIR}./soft/get-pip.py"  "https://bootstrap.pypa.io/get-pip.py"

	if [[ ! -d "${BASEDIR}./soft/pip_dp/" ]]; then
		mkdir -p "${BASEDIR}./soft/pip_dp/"
	fi

	#pip download -d "${BASEDIR}./soft/pip_dp/"  pip setuptools  wheel
	if [[ -f "${BASEDIR}./soft/pip_dp/pip-9.0.1-py2.py3-none-any.whl" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" --no-index --find-links="${BASEDIR}./soft/pip_dp/" pip==9.0.1 setuptools==36.6.0 wheel==0.30.0
	else
		pip download -d "${BASEDIR}./soft/pip_dp/" pip==9.0.1 setuptools==36.6.0 wheel==0.30.0
	fi
	# pip download -d "${BASEDIR}./soft/pip_dp/" supervisor
	if [[ -f "${BASEDIR}./soft/pip_dp/supervisor-3.3.3.tar.gz" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" --no-index --find-links="${BASEDIR}./soft/pip_dp/" supervisor==3.3.3 meld3==1.0.2
	else
		pip download -d "${BASEDIR}./soft/pip_dp/" supervisor==3.3.3 meld3==1.0.2
	fi

	# pip download -d "${BASEDIR}./soft/pip_dp/" flask requests flask-gzip Flask-GzipBomb httpbin
	if [[ -f "${BASEDIR}./soft/pip_dp/Flask-0.12.2-py2.py3-none-any.whl" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" --no-index --find-links="${BASEDIR}./soft/pip_dp/" blinker==1.4 brotlipy==0.7.0 certifi==2017.7.27.1 cffi==1.11.2 chardet==3.0.4 click==6.7 colorama==0.3.9 contextlib2==0.5.5 crayons==0.1.2 dateparser==0.6.0 decorator==4.1.2 enum34==1.1.6 Flask==0.12.2 Flask-Cache==0.13.1 Flask-Common==0.2.0 Flask-gzip==0.1 Flask-GzipBomb==0.1.0 Flask-Limiter==******* greenlet==0.4.12 gunicorn==19.7.1 httpbin==0.6.2 humanize==0.5.1 idna==2.6 itsdangerous==0.24 Jinja2==2.9.6 limits==1.2.1 MarkupSafe==1.0 maya==0.3.3 meinheld==0.6.1 pendulum==1.3.1 pycparser==2.18 python-dateutil==2.6.1 pytz==2017.3 pytzdata==2017.3 raven==6.3.0 regex==2017.9.23 requests==2.18.4 ruamel.ordereddict==0.4.13 ruamel.yaml==0.15.34 six==1.11.0 tzlocal==1.4 urllib3==1.22 Werkzeug==0.12.2 whitenoise==3.3.1
	else
		pip download -d "${BASEDIR}./soft/pip_dp/" blinker==1.4 brotlipy==0.7.0 certifi==2017.7.27.1 cffi==1.11.2 chardet==3.0.4 click==6.7 colorama==0.3.9 contextlib2==0.5.5 crayons==0.1.2 dateparser==0.6.0 decorator==4.1.2 enum34==1.1.6 Flask==0.12.2 Flask-Cache==0.13.1 Flask-Common==0.2.0 Flask-gzip==0.1 Flask-GzipBomb==0.1.0 Flask-Limiter==******* greenlet==0.4.12 gunicorn==19.7.1 httpbin==0.6.2 humanize==0.5.1 idna==2.6 itsdangerous==0.24 Jinja2==2.9.6 limits==1.2.1 MarkupSafe==1.0 maya==0.3.3 meinheld==0.6.1 pendulum==1.3.1 pycparser==2.18 python-dateutil==2.6.1 pytz==2017.3 pytzdata==2017.3 raven==6.3.0 regex==2017.9.23 requests==2.18.4 ruamel.ordereddict==0.4.13 ruamel.yaml==0.15.34 six==1.11.0 tzlocal==1.4 urllib3==1.22 Werkzeug==0.12.2 whitenoise==3.3.1
	fi

	# pip download -d "${BASEDIR}./soft/pip_dp/" Flask-HTTPAuth
	# Flask-HTTPAuth-3.2.3.tar.gz
	if [[ -f "${BASEDIR}./soft/pip_dp/Flask_HTTPAuth-3.2.3-py2.py3-none-any.whl" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" --no-index --find-links="${BASEDIR}./soft/pip_dp/" Flask-HTTPAuth==3.2.3
	else
		pip download -d "${BASEDIR}./soft/pip_dp/" Flask-HTTPAuth==3.2.3
	fi

	if [[ ! -f "${BASEDIR}./soft/pip_dp/scapy-2.4.3.tar.gz" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" scapy==2.4.3
	fi

	if [[ ! -f "${BASEDIR}./soft/pip_dp/psutil-5.7.2.tar.gz" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/" psutil==5.7.2
	fi

	if [[ ! -f "${BASEDIR}./soft/pip_dp/prometheus_client-0.8.0-py2.py3-none-any.whl" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp" prometheus_client==0.8.0
	fi

	if [[ ! -f "${BASEDIR}./soft/pip_dp/nacos-sdk-python-0.1.1.tar.gz" ]]; then
		pip download -d "${BASEDIR}./soft/pip_dp/"   nacos-sdk-python==0.1.1
	fi

	build_chk_soft 9495126aafd2659d357ea66a969c3fe1 "${BASEDIR}./soft/openssl-1.1.0i.tar.gz"  "https://www.openssl.org/source/openssl-1.1.0i.tar.gz"
	build_chk_soft 44d667c142d7cda120332623eab69f40 "${BASEDIR}./soft/zlib-1.2.8.tar.gz"  "https://nchc.dl.sourceforge.net/project/libpng/zlib/1.2.8/zlib-1.2.8.tar.gz"

}

function filter_sh_comment()
{
	sed -i -e 's/^[ \t]*#$//g' -e 's/^[ \t]*#[^!*].*$//g'  $@
}

function build_version()
{
  GW_VER_FILE="../src/hw/gw_parser/include/gw_ver.h"
  GW_VER_MAJOR=`grep "GW_VER_MAJOR" ${GW_VER_FILE} | awk -F'[ ]' '{print $3}'`
  GW_VER_MINOR=`grep "GW_VER_MINOR" ${GW_VER_FILE} | awk -F'[ ]' '{print $3}'`
  GW_VER_REVISION=`grep "GW_VER_REVISION" ${GW_VER_FILE} | awk -F'[ ]' '{print $3}'`
  GW_VER=${GW_VER_MAJOR}.${GW_VER_MINOR}.${GW_VER_REVISION}
  echo "${GW_VER}" > ${BASEDIR}./hw/inst/version.txt
}

function copy_py_dynamic_lib()
{
	# flask requests flask-gzip Flask-GzipBomb httpbin
	#blinker==1.4 brotlipy==0.7.0 certifi==2017.7.27.1 cffi==1.11.2 chardet==3.0.4 click==6.7 colorama==0.3.9 contextlib2==0.5.5 crayons==0.1.2 dateparser==0.6.0 decorator==4.1.2 enum34==1.1.6 Flask==0.12.2 Flask-Cache==0.13.1 Flask-Common==0.2.0 Flask-gzip==0.1 Flask-GzipBomb==0.1.0 Flask-Limiter==******* greenlet==0.4.12 gunicorn==19.7.1 httpbin==0.6.2 humanize==0.5.1 idna==2.6 itsdangerous==0.24 Jinja2==2.9.6 limits==1.2.1 MarkupSafe==1.0 maya==0.3.3 meinheld==0.6.1 pendulum==1.3.1 pycparser==2.18 python-dateutil==2.6.1 pytz==2017.3 pytzdata==2017.3 raven==6.3.0 regex==2017.9.23 requests==2.18.4 ruamel.ordereddict==0.4.13 ruamel.yaml==0.15.34 six==1.11.0 tzlocal==1.4 urllib3==1.22 Werkzeug==0.12.2 whitenoise==3.3.1
	cp -f "${BASEDIR}./soft/pip_dp/"click-6.7-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-0.12.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"itsdangerous-0.24.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Jinja2-2.9.6-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"MarkupSafe-1.0.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Werkzeug-0.12.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"blinker-1.4.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"brotlipy-0.7.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"certifi-2017.7.27.1-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"cffi-1.11.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"chardet-3.0.4-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"colorama-0.3.9-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"contextlib2-0.5.5-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"crayons-0.1.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"dateparser-0.6.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"decorator-4.1.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"enum34-1.1.6-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-Cache-0.13.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-Common-0.2.0.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-gzip-0.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask_GzipBomb-0.1.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Flask-Limiter-*******.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"greenlet-0.4.12-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"gunicorn-19.7.1-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"httpbin-0.6.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"humanize-0.5.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"idna-2.6-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"limits-1.2.1-py2-none-any.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"MarkupSafe-1.0.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"maya-0.3.3-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"meinheld-0.6.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pendulum-1.3.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pycparser-2.18.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"python_dateutil-2.6.1-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pytz-2017.3-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pytzdata-2017.3-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"raven-6.3.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"regex-2017.09.23.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"requests-2.18.4-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"ruamel.ordereddict-0.4.13-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"ruamel.yaml-0.15.34-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"six-1.11.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"tzlocal-1.4.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"urllib3-1.22-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"Werkzeug-0.12.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"whitenoise-3.3.1-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"scapy-2.4.3.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"psutil-5.7.2.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"prometheus_client-0.8.0-py2.py3-none-any.whl  "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"nacos-sdk-python-0.1.1.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	#Flask-HTTPAuth==3.2.3
	cp -f "${BASEDIR}./soft/pip_dp/"Flask_HTTPAuth-3.2.3-py2.py3-none-any.whl "${BASEDIR}./hw/inst/pip_dp"
}

function build_hw()
{
	VER=$(cat "${BASEDIR}./../src/inst/version_hw.txt" | head -n1)
	VER_FULL=$(cat "${BASEDIR}./../src/inst/version_hw.txt" | head -n2 | tail -n1)
	if [[ "${VER}" == "" ]]; then
		echo hw invalid version info
		return
	fi
	# 增加GIT版本号
	#GIT_VER_REVISION=`git rev-list HEAD | wc -l | awk '{print $1}'`
	#GIT_VER_REVISION=$(expr $(git rev-list --all | wc -l) "+" "1" "-" $(git rev-list --all  | grep -n $(git rev-parse HEAD) | cut -d: -f1))
	GIT_VER_REVISION=
	GIT_VER_SHA_FULL=`git rev-parse HEAD`
	GIT_VER_SHA_SHORT=`git rev-parse --short HEAD`
	if [[ ! "${GIT_VER_REVISION}" == "" ]]; then
		VER=${VER}.${GIT_VER_REVISION}
	fi
	if [[ ! "${GIT_VER_SHA_SHORT}" == "" ]]; then
		VER=${VER}-${GIT_VER_SHA_SHORT}
	fi

	DPDK_DIR=~/dpdk-stable/dpdk-stable-17.08.1/
	DPDK_957_KERNEL_DIR=~/dpdk-stable-957/dpdk-stable-17.08.1/
	DPDK_1127_KERNEL_DIR=~/dpdk-stable-1127/dpdk-stable-17.08.1/
	PFRING_DIR=/root/pfring-stable/
	LIBRDKAFKA=/root/librdkafka/

	rm -rf "${BASEDIR}./hw/"

	mkdir -p "${BASEDIR}./hw/tmp/"
	mkdir -p "${BASEDIR}./hw/inst/"
    mkdir -p "${BASEDIR}./hw/inst/stats/"
	mkdir -p "${BASEDIR}./hw/inst/lib/"
	mkdir -p "${BASEDIR}./hw/inst/lib/kernel-693/"
	mkdir -p "${BASEDIR}./hw/inst/lib/kernel-1127/"
	mkdir -p "${BASEDIR}./hw/inst/kmod/kernel-693/"
	mkdir -p "${BASEDIR}./hw/inst/kmod/kernel-1127/"
	mkdir -p "${BASEDIR}./hw/inst/gw_operation/"

  	build_version

	# get-pip
	mkdir -p "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/get-pip.py" "${BASEDIR}./hw/inst/"
	if [[   true ]]; then
		compile_py "${BASEDIR}./hw/inst/"*.py
		rm -f "${BASEDIR}./hw/inst/"*.py
	fi

	# pip==9.0.1 setuptools==36.6.0 wheel==0.30.0
	cp -f "${BASEDIR}./soft/pip_dp/"wheel-0.30.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"setuptools-36.6.0-*.whl "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"pip-9.0.1-*.whl "${BASEDIR}./hw/inst/pip_dp/"

	# supervisor==3.3.3 meld3==1.0.2
	cp -f "${BASEDIR}./soft/pip_dp/"supervisor-3.3.3.tar.gz "${BASEDIR}./hw/inst/pip_dp/"
	cp -f "${BASEDIR}./soft/pip_dp/"meld3-1.0.2-*.whl "${BASEDIR}./hw/inst/pip_dp/"

	# librdkafka
	cp -f "${BASEDIR}./soft/lz4-1.7.5-3.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/gperftools-libs-2.6.1-1.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/numactl-2.0.12-5.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/numactl-libs-2.0.12-5.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/libpcap-1.5.3-12.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/gdb-7.6.1-119.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/libnet-1.1.6-7.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/pciutils-3.5.1-3.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/python-devel-2.7.5-88.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/boost-chrono-1.53.0-28.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/libicu-50.2-3.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/boost-locale-1.53.0-28.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/tcpdump-4.9.2-4.el7_7.1.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/telnet-0.17-64.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/psmisc-22.20-16.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/htop-2.2.0-3.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/vsftpd-3.0.2-27.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/libiconv-1.15-1.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"

	cp -f "${BASEDIR}./soft/pciutils-libs-3.5.1-3.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/python-rpm-macros-3-32.el7.noarch.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/python-srpm-macros-3-32.el7.noarch.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/python2-rpm-macros-3-32.el7.noarch.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/python-2.7.5-88.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/python-libs-2.7.5-88.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/boost-system-1.53.0-28.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/boost-thread-1.53.0-28.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/cpp-4.8.5-39.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/gcc-4.8.5-39.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/glibc-devel-2.17-307.el7.1.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/glibc-headers-2.17-307.el7.1.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/kernel-headers-3.10.0-1127.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/libmpc-1.0.1-3.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/mpfr-3.1.1-4.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/glibc-2.17-307.el7.1.x86_64.rpm" "${BASEDIR}./hw/inst/"
	#cp -f "${BASEDIR}./soft/glibc-2.17-307.el7.1.i686.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/glibc-common-2.17-307.el7.1.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/libgcc-4.8.5-39.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/libgomp-4.8.5-39.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/jq-1.6-1.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/oniguruma-5.9.5-3.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}./soft/unzip-6.0-21.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"

	copy_py_dynamic_lib

	#获取产品序列号
	if [[ -z ${PRODUCT_ID} ]];then
	    PRODUCT_ID=`cat "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h" | grep GW_PRODUCT_SERIAL_ID | awk '{print $3}' | awk -F '["]' '{print $2}'`
	else
		sed -i "s,GW_PRODUCT_SERIAL_ID.*,GW_PRODUCT_SERIAL_ID \"${PRODUCT_ID}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"
	fi

	# 替换GIT版本号
	sed -i  "s,GW_GIT_VER_SHA.*,GW_GIT_VER_SHA \"${GIT_VER_SHA_FULL}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"
	sed -i  "s,GW_GIT_VER_SHORT_SHA.*,GW_GIT_VER_SHORT_SHA \"${GIT_VER_SHA_SHORT}\"," "${BASEDIR}../src/hw/gw_parser/include/gw_ver.h"

	BLD_DIR=`pwd`

	# 指定内核版本
	if [[ -d /usr/src/kernels/3.10.0-693.17.1.el7.x86_64/ ]]; then
		export RTE_SDK=${DPDK_DIR};
		export RTE_TARGET=build
		export RTE_KERNELDIR=/usr/src/kernels/3.10.0-693.17.1.el7.x86_64/\
		# 编译 dpdk库
		if [[ ! -d "${DPDK_DIR}./" ]]; then
			mkdir -p "${DPDK_DIR}./"
		fi

		if [[ ! -d "${DPDK_DIR}./"/build ]]; then
			tar xvf "${BASEDIR}./soft/dpdk-17.08.1.tar.xz" -C "${DPDK_DIR}./../"
			cd "${DPDK_DIR}./"
			make config T=x86_64-native-linuxapp-gcc
			if [[ ! true ]]; then
				# for old cpu ex. dl580 g7 E7540
				sed -ri 's,(CONFIG_RTE_MACHINE=).*,\1"default",' build/.config
				sed -ri 's,(CONFIG_RTE_ENABLE_AVX=).*,\1n,' build/.config # no avx
			fi
			sed -ri 's,(PMD_PCAP=).*,\1y,' build/.config
			sed -ri 's,(SHARED_LIB=).*,\1y,' build/.config
			make
			cd "${BLD_DIR}"
		fi

		if [[ ! -f "${DPDK_DIR}./build/lib/libdpdk.so" ]]; then
			echo "dpdk compile error!";
			return
		fi
		if [[ `ls "${DPDK_DIR}./build/kmod/"*.ko 2>/dev/null | wc -l` -eq 0 ]]; then
			echo "dpdk compile error!";
			return
		fi
		if [[ `ls "${DPDK_DIR}./build/lib/"librte_*.so.* 2>/dev/null | wc -l` -eq 0 ]]; then
			echo "dpdk compile error!";
			return
		fi

		# 编译pfring库
		if [[ ! -f ${PFRING_DIR}/kernel/pf_ring.ko ]];then
			#编译pfring内核
			cd ${PFRING_DIR}/kernel
			make

			#编译网卡驱动
			cd ${PFRING_DIR}/drivers
			make
		fi

		if [[ ! -f ${PFRING_DIR}/userland/lib/libpfring.so ]];then
			# 编译pfring动态库
			cd ${PFRING_DIR}/userland/lib/
			./configure;make

			# 编译libpcap动态库
			cd ../libpcap/
			./configure;make

			cd "${BLD_DIR}"
		fi

		if [[ ! -f ${PFRING_DIR}/userland/lib/libpfring.so ]];then
			echo "pfring compile error!"
			return
		fi

		if [[ ! -f ${PFRING_DIR}/userland/libpcap/libpcap.so.1.8.1 ]];then
			echo "pfring compile error!"
			return
		fi

		if [[ ! -f ${PFRING_DIR}/kernel/pf_ring.ko ]];then
			echo "pfring compile error!"
			return
		fi

		if [[ ! -f ${PFRING_DIR}/drivers/intel/ixgbe/ixgbe-5.3.7-zc/src/ixgbe.ko ]];then
			echo "pfring compile error!"
			return
		fi

		if [[ ! -f ${PFRING_DIR}/drivers/intel/i40e/i40e-2.4.6-zc/src/i40e.ko ]];then
			echo "pfring compile error!"
			return
		fi

		if [[ ! -f ${PFRING_DIR}/drivers/intel/igb/igb-5.3.5.18-zc/src/igb.ko ]];then
			echo "pfring compile error!"
			return
		fi

		if [[ ! -f ${PFRING_DIR}/drivers/intel/e1000e/e1000e-3.4.0.2-zc/src/e1000e.ko ]];then
			echo "pfring compile error!"
			return
		fi
	fi

	if [[ -d /usr/src/kernels/3.10.0-1127.10.1.el7.x86_64/ ]]; then
		export RTE_SDK=${DPDK_1127_KERNEL_DIR};
		export RTE_TARGET=build
		export RTE_KERNELDIR=/usr/src/kernels/3.10.0-1127.10.1.el7.x86_64/
		# 编译 dpdk库
		if [[ ! -d "${DPDK_1127_KERNEL_DIR}./" ]]; then
			mkdir -p "${DPDK_1127_KERNEL_DIR}./"
		fi

		if [[ ! -d "${DPDK_1127_KERNEL_DIR}./"/build ]]; then
			tar xvf "${BASEDIR}./soft/dpdk-17.08.1.tar.xz" -C "${DPDK_1127_KERNEL_DIR}./../"
			cd "${DPDK_1127_KERNEL_DIR}./"
			make config T=x86_64-native-linuxapp-gcc
			if [[ ! true ]]; then
				# for old cpu ex. dl580 g7 E7540
				sed -ri 's,(CONFIG_RTE_MACHINE=).*,\1"default",' build/.config
				sed -ri 's,(CONFIG_RTE_ENABLE_AVX=).*,\1n,' build/.config # no avx
			fi
			sed -ri 's,(PMD_PCAP=).*,\1y,' build/.config
			sed -ri 's,(SHARED_LIB=).*,\1y,' build/.config
			sed -i '9a#if (defined(RHEL_RELEASE_CODE) && \\\n	(RHEL_RELEASE_CODE >= RHEL_RELEASE_VERSION(7, 5)) && \\\n	(RHEL_RELEASE_CODE < RHEL_RELEASE_VERSION(8, 0)) && \\\n	(LINUX_VERSION_CODE < KERNEL_VERSION(4, 14, 0)))\n#define ndo_change_mtu ndo_change_mtu_rh74\n#endif' lib/librte_eal/linuxapp/kni/compat.h
			make
			cd "${BLD_DIR}"
		fi

		if [[ ! -f "${DPDK_1127_KERNEL_DIR}./build/lib/libdpdk.so" ]]; then
			echo "dpdk compile error!";
			return
		fi
		if [[ `ls "${DPDK_1127_KERNEL_DIR}./build/kmod/"*.ko 2>/dev/null | wc -l` -eq 0 ]]; then
			echo "dpdk compile error!";
			return
		fi
		if [[ `ls "${DPDK_1127_KERNEL_DIR}./build/lib/"librte_*.so.* 2>/dev/null | wc -l` -eq 0 ]]; then
			echo "dpdk compile error!";
			return
		fi
	fi

	if [[ -d /usr/src/kernels/3.10.0-957.el7.x86_64/ ]]; then
		export RTE_SDK=${DPDK_957_KERNEL_DIR};
		export RTE_TARGET=build
		export RTE_KERNELDIR=/usr/src/kernels/3.10.0-957.el7.x86_64/
		# 编译 dpdk库
		if [[ ! -d "${DPDK_957_KERNEL_DIR}./" ]]; then
			mkdir -p "${DPDK_957_KERNEL_DIR}./"
		fi

		if [[ ! -d "${DPDK_957_KERNEL_DIR}./"/build ]]; then
			tar xvf "${BASEDIR}./soft/dpdk-17.08.1.tar.xz" -C "${DPDK_957_KERNEL_DIR}./../"
			cd "${DPDK_957_KERNEL_DIR}./"
			make config T=x86_64-native-linuxapp-gcc
			if [[ ! true ]]; then
				# for old cpu ex. dl580 g7 E7540
				sed -ri 's,(CONFIG_RTE_MACHINE=).*,\1"default",' build/.config
				sed -ri 's,(CONFIG_RTE_ENABLE_AVX=).*,\1n,' build/.config # no avx
			fi
			sed -ri 's,(PMD_PCAP=).*,\1y,' build/.config
			sed -ri 's,(SHARED_LIB=).*,\1y,' build/.config
			sed -i '9a#if (defined(RHEL_RELEASE_CODE) && \\\n	(RHEL_RELEASE_CODE >= RHEL_RELEASE_VERSION(7, 5)) && \\\n	(RHEL_RELEASE_CODE < RHEL_RELEASE_VERSION(8, 0)) && \\\n	(LINUX_VERSION_CODE < KERNEL_VERSION(4, 14, 0)))\n#define ndo_change_mtu ndo_change_mtu_rh74\n#endif' lib/librte_eal/linuxapp/kni/compat.h
			make
			cd "${BLD_DIR}"
		fi

		if [[ ! -f "${DPDK_957_KERNEL_DIR}./build/lib/libdpdk.so" ]]; then
			echo "dpdk compile error!";
			return
		fi
		if [[ `ls "${DPDK_957_KERNEL_DIR}./build/kmod/"*.ko 2>/dev/null | wc -l` -eq 0 ]]; then
			echo "dpdk compile error!";
			return
		fi
		if [[ `ls "${DPDK_957_KERNEL_DIR}./build/lib/"librte_*.so.* 2>/dev/null | wc -l` -eq 0 ]]; then
			echo "dpdk compile error!";
			return
		fi
	fi

	if [[ ! -d /usr/src/kernels/3.10.0-693.17.1.el7.x86_64/ && ! -d /usr/src/kernels/3.10.0-1127.10.1.el7.x86_64/ && -d /usr/src/kernels/3.10.0-957.el7.x86_64/ ]];then
		echo ERROR: Not found kernel-devel-3.10.0-693.17.1
		echo Please install kernel-devel-3.10.0-693.17.1.el7.x86_64.rpm
		echo ERROR: Not found kernel-devel-3.10.0-1127.10.1
		echo Please install kernel-devel-3.10.0-1127.10.1.el7.x86_64.rpm
		echo ERROR: Not found kernel-devel-3.10.0-957
		echo Please install kernel-devel-3.10.0-957.el7.x86_64.rpm
		exit -1
	fi

	#编译librdkafka
	if [[ ! -f ${LIBRDKAFKA}/src/librdkafka.so.1 ]];then
		cd ${LIBRDKAFKA}
		./configure
		cd src
		make

	fi

	if [[ ! -f ${LIBRDKAFKA}/src/librdkafka.so.1 ]];then
		echo "librdkafka compile error"
		return
	fi
	cd "${BLD_DIR}"

	# 编译 openssl 1.1.0
	if [[ ! -d /opt/openssl/lib/ ]];then
		tar xf "${BASEDIR}./soft/openssl-1.1.0i.tar.gz"
		cd openssl-1.1.0i
		./config --prefix=/opt/openssl
		make
		sudo make install
		cd "${BLD_DIR}"
	fi

	# 编译libgpg-error
	if [[ ! -d /opt/gcrypt/libgpg-error/ ]];then
		tar -jxf "${BASEDIR}./soft/libgpg-error-1.36.tar.bz2"
		cd libgpg-error-1.36
		./configure --prefix=/opt/gcrypt/libgpg-error
		make
		sudo make install
		cd "${BLD_DIR}"
	fi

	# 编译libgcrypt
	if [[ ! -d /opt/gcrypt/libgcrypt/ ]];then
		tar -jxf  "${BASEDIR}./soft/libgcrypt-1.8.5.tar.bz2"
		cd libgcrypt-1.8.5
		./configure --prefix=/opt/gcrypt/libgcrypt --with-libgpg-error-prefix=/opt/gcrypt/libgpg-error
		make
		sudo make install
		cd "${BLD_DIR}"
	fi

	echo "BLD_DIR=${BLD_DIR}"
	# 编译 gw_parser主程序
	cd "../src/hw/gw_parser/"
	make clean;
	BUILD_SCHEME=Release BUILD_TOOLS=1  CPLUS_INCLUDE_PATH=/opt/Python3/include/python3.7m LIBRARY_PATH=/opt/Python3/lib  make
	if [[ ! -f "./gw_parser" ]]; then
		echo "gw_parser compile error!";
		return
	fi

	#编译 watch_process程序
	cd "../watch_process"
	make clean;make
	if [[ ! -f "./watch_process" ]]; then
		echo "watch process compile error!";
		return
	fi

	cd "${BLD_DIR}"
	#编译 stats python 服务  src/hw/gw_stats_srv
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"usr_cfg.json  "${BASEDIR}./hw/inst/stats"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"gw_stat_srv.py "${BASEDIR}./hw/inst/stats/"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"gw_stat_srv.conf "${BASEDIR}./hw/inst/stats/"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"_licutils.so "${BASEDIR}./hw/inst/stats/"
	cp -f "${BASEDIR}../src/hw/gw_stats_srv/"licutils.pyc "${BASEDIR}./hw/inst/stats/"
	if [[ true ]]; then
		compile_py "${BASEDIR}./hw/inst/stats/"*.py
			rm -f "${BASEDIR}./hw/inst/stats/"*.py
	fi

	if [[ true ]]; then
		compile_py "${BASEDIR}./hw/inst/gw_operation/"*.py
			rm -f "${BASEDIR}./hw/inst/gw_operation/"*.py
	fi
	cp -f "${BASEDIR}../src/gw_operation/"gw_operation.service "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/gw_operation/"supervisord_gw_operation.conf "${BASEDIR}./hw/inst/gw_operation/"

	# openssl
	OPENSSL_TAR_GZ="`pwd`/hw/inst/openssl-bin-1.1.0i.tar.gz"
	cd /opt/openssl/
	tar czf "${OPENSSL_TAR_GZ}" .
	cd -

	# rte_kni.ko igb_uio.ko
	cp -f "${DPDK_DIR}./build/kmod/"*.ko "${BASEDIR}./hw/inst/kmod/kernel-693/"
	cp -f "${DPDK_957_KERNEL_DIR}./build/kmod/"*.ko "${BASEDIR}./hw/inst/kmod/kernel-957/"
	cp -f "${DPDK_1127_KERNEL_DIR}./build/kmod/"*.ko "${BASEDIR}./hw/inst/kmod/kernel-1127/"
 	# 安装时SO库需要进行符号连接
	cp -f "${DPDK_DIR}./build/lib/"librte_*.so.* "${BASEDIR}./hw/inst/lib/kernel-693/"
	cp -f "${DPDK_957_KERNEL_DIR}./build/lib/"librte_*.so.* "${BASEDIR}./hw/inst/lib/kernel-957/"
	cp -f "${DPDK_1127_KERNEL_DIR}./build/lib/"librte_*.so.* "${BASEDIR}./hw/inst/lib/kernel-1127/"
	# dpdk动态库
	cp -f "${DPDK_DIR}./build/lib/libdpdk.so" "${BASEDIR}./hw/inst/lib/kernel-693/"
	cp -f "${DPDK_957_KERNEL_DIR}./build/lib/libdpdk.so" "${BASEDIR}./hw/inst/lib/kernel-957/"
	cp -f "${DPDK_1127_KERNEL_DIR}./build/lib/libdpdk.so" "${BASEDIR}./hw/inst/lib/kernel-1127/"

	#pf_ring动态库
	cp -f "${PFRING_DIR}./kernel/pf_ring.ko" "${BASEDIR}./hw/inst/kmod/"
	cp -f "${PFRING_DIR}./drivers/intel/igb/igb-5.3.5.18-zc/src/igb.ko" "${BASEDIR}./hw/inst/kmod/"
	cp -f "${PFRING_DIR}./drivers/intel/e1000e/e1000e-3.4.0.2-zc/src/e1000e.ko" "${BASEDIR}./hw/inst/kmod/"
	cp -f "${PFRING_DIR}./drivers/intel/ixgbe/ixgbe-5.3.7-zc/src/ixgbe.ko" "${BASEDIR}./hw/inst/kmod/"
	cp -f "${PFRING_DIR}./userland/lib/libpfring.so" "${BASEDIR}./hw/inst/lib/"
	cp -f "${PFRING_DIR}./userland/libpcap/"libpcap.so.* "${BASEDIR}./hw/inst/lib/"
	#librdkakfa
	cp -f "${LIBRDKAFKA}./src/librdkafka.so.1" "${BASEDIR}./hw/inst/lib/"

	# 授权动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/liblicutils_c_sdk/liblicutils.so" "${BASEDIR}./hw/inst/"

	#文件格式解析动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/utils/file_type/libfile_type.so" "${BASEDIR}./hw/inst/"

	#nacos_c++动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/nacos_c++/libnacos-cli.so" "${BASEDIR}./hw/inst/"

	#libaws-api-c++动态库
	cp -f "${BASEDIR}../src/hw/gw_parser/libaws_api_c++/lib64/"libaws-cpp-sdk*.so "${BASEDIR}./hw/inst"

	cp -f "/opt/gcrypt/libgpg-error/lib/"libgpg-error.so.0.27.0 "${BASEDIR}./hw/inst/lib"
	cp -f "/opt/gcrypt/libgcrypt/lib/"libgcrypt.so.20.2.5 "${BASEDIR}./hw/inst/lib"

	# 网关主程序
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser" "${BASEDIR}./hw/inst/"

	#网关parser模块
	if [ ! -d  "${BASEDIR}./hw/inst/parser" ]; then
		mkdir -p "${BASEDIR}./hw/inst/parser"
	fi

	parser_sos="ftp_parser/ftp_parser.so hbase_parser/hbase_parser.so hdfs_parser/hdfs_parser.so hive_parser/hive_parser.so http_parser/http_parser.so mongo_parser/mongo_parser.so ssl_parser/ssl_parser.so oracle_parser/oracle_parser.so mail_parser/mail_parser.so smb_parser/smb_parser.so"
	for parser_so in $parser_sos
	do
    	cp -f "${BASEDIR}../src/hw/gw_parser/parser/$parser_so" "${BASEDIR}./hw/inst/parser"
	done

	#网关source模块
	if [ ! -d  "${BASEDIR}./hw/inst/source" ]; then
		mkdir -p "${BASEDIR}./hw/inst/source"
	fi

	src_sos="file_source/file_source.so dpdk_source/dpdk_source.so nic_source/nic_source.so pcap_source/pcap_source.so"

	for src_so in $src_sos
	do
		cp -f "${BASEDIR}../src/hw/gw_parser/source/$src_so" "${BASEDIR}./hw/inst/source"
	done

	#网关upload模块
	if [ ! -d  "${BASEDIR}./hw/inst/upload" ]; then
		mkdir -p "${BASEDIR}./hw/inst/upload"
	fi

	upload_sos="log_upload/log_upload.so  kafka_upload/kafka_upload.so diy_upload/diy_upload.so"

	for upload_so in $upload_sos
	do
		cp -f "${BASEDIR}../src/hw/gw_parser/upload/$upload_so" "${BASEDIR}./hw/inst/upload"
	done

	# 监控网关状态可执行程序
	cp -f "${BASEDIR}../src/hw/watch_process/watch_process" "${BASEDIR}./hw/inst/"

	# 网关配置文件
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy.conf" "${BASEDIR}./hw/inst/"gw_parser.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/gw_parser_deploy_5g.conf" "${BASEDIR}./hw/inst/"gw_parser_5g.conf

	cp -f "${BASEDIR}../src/hw/gw_parser/forward_info_rule_deploy.conf" "${BASEDIR}./hw/inst/"forward_info_rule.conf
	cp -f "${BASEDIR}../src/hw/gw_parser/user_info_rule_deploy.conf" "${BASEDIR}./hw/inst/"user_info_rule.conf
	# supervisor服务配置文件(去掉gw_stats_srv_license项)
	cp -f "${BASEDIR}../src/hw/supervisord_hw.conf" "${BASEDIR}./hw/inst/"supervisord_hw.conf
	# 绑定网卡脚本文件
	cp -f "${BASEDIR}../src/hw/init_dpdk.sh" "${BASEDIR}./hw/inst/"
	filter_sh_comment "${BASEDIR}./hw/inst/init_dpdk.sh"
	# 解绑网卡脚本文件
	cp -f "${BASEDIR}../src/hw/dpdk_unbind.sh" "${BASEDIR}./hw/inst/"
	filter_sh_comment "${BASEDIR}./hw/inst/dpdk_unbind.sh"

	# qat驱动初始化
	cp -f "${BASEDIR}../src/hw/init_qat.sh" "${BASEDIR}./hw/inst/"
	filter_sh_comment "${BASEDIR}./hw/inst/init_qat.sh"

	cp -f "${BASEDIR}../src/hw/gwhw.service" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_black_list.txt" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_black_list_tools.txt" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/hw/dpdk_alloc_GB" "${BASEDIR}./hw/inst"
	cp -f "${BASEDIR}../src/tools/uninstall.sh" "${BASEDIR}./hw/inst/"uninst_def_svr.sh
	filter_sh_comment "${BASEDIR}./hw/inst/uninst_def_svr.sh"

	cp -f "${BASEDIR}../src/tools/file_monitor.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/burnintest.sh" "${BASEDIR}./hw/inst/"

	cp -f "${BASEDIR}../src/tools/update_gwhw.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/rollback_gwhw.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/filter_sample.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/inst/uninstall_gw.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/gw_sys_info.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/agent_upload_quota.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/load_driver.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/set_irq_affinity" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/data_analyze.py" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/troubleshooting_tool.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/web_uninst.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/renice_sshd.sh" "${BASEDIR}./hw/inst/"
	cp -f "${BASEDIR}../src/tools/licutils_demo" "${BASEDIR}./hw/inst/"

	# 拷贝url过滤库文件
	cp -f "${BASEDIR}../src/hw/url_filter_base.file" "${BASEDIR}./hw/inst/"

	# cp -f "${BASEDIR}../src/inst/kernel-3.10.0-693.17.1.el7.x86_64.rpm" "${BASEDIR}./hw/inst/"

	# 去除文件中注释
	cp -f "${BASEDIR}./../src/inst/inst_hw.sh" "${BASEDIR}./hw/inst/inst.sh"
	filter_sh_comment "${BASEDIR}./hw/inst/inst.sh"
	cp -f "${BASEDIR}./../src/inst/readme_hw.txt" "${BASEDIR}./hw/inst/readme.txt"
	cp -f "${BASEDIR}./../src/inst/Changelog_hw" "${BASEDIR}./hw/inst/Changelog"

	cd "${BASEDIR}./hw/"
	#find "${BASEDIR}./hw/inst/" -type f -name .DS_Store  -exec rm -f {} \;
	# version info
	zip -r inst-${VER}-${PRODUCT_ID}-$(date -u '+%Y%m%dT%H%M%SZ').zip inst/  -x .DS_Store -x \*/.DS_Store

	cd "${BLD_DIR}"
}



function build_cp()
{
	if [[ -f "$1" ]]; then
		echo copy "$1" "$2"
		cp -f "$1" "$2"
	fi
}

function build_cp_new()
{
	echo copy "$1" "$2"
	cp -f $1 "$2"
}

function build_out()
{
	mkdir -p "${BASEDIR}./out"

	# build_cp "${BASEDIR}./hw/inst.zip" "${BASEDIR}./out/hw_inst.zip"
	# time and version info
	if [[ `ls "${BASEDIR}./hw/inst"*.zip 2>/dev/null | wc -l` -eq 1 ]]; then
		build_cp "${BASEDIR}./hw/inst"*.zip "${BASEDIR}./out/hw_inst.zip"
		build_cp_new "${BASEDIR}./hw/inst"*.zip "${BASEDIR}./out/"
		rename './out/inst' './out/hw_inst' "${BASEDIR}./out/inst"*.zip
	fi

	# 最新版本文件
	if [[ -f "${BASEDIR}./out/hw_inst.zip" ]]; then
		touch "${BASEDIR}./out/hw_inst.zip"
	fi

	return
}

function main()
{
	echo build begin $(date "+%F %T")
	echo "product id  $PRODUCT_ID "
	func_log  build_env

	func_log  build_hw

	func_log  build_out

	echo build end $(date "+%F %T")
	return
}

if [ $# -eq 0 ]; then
  BUILD_TYPE=hw
elif [ $# -eq 1 ];then
  if [ "$1" != "hw" -a "$1" != "pcap" ];then
	PRODUCT_ID=$1
	BUILD_TYPE=hw
  else
	BUILD_TYPE=$1
  fi
elif [ $# -ge 2 ];then
  if [ "$1" != "hw" -a "$1" != "pcap" ];then
    echo "Usage: $0 [hw|pcap]"
	echo "  [hw]    hardware version, with dpdk"
	echo "  [pcap] software version, without dpdk, for pcap"
 	exit 1
  else
    BUILD_TYPE=$1
    PRODUCT_ID=$2
  fi
fi

export BUILD_TYPE=$BUILD_TYPE

echo "product id $PRODUCT_ID"
main $PRODUCT_ID
