FROM centos:7.8.2003

# 安装环境、工具
RUN rpm -Uvh https://forensics.cert.org/cert-forensics-tools-release-el7.rpm \
	&& yum --enablerepo=forensics install libiconv-devel kernel-headers git gcc-c++ wget make numactl-devel libpcap-devel bzip2 zip libcurl-devel zlib-devel gperftools-devel librdkafka-devel zip file flex byacc bison kernel kernel-devel which -y 

# 内核环境
RUN export KERNEL_URL=http://*************/isos/mirrors/kernel-devel && rpm -ivh --force $KERNEL_URL/kernel-devel-3.10.0-1062.el7.x86_64.rpm \
$KERNEL_URL/kernel-devel-3.10.0-957.el7.x86_64.rpm \
$KERNEL_URL/kernel-devel-3.10.0-862.el7.x86_64.rpm \
$KERNEL_URL/kernel-devel-3.10.0-693.el7.x86_64.rpm \
$KERNEL_URL/kernel-3.10.0-1062.el7.x86_64.rpm \
$KERNEL_URL/kernel-3.10.0-957.el7.x86_64.rpm \
$KERNEL_URL/kernel-3.10.0-862.el7.x86_64.rpm \
$KERNEL_URL/kernel-3.10.0-693.el7.x86_64.rpm

# dpdk,pfring
RUN cd && wget http://*************/isos/mirrors/soft/dpdk-17.08.1.tar.xz \
  && ls /usr/src/kernels/ |grep -v debug | awk -F- '{print $2,$0}' | awk -F. '{print $1,$0}'| awk '{printf("\
	VER=%s && DPDK_DIR=/tmp/dpdk-stable-$VER/dpdk-stable-17.08.1/ && \
	export RTE_KERNELDIR=/usr/src/kernels/%s/ && export RTE_SDK=$DPDK_DIR && export RTE_TARGET=build && \
	echo BUILD $VER,KERNEL_DIR=$RTE_KERNELDIR && \
	mkdir -p $DPDK_DIR && ls $DPDK_DIR && \
	tar xvf dpdk-17.08.1.tar.xz -C $DPDK_DIR/../ && \
	cd $DPDK_DIR && \
	make config T=x86_64-native-linuxapp-gcc && \
	sed -ri '\''s,(PMD_PCAP=).*,\\1y,'\'' build/.config && \
	sed -ri '\''s,(SHARED_LIB=).*,\\1y,'\'' build/.config && \
	sed -i '\''9a#if (defined(RHEL_RELEASE_CODE) && \\\\\\n	(RHEL_RELEASE_CODE >= RHEL_RELEASE_VERSION(7, 5)) && \\\\\\n	(RHEL_RELEASE_CODE < RHEL_RELEASE_VERSION(8, 0)) && \\\\\\n	(LINUX_VERSION_CODE < KERNEL_VERSION(4, 14, 0)))\\n#define ndo_change_mtu ndo_change_mtu_rh74\\n#endif'\'' lib/librte_eal/linuxapp/kni/compat.h && \
	make && cd -  \n",$1,$3)}' | sh  && ln -s /tmp/dpdk-stable-693/ /tmp/dpdk-stable \
  && ls /lib/modules/ |grep -v debug | awk -F- '{print $2,$0}' | awk -F. '{print $1,$0}'| awk '{printf("PFRING_DIR=/tmp/pfring-stable-%s && \
	export BUILD_KERNEL=%s && \
	echo PFRING_DIR=$PFRING_DIR,BUILD_KERNEL=$BUILD_KERNEL && \
	git clone https://gitee.com/codergeek/PF_RING.git  $PFRING_DIR -b  7.4.0-stable --single-branch && \
	cd $PFRING_DIR/kernel && make || exit -1 && cd - && \
	cd $PFRING_DIR/drivers && make || exit -1 && cd - && \
	cd $PFRING_DIR/userland/lib/ && ./configure && make || exit -1 && cd - && \
	cd $PFRING_DIR/userland/libpcap && ./configure && make && cd - || exit -1 \n",$1,$3)}' | sh && ln -s /tmp/pfring-stable-693/ /tmp/pfring-stable \
  && cd /tmp && LIBRDKAFKA="librdkafka" && git clone https://gitee.com/mirrors/librdkafka.git $LIBRDKAFKA -b v1.1.0 --single-branch && cd $LIBRDKAFKA && ./configure && cd src && make && cd || exit -1

# openssl,pip
RUN wget http://*************/isos/mirrors/soft/openssl-1.1.0i.tar.gz && tar xf openssl-1.1.0i.tar.gz -C /root \
	&& cd /root/openssl-1.1.0i && ./config --prefix=/opt/openssl && make install && cd .. && rm -rf openssl-1.1.0i openssl-1.1.0i.tar.gz \
 && wget http://*************/isos/mirrors/soft/libgpg-error-1.36.tar.bz2 && tar jxf libgpg-error-1.36.tar.bz2 -C /root \
	&& cd /root/libgpg-error-1.36 && ./configure --prefix=/opt/gcrypt/libgpg-error && make && make install && cd .. && rm -rf libgpg-error-1.36 libgpg-error-1.36.tar.bz2 \
 && wget http://*************/isos/mirrors/soft/libgcrypt-1.8.5.tar.bz2 && tar jxf libgcrypt-1.8.5.tar.bz2 -C /root \
	&& cd /root/libgcrypt-1.8.5 && ./configure --prefix=/opt/gcrypt/libgcrypt --with-libgpg-error-prefix=/opt/gcrypt/libgpg-error && make install && cd .. && rm -rf libgcrypt-1.8.5 libgcrypt-1.8.5.tar.bz2 \
 && wget https://bootstrap.pypa.io/get-pip.py --no-check-certificate && python get-pip.py

ENV PREP_BUILD_ROOT=/tmp RTE_SDK=/tmp/dpdk-stable/dpdk-stable-17.08.1/

# 安装单元测试工具gtest
RUN yum install epel-release -y && yum install -y gtest-devel

CMD "/bin/bash"

