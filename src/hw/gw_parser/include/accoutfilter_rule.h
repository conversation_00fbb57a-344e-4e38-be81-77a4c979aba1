/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __ACCOUTFILTER_RULE_H__
#define __ACCOUTFILTER_RULE_H__

#include "filter_rule.h"
#include "cJSON.h"

typedef struct
{
  cJSON *out;
  const char *r_k;
} rule_key_t;

// user info rule
typedef struct user_info_rule
{
  char *url;
  char *keys;      // "JSESSIONID\0" "\0\0";
  char *keys_name; // "nickname\0" "\0\0";
  char *keys_uid;  // "uid\0" "\0\0";
} user_info_rule_t;

// 修改全局配置变量为结构体变量，支持配置参数动态加载
typedef struct
{
  user_info_rule_t *rule_user_info;
  int rule_user_info_num;
} rule_user_info_t;

class CGwCommon;
class CTcpParser;

class CAccoutfilterRule : public CFilterRule
{
public:
  CAccoutfilterRule(void);
  virtual ~CAccoutfilterRule(void);

public:
  /**
   * 命中过滤规则。
   * @param unsigned int data
   */
  virtual int hit(unsigned data);

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   *  设置TcpParser对象实例 
   *  @paramount CTcpParser *tcpparser
   */
  virtual void set_tcp_parser(CTcpParser *tcpparser);

  virtual void init();

  virtual void fini();

  virtual void set_accout_filter(const char* p_accout_filename);

  virtual rule_user_info_t *get_accout_info() const;

  virtual void rule_user_extract(const char *s, size_t length, rule_key_t *rk_list);

  /**
   *  动态加载账号转发规则 
   *  @param const char*
   */
  virtual void read_conf_user_info_for_mon(const char *filename);

protected:
  rule_user_info_t *read_accout_rule_info(const cJSON *cjson_rule_conf);
  char *rule_init_url_strings(cJSON *r);
  char *rule_init_conf_strings(cJSON *r);
  void free_accout_info(rule_user_info_t *rule_accout_info_ptr);

protected:
    CGwCommon *m_comm;
    CTcpParser *m_tcpparser;
    rule_user_info_t *m_accoutfilter_info;
};

#endif //__ACCOUTFILTER_RULE_H__