/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __PARSER_H__
#define __PARSER_H__

#include <stdint.h>
#include <string>
class CGwCommon;
class CFilterRule;

struct conn;

struct app_stream;

struct HalfStreamData;
struct StreamData;
struct SessionMgtData;

class CSessionMgt;
class CSession;
class CTcpParser;

class CParser
{
public:
  virtual ~CParser(void) = 0;

public:
  /**
   * 
   * 清空缓存 (待上传的数据，待解密数据等)
   * */
  virtual void cache_clean() = 0;
  
  /**
   * 在接收数据时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual bool probe(CSessionMgt *, const app_stream *,const struct conn *, CSession*) = 0;

  

  /**
   * 在连接关闭时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual bool probe_on_close(CSessionMgt *, const app_stream *,const struct conn *, CSession*) = 0;

  /**
   * 在连接重置时，探测数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual bool probe_on_reset(CSessionMgt *, const app_stream *,const struct conn *, CSession*) = 0;

  /**
   * 在接收数据时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual int parse(CSessionMgt *, const app_stream *,const struct conn *, CSession*) = 0;

  /**
   * 在接收数据时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual int parse_clear(CSessionMgt *, const app_stream *,const struct conn *, CSession*) = 0;
  
  /**
   * 在连接关闭时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual int parse_on_close(CSessionMgt *, const app_stream *,const struct conn *, CSession*) = 0;

  /**
   * 在连接重置时，解析数据流协议。
   * @param CSessionMgt *
   * @param app_stream *
   * @paramstruct conn *
   */
  virtual int parse_on_reset(CSessionMgt *, const app_stream *,const struct conn *, CSession*) = 0;

  /**
   * 获取当前流解析出来的数据。
   * @param struct StreamData *
   * @param int dir
   * @param int *data_len
   * @param int *offset_out
   */
  virtual const char *get_data(const struct StreamData *, int dir, int *data_len, int *offset_out) = 0;

  /**
   * 已处理字节数。
   * @param struct StreamData *
   * @param int dir
   * @param int num
   */
  virtual bool discard(struct StreamData *, int dir, int num) = 0;

  /**
   * 已处理字节数，同时更新数据。
   * @param struct StreamData *
   * @param int dir
   * @param int num
   */
  virtual bool discard_and_update(struct StreamData *, int dir, int num) = 0;

  // /**
  //  * 删除解析对象中在会话管理中的单边数据。
  //  * @param HalfStreamData*
  //  */
  // virtual void del_session_half_stream(HalfStreamData *) = 0;

  /**
   * @param StreamData*
   */
  virtual void del_session_stream(StreamData *) = 0;

  /**
   * @param SessionMgtData*
   */
  virtual void del_session_param(SessionMgtData *) = 0;

  virtual void init() = 0;

  virtual void fini() = 0;

  virtual void run() = 0;

  /**
   * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
   */
  virtual const char *get_name(void) const = 0;

  /**
   * 获取版本号。
   */
  virtual const char *get_version(void) const = 0;

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm) = 0;

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *) = 0;

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void) = 0;

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void) = 0;

  /**
   * 设置过滤规则。
   * @param CFilterRule *rule
   */
  virtual void set_url_filter_rule(CFilterRule *rule) = 0;

  /**
   *  设置账号过滤规则 
   *  @param CFilterRule *rule
   */
  virtual void set_accout_filter_rule(CFilterRule *rule) = 0;

  virtual void set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule) = 0;
  /**
   * 增加上层协议解析对象。
   * @param CParser *parser
   */
  virtual void add_upstream(CParser *parser) = 0;

  /**
   * 清空上层协议解析对象
   */
  virtual void reset_upstream(void) = 0;

  /**
   * 推送到上层消息(异步方式, Json序列化数据)
   * @param char *s
   * @param size_t *length
   */
  virtual void push_upstream_msg(char *s, size_t length) = 0;

  /**
   * 是否使用当前协议解析流数据
   * @param struct StreamData*
   */
  virtual bool is_parsed(const struct StreamData *) const = 0;

  /**
   * 克隆会话流数据到队列中使用(预留)
   * @param struct StreamData*
   */
  virtual struct StreamData *clone_stream_data(const struct StreamData *) = 0;

  /**
   *  获取解析http数量(针对http parser) 
   */
  virtual uint64_t get_parser_http_cnt() = 0;

  /**
   *  获取解析http成功的数量(针对http parser) 
   */
  virtual uint64_t get_succ_parser_http_cnt() = 0;

  /**
   *  获取解析parser的状态数据，以便于进行查看Parser内部状态
   */
  virtual void* get_parser_status() = 0;

  /**
   * 设置解析对象type
   */
  virtual void set_parser_type(int type) = 0;

  /**
   *  设置tcpparser对象
   */
  virtual void set_tcp_parser(CTcpParser *p) {};

  virtual void read_conf_urlbase_for_mon() = 0;

  virtual void read_conf_filetype_for_mon() = 0;

  virtual void get_log_buf(char *log_buf, size_t log_buf_len) const = 0;

  virtual uint32_t parser_status() const = 0;

  virtual void set_drop_file_event(bool is_drop) {};

protected:
  //   CGwCommon *m_comm;
  //   volatile int m_quit_signal;
  //   char m_name[32];
};

#endif // __PARSER_H__
