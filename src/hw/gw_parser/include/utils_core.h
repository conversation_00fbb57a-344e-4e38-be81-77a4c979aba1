/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __UTILS_CORE_H__
#define __UTILS_CORE_H__

#ifdef __cplusplus
extern "C"
{
#endif

// 最大线程数据
#if __FOR_TEST__
#define WORKER_PARAMS_MAX_NUM (16)
#elif __FOR_DEV_MAC__
#define WORKER_PARAMS_MAX_NUM (4)
#elif __FOR_DEV_VIRENV__
#define WORKER_PARAMS_MAX_NUM (8)
#else
// #define WORKER_PARAMS_MAX_NUM (32)
#define WORKER_PARAMS_MAX_NUM (64)
#endif

#ifdef __cplusplus
}
#endif

#endif // __UTILS_CORE_H__
