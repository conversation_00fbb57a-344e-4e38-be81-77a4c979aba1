/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __TASK_WORKER_H__
#define __TASK_WORKER_H__

#include <stddef.h>

struct worker_routine_param;

class CTaskWorker;
struct TaskWorkerData
{
  size_t mem_size;
  CTaskWorker *p_tw;

  // follow data
};

class CWorkerQueue;
class CTaskWorker
{
public:
  virtual CWorkerQueue *get_wq(void) const = 0;
  virtual int deal_data(const TaskWorkerData *) = 0;
  virtual void free_data(const TaskWorkerData *) = 0;
  virtual void init(void) = 0;
  virtual void fini(void) = 0;
  virtual void release(void) const = 0;
};

#endif // __TASK_WORKER_H__
