/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __PORTFILTER_RULE_H__
#define __PORTFILTER_RULE_H__

#include <mutex>
#include "filter_rule.h"

typedef struct
{
  unsigned int port_min;
  unsigned int port_max;
} port_filter_data_t;

typedef struct
{
  int conf_port_filter_num;                  // = 0;
  int conf_port_filter_last;                 // = 0;
  port_filter_data_t *conf_port_filter_port; // = NULL;
  unsigned int port_bit[65536 >> 5];         // = {0};
} conf_port_filter_t;

class CGwCommon;
class CTcpParser;

class CPortfilterRule : public CFilterRule
{
public:
    CPortfilterRule(void);
    virtual ~CPortfilterRule(void);

    virtual void init();

    virtual void fini();

public:
    /**
     * 命中过滤规则。
     * @param unsigned int data
     */
    virtual int hit(unsigned data);

    /**
     * 设置全局公共类对象实例。
     * @param CGwCommon *comm
     */
    virtual void set_gw_common(CGwCommon *comm);

    /**
     *  设置TcpParser对象实例 
     *  @paramount CTcpParser *tcpparser
     */
    virtual void set_tcp_parser(CTcpParser *tcpparser);

    /**
     *  设置端口过滤 
     *  @param const char*
     */
    virtual void set_port_filter(const char* p_port_filter_rule);

    virtual void set_port_white(const char* p_port_white_rule);

    /**
     *  命中端口过滤规则 
     *  @param unsigned short
     */
    virtual int port_filter_hit(unsigned short port);
    virtual int port_white_hit(unsigned short port);
    /**
     *  PORT参数更新 
     */
    virtual void port_filter_for_mon(int is_for_nacos, const char *s = 0);
    virtual void port_white_for_mon(int is_for_nacos, const char *s = 0);

protected:
    void free_conf_port(conf_port_filter_t *p_port_filter);
    int read_conf_port_filter_inner(const char *s, conf_port_filter_t *conf_ptr);
    int get_port_num(const char *s, size_t length);
    static int compare_port_filter_data(const void *a, const void *b);

protected:
    conf_port_filter_t *m_p_port_filter_rule;
    conf_port_filter_t *m_p_port_white_rule;
    CGwCommon *m_comm;
    CTcpParser *m_tcpparser;
    std::mutex m_conf_mutex;
};


#endif //__PORTFILTER_RULE_H__