#ifndef __FILE_INFO_DEAL_H
#define __FILE_INFO_DEAL_H


#define RW_FLAG_READ   0
#define RW_FLAG_WRITE  1
#define RW_FLAG_DELETE 2
#define RW_FLAG_OTHER  3

#include <string>

struct upload_stats_t
{
  std::string bucket_name;
  std::string str_file_path;  /* 唯一路径 */
  std::string str_md5_sum;
  std::string str_sha256;
  int ret;                    /* 返回值 */
  
  upload_stats_t() = default;
  upload_stats_t(upload_stats_t&) = default;
  upload_stats_t(upload_stats_t&&);
  upload_stats_t& operator=(upload_stats_t&) = default;
  upload_stats_t& operator=(upload_stats_t&&);
};

class FileUpload
{
public:
    virtual upload_stats_t upload_file(const char*, std::size_t len, const char* file_name ) = 0;
    virtual std::string get_file_sha256(const char* data, const uint32_t data_len) {return "";}
};

struct file_info_json 
{
  char *file_name;
  char *file_type;
  char *file_warn;
  char *file_data;
  char *dir;
  char *md5sum;
  char *sha256;
  char *bucket;
  int rw_flag;
  int is_complete;
  int file_len;
  int upload_flag;
  int file_real_len;
public:
  file_info_json();
  file_info_json(file_info_json& fij) = delete;
  file_info_json(file_info_json&& fij);
  file_info_json& operator=(file_info_json&) = delete;
  file_info_json& operator=(file_info_json&&);
  ~file_info_json();
};

const char* file_info_format(const char* file_name, size_t file_name_len, 
                            const char* file_type, size_t file_type_len, 
                            const char* file_warn, size_t file_warn_len, 
                            const char* file_data, size_t file_len, 
                            int is_incomplete,
                            int rw_flag, FileUpload* fileupload , int max_data_len);

const char* http_file_info_format(const char* file_name, size_t file_name_len, 
                                const char* file_type, size_t file_type_len, 
                                const char* file_warn, size_t file_warn_len, 
                                const char* file_data, size_t file_len, 
                                int is_incomplete,
                                int rw_flag, FileUpload* fileupload, int *upload_flag , int max_data_len);

file_info_json file_info_format_encode(const char* file_name, size_t file_name_len, 
                            const char* file_type, size_t file_type_len, 
                            const char* file_warn, size_t file_warn_len, 
                            const char* file_data, size_t file_len, 
                            int is_incomplete,
                            int rw_flag, FileUpload* fileupload , int max_data_len);

#endif // __FILE_INFO_DEAL_H