/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __URLFILTER_RULE_H__
#define __URLFILTER_RULE_H__

#include <pthread.h>
#include "filter_rule.h"
#include "cJSON.h"

// forward info rule
typedef struct forward_info_rule
{
    char *url;
    int state; // 0,1
    int threshold;
    double percent;
} forward_info_rule_t;

#define URL_LEN 255

typedef struct forward_table_tag
{
    char url[URL_LEN];
    long threshold;              /* 达到阈值后，触发抽样 */
    double percent;              /* 抽样百分比 */
    int sampling;                /* 0未触发阈值, 不抽样; 1已触发阈值，进行抽样 */
    unsigned long counter;       /* 计数器 */
    unsigned long last_sampling; /* 上一个抽样点 */
} forward_table;

// 修改全局配置变量为结构体变量，支持配置参数动态加载
typedef struct
{
    forward_info_rule_t *rule_forward_info;
    forward_info_rule_t *rule_forward_unknown_info;
    int rule_forward_info_num;
    int forward;

    forward_table *known_forward_table;
    forward_table *unknown_forward_table;
    int known_forward_table_cnt;
    int unknown_forward_table_cnt;

} rule_forward_t;

enum forward_reason
{
  NO_FORWARD_RULE = 0,          /* 没有转发规则,比如转发规则文件不存在，这种情况全部会转发 */
  MATCH_FORWARD_RULE = 1,       /* 匹配到了转发规则 */
  DONT_MATCH_FORWARD_RULE = 2   /* 未匹配到转发规则 */
};

class CGwCommon;
class CTcpParser;

class CUrlfilterRule : public CFilterRule
{
  public:
    CUrlfilterRule(void);
    virtual ~CUrlfilterRule(void);

  public:
    /**
     * 命中过滤规则。
     * @param unsigned int data
     */
    virtual int hit(unsigned data);

    /**
     * 命中URL转发规则
     * @param const char *url
     */
    virtual int forward(const char *url);

    /**
     * 设置全局公共类对象实例。
     * @param CGwCommon *comm
     */
    virtual void set_gw_common(CGwCommon *comm);

    /**
     *  设置TcpParser对象实例 
     *  @paramount CTcpParser *tcpparser
     */
    virtual void set_tcp_parser(CTcpParser *tcpparser);


    /**
     *    设置url过滤规则 
     *    @param const char*
     */
    virtual void set_url_filter(const char *p_urlfilter_name);

    /**
     * 命中URL转发规则
     * @param char *url
     * @param char *sub_url
     * @param int
     * @param int *
     */
    virtual int check_forward(char *full_url, char *sub_url, int full_check, int *forward_reason);

    /**
     *  动态加载URL转发规则 
     *  @param const char*
     */
    virtual void read_conf_forward_for_mon(const char* filename);

    virtual void init();

    virtual void fini();

  protected:
    rule_forward_t *read_urlfilter_forward(const cJSON *cjson_forward_conf);
    void free_urlfilter_forward(rule_forward_t *p_url_filter);
    void init_forward_table_inner();

  protected:
    CGwCommon *m_comm;
    CTcpParser *m_tcpparser;
    rule_forward_t *m_p_url_filter;
    pthread_mutex_t m_lock;
    int m_i_init;
    int m_i_unknown_url_max_num;
};
#endif //__URLFILTER_RULE_H__