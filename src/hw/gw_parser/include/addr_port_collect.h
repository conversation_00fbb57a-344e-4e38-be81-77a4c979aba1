#pragma once

#include <stdint.h>
#define MAX_ADDR_PROT_NUM 500
#define SHOW_ADDR_PORT_NUM 50
#define STREAM_ORDER_BY_FLOW 0
#define STREAM_ORDER_BY_COUNT 1
struct AddrPortCount
{
    uint32_t src_addr;
    uint32_t dst_addr;
    uint16_t dst_port;
    uint64_t total;
    uint64_t count;
};
class CAddrPortCollect
{
public:
    CAddrPortCollect();
    ~CAddrPortCollect();
    void add_addr_port(uint32_t dst_addr, uint32_t src_addr, uint16_t dst_port, uint32_t flow, int first_link);
    int get_top_items(struct AddrPortCount* items, int num, int order_field);
    void clear();
    //void set_max_num(int max_num);

protected:
    struct AddrPortCount addr_port_nums[MAX_ADDR_PROT_NUM];
    volatile int writing;
};