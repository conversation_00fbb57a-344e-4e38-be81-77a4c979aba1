/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __IPFILTER_H__
#define __IPFILTER_H__

#include <stdint.h>
#include <pthread.h>
#include <mutex>
#include "filter_rule.h"

typedef struct
{
  // int i_ip_type;  /* 0:ipv4, 1:ipv6  */
  int is_v4;
  struct
  {
    uint32_t addr_min;
    uint16_t a_ipv6_addr_min[8];
  };

  struct
  {
    uint32_t addr_max;
    uint16_t a_ipv6_addr_max[8];
  };
} ip_filter_data_t;

typedef struct
{
  int conf_ip_filter_num;                // = 0;
  int conf_ip_filter_last;               // = 0;
  ip_filter_data_t *conf_ip_filter_addr; // = NULL;
} conf_ip_filter_t;

class CGwCommon;
class CTcpParser;

class CIpfilterRule : public CFilterRule
{
  public:
    CIpfilterRule(void);
    virtual ~CIpfilterRule(void);

  public:
    /**
     * 命中过滤规则。
     * @param unsigned int data
     */
    virtual int hit(unsigned data);

    virtual void init();

    virtual void fini();

    /**
     * 设置全局公共类对象实例。
     * @param CGwCommon *comm
     */
    virtual void set_gw_common(CGwCommon *comm);

    /**
     *  设置TcpParser对象实例 
     *  @paramount CTcpParser *tcpparser
     */
    virtual void set_tcp_parser(CTcpParser *tcpparser);

    /**
     * 设置过滤IP参数
     * @param const char*
     */ 
    virtual void set_ip_filter(const char *p_ip_filter);

    /**
     * 设置白名单IP参数
     * @param const char*
     */ 
    virtual void set_ip_white(const char *p_ip_white);

    /**
     * 过滤IPv4地址
     * @param unsigned 
     * @return  1 命中 0 未命中
     */
    virtual int ip_filter_hit(unsigned addr);

    /**
     * 过滤IPv6地址
     * @param uint16_t*
     * @return 1 命中 0 未命中
     */
    virtual int ip6_filter_hit(uint16_t *p_ipv6_addr);

    /**
     * IPv4白名单
     * @param unsigned
     * return 1 命中 0 未命中 
     */
    virtual int ip_white_hit(unsigned addr);

    /**
     * IPv6白名单
     * @param uint16_t*
     * @return 1 命中 0 未命中
     */
    virtual int ip6_white_hit(uint16_t *p_ipv6_addr);

    /**
     *  IP过滤参数动态更新 
     */
    virtual void ip_filter_for_mon(int is_for_nacos, const char *s = 0);

    /**
     *  IP白名单参数动态更新 
     */
    virtual void ip_white_for_mon(int is_for_nacos, const char *s = 0);

protected:
    int read_conf_ip_inner(const char *s, conf_ip_filter_t *conf_ptr);
    void free_conf_ip(conf_ip_filter_t *p_conf_ip);
    int get_ipv6_addr(const char *p_buf, size_t length, uint16_t *p_v6_addr);
    static int compare_ipv6_filter_data(const void *a, const void *b);
    int ip_hit_inner(conf_ip_filter_t *conf_ptr, unsigned addr);
    int ip6_hit_inner(conf_ip_filter_t *conf_ptr, uint16_t *p_ipv6_addr);
    int get_ip_addr(const char *s, size_t length);
    void read_conf_ip_for_mon_inner(conf_ip_filter_t **_p_conf_ptr, const char *s);
 
protected:
    CGwCommon *m_comm;
    CTcpParser *m_tcpparser;
    conf_ip_filter_t *m_conf_ip_filter_ptr;
    conf_ip_filter_t *m_conf_ip_white_ptr;
    std::mutex m_conf_mutex;
};

#endif // __IPFILTER_H__