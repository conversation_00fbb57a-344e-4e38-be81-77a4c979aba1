/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __GW_LICENSE_H__
#define __GW_LICENSE_H__

#include <string>
#include <pthread.h>
#include <vector>

#ifdef __cplusplus
extern "C" {
#endif
#include "authorization_lic.h"

class CGwCommon;

class CGwLicense
{
public:
  CGwLicense(void);
  virtual ~CGwLicense(void);

public:
  virtual void init();

  virtual void fini();

  virtual void run();

  /**
   * 验证网卡源数量
   * @param int num
   */
  virtual int verify_source_num(int num);

  /**
   * 验证模块功能开关
   * @param const char*
   */
  virtual int verify_func(const char *);

  /**
   * 验证产品有效时间。
   */
  virtual int verify_product_exprie();

  /**
   * 验证是否需要限流 
   */
  virtual int verify_limit_rate();

  /**
   * 获取魔数
   */
  virtual int get_magic();

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *);

  /**
   * 设置ip字节数在状态统计的名字，根据名字查找qps，限速使用 
   * @param const char *
   */
  virtual void set_ip_bytes_stat_name(const char *name);

  /**
   * 设置source源名称
   * @param const char*
   */
  virtual void set_source_name(const char *name);

  /**
   *  获取网卡源数量
   */
  virtual unsigned get_eth_num();

  /**
   * 验证pcap源功能是否开启 
   */
  virtual unsigned verify_pcap_func();

  /**
   *  验证ssl功能是否开启 
   */
  virtual unsigned verify_ssl_func();

  /**
   *  获取gzip解压深度 
   */
  virtual unsigned get_gzip_deep();

protected:
  CGwCommon *m_comm;
  volatile int m_quit_signal;
  std::string m_str_lic_path;   /* license文件路径 */
  int m_i_verify_samll_version; /* 版本匹配是否匹配小版本 */
  unsigned m_u_product_expired; /* 产品是否过期 */
  unsigned m_u_pcap_enable;     /* pcap功能是否开启 */
  unsigned m_u_pcap_expired;    /* pcap功能是否过期 */
  unsigned m_u_ssl_enable;      /* ssl功能是否开启 */
  unsigned m_u_eth_num;         /* 限制网卡的数量 */
  unsigned m_u_limit_rate;      /*速率限制*/
  int m_u_magic;                /*超过速率限制后生成一个魔数，用于按session丢弃包*/
  unsigned m_u_gzip_deep;       /* http gzip 解压深度 */
  unsigned m_u_is_limit_speed;  /* 是否需要限流 */
  std::string m_str_ip_stat_name;
  std::vector<std::string> m_vec_source_name;
  int m_i_source_flag; /* 0 表示dpdk源 1 表示pcap源 */

protected:
  pthread_t m_thread_check_expired;
  int m_check_expired_thread_stats;
  static int report_check_expired_run(void *arg_ptr);
  int check_license_expired();

  pthread_t m_thread_check_rate;
  int m_check_rate_thread_stats;
  static int report_check_rate_run(void *arg_ptr);
  int check_license_rate();

private:

  void creat_product_info(st_pro_info_t *p_st_pro_info);

  /**
   * 初始化licese文件
   */
  int init_licutils(void);

  /**
   * 解析license功能模块信息 
   */
  void parser_func_license(void);
};

#ifdef __cplusplus
}
#endif

#endif // __GW_LICENSE_H__
