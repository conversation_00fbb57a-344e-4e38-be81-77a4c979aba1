/*
 * @Author: youweizhi
 * @LastEditors: youweizhi
 */
#ifndef __NACOS_LISTEN_CONF_H__
#define __NACOS_LISTEN_CONF_H__

#include <pthread.h>
#include <string>
#include <vector>
#include <map>
#include <functional>

class CGwCommon;
class CkeyChangeListener;
class CFilterRule;
class CParser;

class CGwConfig;

typedef struct
{
    std::string str_data_id;
    std::string str_group;
    std::string conf_type;
}nacos_api_param_t;

typedef struct
{
    int auth_enable = 0;
    std::string auth_username = "";
    std::string auth_password = "";
} nacos_auth;

class CNacosListen
{
    public:
        CNacosListen();
        virtual ~CNacosListen();

        virtual void init(void);
        virtual void fini(void);
        virtual void run();

        virtual void set_gw_common(CGwCommon *comm);
        virtual void set_quit_signal(void);
        virtual void wait_for_stop(void);
        virtual bool load_conf(const char *);
        virtual void deal_mon_conf(const std::string &nacos_info, CkeyChangeListener *change_listen);
        virtual void deal_mode_conf(const std::string &nacos_info, CkeyChangeListener *change_listen);
        virtual void deal_switch_conf(const std::string &nacos_info, CkeyChangeListener *change_listen);
        virtual void deal_limit_conf(const std::string &nacos_info, CkeyChangeListener *change_listen);
        virtual void add_conf_handle(const std::string&, std::function<void(const char*)>);
        virtual void add_conf_handle_int(const std::string&, std::function<void(int)>);
    
    private:
        int parser_key(CGwConfig *p_config, const char *section, const char *key, std::string &value, int is_ipseg);
        void mon_ip_conf(std::string &str_ips, std::string &str_ipseg, const char *p_flag);
        void use_conf_handle(std::string &str_value, const char *p_key);
        void use_conf_handle_int(int value, const char *p_key);
        static int thread_nacos_listen_config(void *p_arg);
        int nacos_listen_config(const char *p_server_addr);

    private:
        CGwCommon *m_comm;
        volatile int m_quit_signal;
        std::map<std::string, std::vector<nacos_api_param_t> > m_mp_nacos_api_param;
        std::map<std::string, nacos_auth> m_auth;
        std::vector<pthread_t> m_v_nacos_listen;
        std::vector<CkeyChangeListener *> m_v_change_listener;
        int m_nacos_log_level;
        std::map<std::string, std::function<void(const char *)> > m_conf_handle_map;
        std::map<std::string, std::function<void(int)> > m_conf_handle_int_map;
};

#endif // __NACOS_LISTEN_CONF_H__