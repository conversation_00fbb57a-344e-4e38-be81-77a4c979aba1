/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __WATCH_DOG_H__
#define __WATCH_DOG_H__

#include <pthread.h>
#include <stdint.h>

typedef struct
{
  pthread_t pid;                  /* 线程ID */
  uint32_t u32_is_important;      /* 线程是否重要 0 表示不重要 1 表示重要 */    
  int i_reserver;                 /* 预留 */
  void *p_resrver;                /* 预留 */
}pthread_info_t;

class CGwCommon;
struct pthread_watch_info;

class CWatchDog
{
public:
  CWatchDog(void);
  virtual ~CWatchDog(void);

public:
  /**
   * 线程循环中周期调用，报告状态。
   */
  virtual void report();

  virtual void init();

  virtual void fini();

  virtual void run();

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 线程初始调用一次开始守护，分配序号。
   */
  virtual int watch_module_conf_pthread_info(const pthread_info_t *p_st_pthread_info, uint32_t  *p_u32_index);

  /**
   * 各个线程报告状态
   */
  virtual int watch_modole_report_pthread_info(uint32_t u32_index);

  virtual void begin();

  virtual void end();

protected:
  CGwCommon *m_comm;
  volatile int m_quit_signal;
  pthread_t m_pthread_key;
  static int pthread_watch_module(void *arg_ptr);
  int routine_watch();
  uint32_t m_u32_thread_cnt;
  int m_i_watch_thread_state;
  pthread_watch_info* m_p_st_watch_info;
};

#endif // __WATCH_DOG_H__
