/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef _CFILTERRULE_H
#define _CFILTERRULE_H

class CGwCommon;
class CTcpParser;

class CFilterRule
{
public:
  CFilterRule(void);
  virtual ~CFilterRule(void);

public:
  /**
   * 命中过滤规则。
   * @param unsigned int data
   */
  virtual int hit(unsigned data) = 0;

  /**
   * 命中URL转发规则
   * @param const char *url
   */
  virtual int forward(const char *url);

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm) = 0;

  /**
   *  设置TcpParser对象实例 
   *  @paramount CTcpParser *tcpparser
   */
  virtual void set_tcp_parser(CTcpParser *tcpparser) = 0;

  /**
   *  IP过滤参数动态更新 
   */
  virtual void ip_filter_for_mon(int is_for_nacos, const char *s = 0);

  /**
   *  IP白名单参数动态更新 
   */
  virtual void ip_white_for_mon(int is_for_nacos, const char *s = 0);

  /**
   *  PORT参数更新 
   */
  virtual void port_filter_for_mon(int is_for_nacos, const char *s = 0);

  /**
   *  动态加载URL转发规则 
   *  @param const char*
   */
  virtual void read_conf_forward_for_mon(const char* filename);

  /**
   *  动态加载账号转发规则 
   *  @param const char*
   */
  virtual void read_conf_user_info_for_mon(const char *filename);

  virtual void init() = 0;

  virtual void fini() = 0;
};

#endif //_CFILTERRULE_H