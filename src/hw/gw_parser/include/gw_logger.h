/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __GW_LOGGER_H__
#define __GW_LOGGER_H__

#include <stdarg.h>

#include <vector>

#include "utils.h"

#define GWLOGGER_LEVEL_OFF 9       // OFF
#define GWLOGGER_LEVEL_TEST 8      // TST
#define GWLOGGER_LEVEL_FATAL 7     // FAT
#define GWLOGGER_LEVEL_ERROR 6     // ERR
#define GWLOGGER_LEVEL_WARN 5      // WAR
#define GWLOGGER_LEVEL_NOTICE 4    // NOT
#define GWLOGGER_LEVEL_INFO 3      // INF
#define GWLOGGER_LEVEL_DEBUG 2     // DBG
#define GWLOGGER_LEVEL_TRACE 1     // TRA
#define GWLOGGER_LEVEL_ALL 0       // ALL
#define GWLOG<PERSON>R_LEVEL_UNKNOWN 128 // UNK

#define GWLOG_(v, lv, format, ...) ((v)->get_gw_logger())->log(lv, format, ##__VA_ARGS__)
#define GWLOG_SYNC(v, lv, format, ...) ((v)->get_gw_logger())->log_sync(lv, format, ##__VA_ARGS__)

// 默认日志输出模式
#define GWLOG GWLOG_
// #define GWLOG GWLOG_SYNC

// #define GWLOG_FATAL(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_FATAL, format, ##__VA_ARGS__)
#define GWLOG_FATAL(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_FATAL, "%s:%-3d %s() " format, __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#define GWLOG_ERROR(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_ERROR, format, ##__VA_ARGS__)
#define GWLOG_WARN(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_WARN, format, ##__VA_ARGS__)
#define GWLOG_NOTICE(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_NOTICE, format, ##__VA_ARGS__)
#define GWLOG_INFO(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_INFO, format, ##__VA_ARGS__)

#ifdef NDEBUG
#define GWLOG_DEBUG(v, format, ...) ((void)0)
#else
// #define GWLOG_DEBUG(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_DEBUG, format, ##__VA_ARGS__)
#define GWLOG_DEBUG(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_DEBUG, "%s:%-3d %s() " format, __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#endif

#ifdef _DEBUG
// #define GWLOG_TRACE(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_TRACE, format, ##__VA_ARGS__)
#define GWLOG_TRACE(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_TRACE, "%s:%-3d %s() " format, __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#else
#define GWLOG_TRACE(v, format, ...) ((void)0)
#endif

#ifdef NDEBUG
#define GWLOG_TEST(v, format, ...) ((void)0)
#else
// #define GWLOG_TEST(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_TEST, format, ##__VA_ARGS__)
#define GWLOG_TEST(v, format, ...) GWLOG(v, GWLOGGER_LEVEL_TEST, "%s:%-3d %s() " format, __FILE__, __LINE__, __FUNCTION__, ##__VA_ARGS__)
#endif

class CGwCommon;
class CWorkerQueue;
struct worker_routine_param;

struct log_msg;

class CGwLogger
{
public:
  CGwLogger(void);
  virtual ~CGwLogger(void);

public:
  /**
  * 日志输出（异步到队列中）
  * @param int level
  * @param const char *format
  */
  virtual void log(int level, const char *format, ...);

  /**
  * 日志输出（同步）
  * @param int level
  * @param const char *format
  */
  virtual void log_sync(int level, const char *format, ...);

  virtual void log_flush(void);

  virtual void init();

  virtual void fini();

  virtual void run();

  virtual int get_level(void) const;

  virtual void set_level(int level);

  virtual void set_level_name(const char *level);

  /**
   * 设置日志输出模式
   *  0 表示异步
   *  1 表示同步
   */
  virtual void set_mode(int mode);

  /**
   * 获取日志输出模式
   */
  virtual int get_mode(void) const;

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

protected:
  CGwCommon *m_comm;
  volatile int m_quit_signal;
  int m_level;
  int m_mode;

protected:
  void free_worker_queue(CWorkerQueue *p);
  static void free_log_msg(log_msg *p); // this包含在结构体中

  static int worker_routine_log_msg(void *args_ptr, worker_routine_param *pwrp, void *p);
  int worker_routine_log_msg_inner(void *p);

  CWorkerQueue *m_wq;

  void log_args(int level, const char *format, va_list arg_ptr);

protected:
  std::vector<log_msg *> m_vec_log_msg;
  spinlock_t SPIN_DECL(m_msg_lock);
  //  日志缓存对象访问时加线程锁 lock unlock
  void log_msg_lock(void);
  void log_msg_unlock(void);
};

#endif // __GW_LOGGER_H__