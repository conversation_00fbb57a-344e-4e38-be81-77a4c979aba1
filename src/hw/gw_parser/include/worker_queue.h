/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __WORKER_QUEUE_H__
#define __WORKER_QUEUE_H__

#include <stdint.h>
#include <pthread.h>

#include <vector>
#include <string>

struct StatsTaskData;

class CGwCommon;
class CWatchDog;

typedef void (*q_destroy_func_t)(void *);

struct queue_root ;

typedef struct worker_routine_param
{
  int step; // WQ_WR_xx
  void *data;
} worker_routine_param_t;

class CTaskWorker;

typedef int (*WORKER_ROUTINE)(void *, worker_routine_param_t *, void *);

#define WQ_STATUS_SUCC 0
#define WQ_STATUS_FAIL 1
#define WQ_STATUS_FAIL2 2
#define WQ_STATUS_FAIL3 3
#define WQ_STATUS_FAIL4 4
#define WQ_QUEUE_FAIL  5

#define WQ_WR_BEGIN 1
#define WQ_WR_END 2
#define WQ_WR_DATA 3

class CWorkerQueue
{
public:
  CWorkerQueue(void);
  virtual ~CWorkerQueue(void);

protected:
  queue_root *m_p_q;
  std::vector<pthread_t> m_vec_thread;
  // pthread_key_t m_thread_key;
  StatsTaskData *m_stats_task_data;
  volatile uint64_t m_stats_queue_memory_size;
  volatile uint64_t m_stats_queue_max_memory_size;
  unsigned int m_conf_queue_max_num;
  uint64_t m_conf_queue_memory_max_size_bytes;

public:
  /**
   * 获取队列占用内存变量引用。
   */
  virtual volatile uint64_t &get_queue_mem_size(void);

  /**
   * 获取队列占用最大内存变量引用。
   */
  virtual volatile uint64_t &get_queue_max_mem_size(void);

  virtual void init();

  virtual void fini();

  virtual void run();

  /**
   * 设置全局公共类对象实例。
   * @param CCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置守护模块对象。
   * @param CWatchDog*
   */
  virtual void set_watchdog(CWatchDog *);

  /**
   * 创建队列。
   */
  virtual void create_queue(void);

  /**
   * 删除队列
   */
  virtual void delete_queue(void);

  /**
   * 清空队列
   */
  virtual void flush_queue(void);

  /**
   *  获取队列元素个数
   */
  virtual uint32_t queue_elements_num(void); 

  /**
   * 创建工作线程
   * @param int num
   * @param WORKER_ROUTINE worker_rtn
   * @param void *arg
   */
  virtual int create_thread(int num, WORKER_ROUTINE worker_rtn, void *arg);

  /**
   * 调整工作线程数量
   * @param int num
   */
  virtual int adjust_worker_thread_num(int num);

  /**
   * 等工作线程完成。
   * @param int off
   */
  virtual void wait_thread(int off);

  /**
   * 设置队列元素销毁函数。
   */
  virtual void set_queue_destroy_callback(q_destroy_func_t func);

  /**
   * 设置队列元素数量和占用内存大小。
   */
  virtual void set_queue_num_and_bytes(int num, uint64_t bytes);

  /**
   * 设置队列名称。
   */
  virtual void set_queue_name(const char *name);

  /**
   * 获取队列名称。
   */
  virtual const char *get_queue_name() const;

  /**
   * 更新守护报告。
   */
  virtual void update_report(void);

  /**
   * 放入数据到队列中。
   */
  virtual bool queue_put_data(void *p, size_t mem_size);

  /**
   * 状态计数
   * int type: = 0 succ; =1 fail; =2 fail2; =3 fail3; =4 fail4;
   * int num
   */
  virtual void status_count(int type, int num);

  /**
   * 获取任务状态
   */
  virtual StatsTaskData *get_stats_task_data(void) const;

  /**
   * 设置任务Worker
   */
  virtual void set_task_worker(CTaskWorker *ptw);

  /**
   * 设置任务状态 
  */
  virtual void set_stats_task_data(void);

protected:
  static void *thread_routine(void *args_ptr);
  void thread_routine_inner();

protected:
  CTaskWorker *m_ptw;

  CGwCommon *m_comm;
  volatile int m_quit_signal;
  q_destroy_func_t m_destroy_func;
  CWatchDog *m_watch_dog;

  std::string m_queue_name;

  WORKER_ROUTINE m_run_worker_rtn;
  void *m_run_arg;

  // 停止单个线程
  volatile int m_thread_stop_off;
};
#define SYNC_FETCH_AND_ADD_UINT64(a, b) __sync_fetch_and_add(a, (uint64_t)(b))
#define SYNC_FETCH_AND_SUB_UINT64(a, b) __sync_fetch_and_sub(a, (uint64_t)(b))
#endif // __WORKER_QUEUE_H__
