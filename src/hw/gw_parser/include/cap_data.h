
#ifndef __CAP_DATA_H__
#define __CAP_DATA_H__

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include "utils.h"

#define NET_CARD_STAT_LEN (8)
#define NIC_DEVICE_NAME_LEN (128)

typedef struct
{
  uint64_t u64_eth_speed;                  /* 千兆or万兆网卡 */
  char a_net_card_stat[NET_CARD_STAT_LEN]; /* 网卡状态(up or down) */
  uint64_t u64_in_total_packets;           /* 进入网口的包数 */
  uint64_t u64_in_err_packets;             /* 进入网卡出现错误的包数 */
  uint64_t u64_in_drop_packets;            /* 由硬件网卡丢弃的包数 */
  uint64_t u64_in_total_bytes;             /* 进入网卡的字节数 */
  uint64_t u64_in_drop_bytes;              /* 从硬件网卡丢弃的字节数 */
  uint64_t u64_in_err_bytes;               /* 进入网卡错误的字节数 */
} eth_info_t;

typedef struct 
{
  uint64_t u64_file_num;                        /* 解析pcap文件的数量 */
  uint64_t u64_file_succ_num;                   /* 解析pcap文件成功的数量 */
  uint64_t u64_file_fail_num;                   /* p解析pcap文件失败的数量 */
}pcap_probe_info_t;

typedef struct
{
  char ch_device_name[NIC_DEVICE_NAME_LEN];
  uint64_t u64_recv_packets;
  uint64_t u64_drop_packets;
  uint16_t u16_speed;
  char status[8];
} nic_info_t;

// int dpdk_main(int argc, char **argv, void(fastcall *callback)(void *, unsigned int, void *), void *userdata);

// void capd_set_quit_signal(void);
// void capd_print_stats(void);

typedef struct pkt_info
{
  int o_st; // 返回当前数据包的状态码， 用于tcp分组用, 0表表示丢弃; 1表示第一组; 2表示第二组; 3表示第三组; 最多分成8组
  void *buf;
  unsigned int size; // 包抓取长度
  unsigned int pkt_size; // 包实际长度
} pkt_info_t;

typedef int(fastcall *CALLBACK_PKT)(pkt_info_t *, unsigned int, void *);

typedef struct pcap_info
{
  void *buf;
  const char *filename;
  uint32_t timestamp;
  uint32_t microseconds;
  uint32_t packet_length;
  uint32_t packet_length_wire;
} pcap_info_t;

typedef void(fastcall *CALLBACK_PCAP)(pcap_info_t *, void *, int);

typedef int(fastcall *CALLBACK_SAMPLE)(void *);

typedef void(fastcall *CALLBACK_FLOW)(const char*, size_t);

typedef struct
{
  int size; // 结构体长度

  // mode 运行的模式
  // =0 IP处理与捕包在不同线程;
  // =1 IP处理与捕包在同一个线程;
  int mode;

  // 解析 ip 数据，只需要能够得到ip和端口信息的 header 减少数据考拷贝
  void *userdata_ip;
  CALLBACK_PKT cb_ip;

  // tcp
  void *userdata_tcp;
  CALLBACK_PCAP cb_tcp;

  void *userdata_flow;
  CALLBACK_FLOW cb_flow;

} capd_args_t;

// int capd_main_new(int argc, char **argv, capd_args_t *arg_ptr);

// /* 收集网卡当前的状态 */
// int collect_eth_info(eth_info_t *p_st_eth_info, uint32_t *p_work_port_cnt);

// void set_sample_args(int i_sample_mode, int i_sample_rate, CALLBACK_SAMPLE cb_sample);

// void set_limit_eth_num(uint32_t u32_eth_num);

#ifdef __cplusplus
}
#endif

#endif // __CAP_DATA_H__
