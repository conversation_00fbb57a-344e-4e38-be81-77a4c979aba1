/*
 * @Author: youweizhi
 * @LastEditors: youweizhi
 */
#ifndef __CPP_UTILS_H__
#define __CPP_UTILS_H__

#include <vector>
#include <string>

std::vector<std::string> split_string(std::string srcStr, std::string delimStr, bool repeatedCharIgnored = true);

void split_string(std::string srcStr, std::string delimStr, std::vector<std::string> &resultStringVector, bool repeatedCharIgnored = true);
bool wildcmp(const std::string& pattern, const std::string& str) ;


template<class T>
class CPointSafe
{
private:
    T** p;
public:
    CPointSafe(T* &_p) : p(&_p) {
    }
    ~CPointSafe() {
        if (p && *p) {
            free((void*)(*p));
        }
    }
    void move() {
        p = NULL;
    }
};

template<class T>
class CPPPointSafe
{
private:
    T *p;
public:
    CPPPointSafe(T *p){
        
    }
    ~CPPPointSafe() {
        if (p) {
            delete p;
        }
    }
    void move() {
        p = NULL;
    }
};

#endif