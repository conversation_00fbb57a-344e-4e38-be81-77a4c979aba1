/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __MON_CONF_H__
#define __MON_CONF_H__

#include <string>
#include <pthread.h>

typedef struct
{
  int file_size;    // 文件大小
  long modify_time; // 文件修改时间
  //char file_md5[32]; // TODO 文件MD5
} conf_file_stat_t;

class CGwCommon;

class CMonConf
{
public:
    CMonConf(void);
    virtual ~CMonConf(void);

    virtual void init();

    virtual void fini();

    virtual void run();

    virtual void set_gw_common(CGwCommon *comm);

    virtual void set_quit_signal(void);

    virtual void wait_for_stop(void);


private:
    std::string m_str_conf_filepath;
    conf_file_stat_t m_mon_conf;

    std::string m_str_url_filepath;
    conf_file_stat_t m_mon_conf_url;

    std::string m_str_accout_filepath;
    conf_file_stat_t m_mon_conf_accout;

    volatile int m_quit_signal;
    CGwCommon *m_comm;
    int m_routine_mon_conf_stat;
    pthread_t m_pthread_mon_conf;
    std::string m_str_urlbase_filepath;
    conf_file_stat_t m_mon_conf_urlbase;
    std::string m_str_interactive_filepath;
    conf_file_stat_t m_mon_conf_interactive;

    bool load_conf(void);
    
    static int thread_routine_mon_conf(void *arg_ptr);
    int routine_mon_conf();
    
    void load_conf_file_stat(const char *filename, conf_file_stat_t *p_cfs);
    int has_load_conf(conf_file_stat_t *p_cfs, const char *filename);
    void check_conf(void);
    void check_conf_forward(void);
    void check_conf_user_info(void);
    void check_conf_url_base_info(void);
};

#endif //__MON_CONF_H__