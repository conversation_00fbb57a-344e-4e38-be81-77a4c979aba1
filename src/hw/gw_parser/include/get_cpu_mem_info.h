#ifndef _GET_CPU_MEM_INFO_H_
#define _GET_CPU_MEM_INFO_H_

#include <stdint.h>
#include <sys/types.h>

#ifdef __cplusplus
extern "C"
{
#endif

/* param:  u32_core_id_num : IN: cpu核的个数 */
/*         pid : IN: 进程号 */
/*         p_f_cpu_usage  : OUT: 返回CPU使用率 */
void get_proc_cpu_usage(uint32_t u32_core_id_num, pid_t pid, float *p_f_cpu_usage);

/* param: pid :IN: 进程号 */
/*        p_f_mem_usage :OUT: 内存占用率 */
void get_proc_mem_usage(pid_t pid, float *p_f_mem_usage);

#ifdef __cplusplus
}
#endif

#endif