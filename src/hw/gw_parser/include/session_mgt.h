/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __SESSION_MGT_H__
#define __SESSION_MGT_H__

#include <string.h>

#include <time.h>

// #include <vector>
#include <map>

#include "utils.h"
#include "gw_common.h"
#include "addr_port_collect.h"

#define MAX_SESSION_MGT_PROTO_TYPE_NUM (16)
#define SESSION_MGT_PROTO_TYPE_TCP (0)

class CSession;
class CParser;
class CTcpParser;

class CSessionMgt
{
public:
  CSessionMgt();
  virtual ~CSessionMgt();

public:
  /**
   * @param const ConnData *conn
   */
  // virtual CSession *get_session(const ConnData *conn);

  /**
   * 查找会话Session。
   * @param const ConnData *conn
   */
  virtual CSession *find_session(const ConnData *conn);

  /**
   * 创建新会话。
   * @param const ConnData *conn
   */
  virtual CSession *new_session(const ConnData *conn);

  /**
   * 删除会话。
   * @param const ConnData *conn
   */
  virtual void del_session(const ConnData *conn);

  /**
   * 检查状态（主要检查是否有超时连接）。
   * @param int timeout
   */
  virtual void check_state(int timeout);

  virtual void init();

  virtual void fini();

  /**
   * @param int num
   */
  virtual void init_session(int num);

  /**
   * 释放所有会话数据。
   */
  virtual void free_all_session(void);

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 设置解析对象在会话管理中的数据。
   * @param CParser *parser
   * @param SessionMgtData* data
   */
  virtual bool set_parser_data(CParser *parser, SessionMgtData *data);

  /**
   * 获取解析对象在会话管理中的数据。
   * @param CParser *parser
   */
  virtual SessionMgtData *get_session_data_from_parser(CParser *parser);

  /**
   * 设置Tcp解析对象到会话中。
   * @param const ConnData *conn
   * @param CTcpParser *parser
   * @param StreamData* data
   */
  CSession* set_tcp_parser(const ConnData *conn, CTcpParser *parser, StreamData *data);

  /**
   * 创建新对象
   */
  virtual CSessionMgt *create(void) const;

  /**
   * 释放对象
   */
  virtual void release(void);

  /**
   * 克隆对象到队列中使用
   * @param CSessionMgt *
   */
  virtual void clone(const CSessionMgt *);


  virtual void add_addr_port(uint32_t dst_addr, uint16_t dst_port, uint32_t src_addr, uint16_t src_port, uint32_t flow, int first_link);

  virtual int get_top_addr_port_items(struct AddrPortCount *rets, int num, int order_field);

  virtual void clear_addr_port_items();

  virtual int get_session_num() const {
    return m_map_session.size();
  }

  virtual int get_max_session_num() const {
    return m_session_max_num;
  }

  virtual int get_disable_timeout_check() const {
    return m_disable_timeout_check;
  }
  virtual void set_disable_timeout_check(int disable_timeout_check) {
    m_disable_timeout_check = disable_timeout_check;
  }

protected:
  class map_session_func
  {
  public:
    inline bool operator()(const ConnData& cd1, const ConnData& cd2) volatile const
    {
      return memcmp(&cd1, &cd2, sizeof(ConnData)) < 0;
    }
  };

  // void free_session_data(const ConnData *conn);

protected:
  class CGwCommon *m_comm;
  int m_session_max_num;
  int m_disable_timeout_check;
  time_t m_last_check_time;
  std::map<ConnData, CSession *, map_session_func> m_map_session;

  typedef struct
  {
    union {
      CParser *parser;
      CTcpParser *tcp_parser;
    };
    SessionMgtData *data;
  } session_mgt_ext_parser_data_t;

  // 等同于 CSession::m_proto_data成员
  session_mgt_ext_parser_data_t m_proto_session_data[MAX_SESSION_MGT_PROTO_TYPE_NUM];
  CAddrPortCollect m_addr_port_collect;
};

#endif // __SESSION_MGT_H__
