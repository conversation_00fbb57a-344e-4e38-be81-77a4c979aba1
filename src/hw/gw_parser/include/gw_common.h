/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __GWCOMMON_H__
#define __GWCOMMON_H__

#include <time.h>
#include <stdint.h>

#include <vector>
#include <map>
#include <string>
#include "utils.h"
#include <pthread.h>

#define CONF_VB_GOBAL 1
#define CONF_VB_SSL 2

// upload mode
#define CONF_UPLOAD_MODE_WEB            1
#define CONF_UPLOAD_MODE_KAFKA          2
#define CONF_UPLOAD_MODE_LOG            3
#define CONF_UPLOAD_MODE_TEST           4

class CGwLicense;
class CGwConfig;
class CGwStats;
class CWatchDog;
class CGwLogger;
class CMonConf;
class CObCfg;
class CFilterRule;
class CWorkerQueue;
class CInteractiveService;
class CSource;
class CParser;
class CUpload;
class CSessionMgt;
class CSession;

class CNacosListen;

class CMinioUpload;

struct conn;
struct app_stream;

typedef struct deal_parser_param
{
  int state; // TCP_STATE_xxx
  CSessionMgt *p_smgt;
  const struct app_stream *p_app_stream;
  const struct conn *pcon;
} deal_parser_param_t;

class CGwCommon
{
public:
  CGwCommon(void);
  virtual ~CGwCommon(void);

public:

  virtual void cache_clean();
  virtual void init();
  /**
   * 调用多层依赖协议解析器对象进行解析。
   */
  virtual bool deal_parser(CSession *p_session, CParser *p_parent, deal_parser_param_t *pdpp, CParser *&parser_out) const;

  /**
   * 返回时间戳秒级（不受系统时间更改影响）
   */
  virtual time_t gw_time(void) const;

  /**
   * 返回时间戳毫秒级（不受系统时间更改影响）
   */
  virtual uint64_t gw_time_ms(void) const;


  virtual uint64_t gw_real_time_ms(void) const;

  uint64_t gw_real_time_ts(void) const;

  /**
   * 动态内存分配
   * @param int size
   */
  virtual void *mem_malloc(int size) const;

  /**
   * 动态内存释放
   * @param void*
   */
  virtual void mem_free(void *) const;

  /**
   * 动态内存调整
   * @param void*
   * @param int size
   */
  virtual void *mem_realloc(void *, int) const;

  /**
   * 内存不足提示。
   * @param const char *
   */
  virtual void no_mem(const char *) const;

  //#if !_DISABLE_LICENSE_
  /**
   * 获取授权管理对象实例
   */
  virtual CGwLicense *get_gw_license(void) const;
  //#endif

  /**
   * 获取配置对象实例
   */
  virtual CGwConfig *get_gw_config(void) const;

  /**
   * 获取全局状态对象实例
   */
  virtual CGwStats *get_gw_stats(void) const;

  /**
   * 获取守护对象实例
   */
  virtual CWatchDog *get_watchdog(void) const;

  /**
   * 获取日志对象实例
   */
  virtual CGwLogger *get_gw_logger(void) volatile const;

  /**
   * 获取动态参数配置实例 
   */
  virtual CMonConf *get_mon_conf(void) const;

  /**
   * 获取配置热变更实例 
   */
  virtual CObCfg *get_ob_cfg(void) const;

  /**
   * 获取conn连接地址字符串(src_ip,src_port,dst_ip,dst_port)
   * @param char buf[256]
   * @param struct conn *pcon
   */
  virtual const char *get_conn_addr(char buf[256], size_t size, const struct conn *pcon) volatile const;

  /**
   * 获取IP地址字符串
   * @param char buf[256]
   * @param struct addr *addr
   */
  virtual const char *get_ip_addr(char buf[256], size_t size, const struct addr *addr) volatile const;

  /**
   * 获取IP过滤规则对象实例
   * @param int type
   */
  virtual CFilterRule *get_ip_filter_rule(int type = 0) const;


  virtual CFilterRule *get_upload_client_ip_filter_rule() const
  {
    return m_upload_client_ip_filter_rule;
  }

  virtual CFilterRule *get_upload_server_ip_filter_rule() const
  {
    return m_upload_server_ip_filter_rule;
  }

  virtual CNacosListen* get_nacos_listen_conf() const;

  virtual CMinioUpload* get_minio_upload() const;

  /**
   * 获取端口过滤规则对象实例
   * @param int type
   */
  virtual CFilterRule *get_port_filter_rule(int type = 0) const;

  /**
   * 获取URL过滤规则对象实例
   * @param int type
   */
  virtual CFilterRule *get_url_filter_rule(int type = 0) const;

  /**
   * 获取账号过滤规则对象实例
   * @param int type
   */
  virtual CFilterRule *get_accout_filter_rule(int type = 0) const;

  /**
   * 获取交互服务对象实例
   */
  virtual CInteractiveService *get_interactive_service(void) const;

  /**
   * 获取所有网络包数据源对象。
   * @param CSource*
   * @param int size
   */
  virtual int get_source_array(CSource *[], int size) const;

  /**
   * 获取所有网络包解析对象。
   * @param CParser*
   * @param int size
   */
  virtual int get_parser_array(CParser *[], int size) const;

  /**
   * 获取所有消息上传对象。
   * @param CUpload*
   * @param int size
   */
  virtual int get_upload_array(CUpload *[], int size) const;

  /**
   * 为模块中创建线程和队列实例。
   */
  virtual CWorkerQueue *create_worker_queue(void) const;

  /**
   * 删除线程和队列实例。
   */
  virtual void destory_worker_queue(CWorkerQueue *) const;

  /**
   * 获取全局会话管理对象实例数量。
   */
  virtual int get_session_mgt_num(void) const;

  /**
   * 获取全局会话管理对象实例。
   */
  virtual CSessionMgt *get_session_mgt(int worker_no) const;

  /**
   * 获取协议解析名对应的协议类型。
   */
  virtual int get_parser_name_type(const char *) const;

  /**
   * 获取协议解析对象对应的协议类型。
   */
  virtual int get_parser_type(const CParser *) const;

  /**
   * 获取事件上传对象。
   */
  virtual CUpload *get_upload_from_parser(const CParser *, const char *);

  virtual CUpload *get_upload_from_name(const char *upload_name);

  /**
   * 日志输出详细程度
   */
  virtual int get_verbose(int type = CONF_VB_GOBAL) const;

  /**
   * 转换上传模式
   */
  static int get_upload_mode(const char *name);

  /**
   * 设置进程退出标识
   */
  virtual void set_gwparser_exit();

  /**
   * 获取进程退出标识
   */
  virtual int get_gwparser_exit();

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *);

  virtual void set_myself_to_module(void);

  virtual int gwhw_parser_status();

protected:
  static void* update_current_gw_time(void *arg_ptr);
  void update_current_gw_ts();

  void eaelier_init_logger_and_sessionmgt();
  void init_module();

  friend class CGwMain;
  std::vector<CUpload *> m_vec_upload;
  // 实例所在SO库的序号
  std::vector<int> m_vec_upload_so;
  std::vector<CSource *> m_vec_source;
  // 实例所在SO库的序号
  std::vector<int> m_vec_source_so;
  std::vector<CParser *> m_vec_parser;
  // 实例所在SO库的序号
  std::vector<int> m_vec_parser_so;
  // 实例对应的类型ID，当不存在时类型ID为-1
  std::vector<int> m_vec_parser_type;
  std::map<const CParser *, int> m_map_parser_type;

  std::vector<CSessionMgt *> m_vec_session_mgt;

  CGwLicense *m_gw_license;
  CInteractiveService *m_interactive_service;
  CGwStats *m_gw_stats;
  CGwConfig *m_gw_config;
  CGwLogger *m_gw_logger;
  CWatchDog *m_watch_dog;
  CMonConf *m_mon_conf;
  CObCfg *m_ob_cfg;
  CFilterRule *m_ip_filter_rule;
  CFilterRule *m_port_filter_rule;
  CFilterRule *m_url_filter_rule;
  CFilterRule *m_user_filter_rule;

  CFilterRule *m_upload_client_ip_filter_rule;
  CFilterRule *m_upload_server_ip_filter_rule;
  CNacosListen *m_nacos_listen;
  CMinioUpload *m_minio_upload;

  // 解析器名称（缩写）对应的类型ID
  std::map<const std::string, int> m_map_parser_name_type;

protected:
  typedef std::vector<CParser *> parser_dep_value_t;
  typedef std::map<CParser *, parser_dep_value_t> parser_dep_map_t;
  // 协议解析依赖关系
  parser_dep_map_t m_map_parser_dep; // tcp->http,ssl; ssl->http
  // 协议解析数据依赖关系
  parser_dep_map_t m_map_parser_data_dep; // http->tcp,ssl; ssl->tcp

  typedef std::map<const CParser *, CUpload *> parser_upload_map_t;

  parser_upload_map_t m_map_parser_upload;

public:
  //std::vector<CParser *>& get_parser_data_dep(CParser *p) const;
  void get_parser_data_dep(CParser *p, std::vector<CParser *>& vp) const;

protected:
  int m_conf_verbose;
  int m_conf_ssl_verbose;

  pthread_t m_thread_time_produce;
  time_t m_time_ts;
  uint64_t m_time_ms;
  uint64_t m_real_time_ts;
  uint64_t m_real_time_ms;
  std::string m_str_logger_level;

private:
  int m_i_exit_flag;
  int m_max_session_num;
  int m_session_mgt_disable_timeout_check;
};

// 从类名取出对应的解析协议名称
std::string get_proto_string_from_parser_name(const std::string &srcStr);

#endif // __GWCOMMON_H__
