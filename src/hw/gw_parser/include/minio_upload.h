#ifndef __MINIO_UPLOAD_H__
#define __MINIO_UPLOAD_H__

#include <pthread.h>
#include <string>
#include "file_info_deal.h"

class CGwCommon;
namespace Aws{
    namespace S3 {
        class S3Client;
    };
    class SDKOptions;
};

class CMinioUpload : public FileUpload
{
    public:
        CMinioUpload();
        virtual ~CMinioUpload();

        virtual void init(void);
        virtual void fini(void);
        virtual void run();
        virtual void set_quit_signal(void);
        virtual void wait_for_stop(void);
       
        virtual void set_gw_common(CGwCommon *comm);
        virtual bool load_conf(const char *);
        virtual upload_stats_t upload_file(const char *p_file_data, size_t file_len, const char *p_file_name);
        void modify_status(int enable);
        virtual std::string get_file_sha256(const char* data, const uint32_t data_len);
    
    private:
        void new_client();
        static int report_check_bucket(void *arg);
        void check_bucket();
    private:
        volatile int m_quit_signal;
        pthread_t m_minio_check_bucket;
        int m_check_bucker_thread_stats;
        CGwCommon *m_comm;
        std::string m_str_endpoint;
        std::string m_str_access_key;
        std::string m_str_secret_key;
        std::string m_str_buckets;
        Aws::SDKOptions *m_options;
        static thread_local Aws::S3::S3Client *m_client; 
        int m_upload_enable;
        bool m_is_bucket_fixed;
};

std::string md5sum(const char*, size_t);

#endif // __MINIO_UPLOAD_H__