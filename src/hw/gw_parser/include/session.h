/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __SESSION_H__
#define __SESSION_H__

#include <time.h>
#include <inttypes.h>

#include <map>

#include "utils.h"

// session最多支持协议的数量
#define MAX_SESSION_PROTO_TYPE_NUM (16)
#define SESSION_PROTO_TYPE_TCP (0)

class CTcpParser;
class CParser;
struct StreamData;

class CSessionMgt;
class CGwCommon;

struct search_func_param;

class CSession
{
public:
  CSession();
  virtual ~CSession();

public:
  /**
   * 获取当前有效数据
   * @param int *datalen
   */
  virtual const char *get_data(CParser *pp, int dir, int *data_len, int *offset_out);

  /**
   * 设置当前未处理字节流的偏移。
   * @param CParser *pp
   * @param int num
   */
  virtual bool discard(CParser *pp, int dir, int num);

  /**
   * 设置当前未处理字节流的偏移，并更新当前缓冲区。
   * @param CParser *pp
   * @param int num
   */
  virtual bool discard_and_update(CParser *pp, int dir, int num);


  virtual void set_header_complete();

  virtual void set_tcp_direction_confirmed();

  virtual void init();

  virtual void fini();

  virtual void del_data();

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 设置会话管理对象。
   * @param CSessionMgt *psm
   */
  virtual void set_session_mgt(CSessionMgt *psm);

  /**
   * 获取会话管理对象。
   */
  virtual CSessionMgt *get_session_mgt(void) const;

  /**
   * 关闭tcp连接，不再解析数据。
   * @param CGwCommon *comm
   */
  virtual void tcp_drop_data(CParser *pp, int drop_reason = 0);

  /**
   * 从会话中获取解析出关联的数据。
   * @param CParser *parser
   */
  virtual StreamData *get_stream_data_from_parser(CParser *parser);


  /**
   * 从会话中获取解析出关联的数据。
   * @param int type
   */
  virtual StreamData *get_stream_data_from_type(int type);

  /**
   * 是否含有指定网络协议类型的解析数据。
   * @param int type
   */
  virtual bool has_proto_type(int type);

  /**
   * 设置解析对象到会话中。
   * @param const ConnData *conn
   * @param CParser *parser
   * @param StreamData* data
   */
  virtual bool set_parser(CParser *parser, StreamData *data);

  /**
   * 根据type设置解析对像到会话中。
   * @param int type
   * @param CParser *parser
   * @param StreamData *data
   */
  virtual bool set_parser_by_type(int type, CParser *parser, StreamData *data);

  /**
   * 更新刷新时间
   */
  virtual void update_time(void);

  /**
   * 取出当前连接信息
   */
  virtual const ConnData *get_conn(void) const;

  /**
   * 取出当前连接的时间戳信息
   */
  virtual double get_ts(void) const;

  /**
   * 取出当前连接的时间戳信息(毫秒级)
   */
  virtual int64_t get_ts_ms(void) const;

  /**
   * 创建新对象
   */
  virtual CSession *create(void) const;

  /**
   * 释放对象
   */
  virtual void release(void);

  /**
   * 克隆对象到队列中使用
   * @param CSession *
   */
  virtual void clone(CSession *);

  /**
   * 重置协议解析调用链
   */
  virtual bool reset_parser_chain(void);

  /**
   * 协议解析调用链--获取调用关系
   * @param const CParser *
   * @param CParser *&
   */
  bool parser_chain_get(const CParser *, CParser *&);

  /**
   * 协议解析调用链--增加协议调用关系
   * @param const CParser *
   * @param CParser *
   */
  bool parser_chain_add(const CParser *, CParser *);

  virtual void set_reverse(bool reverse) {
    this->m_reverse = reverse;
  }

  virtual bool get_reverse() {
    return this->m_reverse;
  }

  virtual void clone_tcp_stream(CSession *);

  virtual void set_m_proto_data(int num, StreamData *p){
    this->m_proto_data[num].data = p;
  }

protected:
  std::map<const CParser*, CParser*> m_map_parser_chain;

protected:
  enum
  {
    HASH_DELETE = -1,
    HASH_DATA = 1
  };

public:
  bool has_data()
  {
    return (stat == HASH_DATA);
  }

protected:
  void search_func(CParser *pp, search_func_param*);

protected:
  int stat; // DATA or DELETE

protected:
  friend class CSessionMgt;
  CSessionMgt *m_psm;
  ConnData m_con;
  time_t m_last_update;
  bool m_reverse;

  // TODO 超时链表指针
  // CSession *prev;
  // CSession *next;

  // 引用对象在队列中使用
  CTcpParser *m_tcp_parser;
  // 克隆对象到队列中使用
  StreamData *m_tcp_stream_data;

  typedef struct
  {
    union
    {
      CParser *parser;
      CTcpParser *tcp_parser;
    };
    StreamData *data;
  } session_ext_parser_data_t;

  // 目前不支持同一种协议在解析过程中进行嵌套使用
  // 缓解措施: 将协议解析库编译成功能相同协议名称不同的动态库，分别指定类型编号，并建立依赖关系；
  session_ext_parser_data_t m_proto_data[MAX_SESSION_PROTO_TYPE_NUM];

protected:
  class CGwCommon *m_comm;

public:
  uint16_t m_vlan_id;
  uint16_t m_probe_cnt; // 探测次数
};

#endif // __SESSION_H__
