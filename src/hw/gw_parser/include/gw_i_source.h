/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __SOURCE_H__
#define __SOURCE_H__
#include <string>
#include "cap_data.h"

// #include <stdint.h>


// typedef struct pkt_info
// {
//   int o_st; // 返回当前数据包的状态码， 用于tcp分组用, 0表表示丢弃; 1表示第一组; 2表示第二组; 3表示第三组; 最多分成8组
//   void *buf;
//   unsigned int size;
//   unsigned int pkt_size;
// } pkt_info_t;

// typedef int(fastcall *CALLBACK_PKT)(pkt_info_t *, unsigned int, void *);

// typedef struct pcap_info
// {
//   void *buf;
//   uint32_t timestamp;
//   uint32_t microseconds;
//   uint32_t packet_length;
//   uint32_t packet_length_wire;
// } pcap_info_t;

// typedef void(fastcall *CALLBACK_PCAP)(pcap_info_t *, void *, int);

// typedef int(fastcall *CALLBACK_SAMPLE)(void *);

// struct eth_info_t;

class CGwCommon;
class CTcpParser;
struct gw_parser_stats_t ;

class CSource
{
public:
  virtual ~CSource(void) = 0;

public:

  /*
   * 清理任务队列到缓存
   */
  virtual void cache_clean() = 0;

  /**
   * 模块初始时调用
   */
  virtual void init() = 0;

  /**
   * 模块结束时调用
   */
  virtual void fini() = 0;

  /**
   * 运行调用
   */
  virtual void run() = 0;

  /**
   * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
   */
  virtual const char *get_name(void) const = 0;

  /**
   * 获取版本号。
   */
  virtual const char *get_version(void) const = 0;

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm) = 0;

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *) = 0;

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void) = 0;

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void) = 0;

  /**
   * 设置IP层回调函数（通常为单线程）
   * @param CALLBACK_PKT cb_ip
   * @param void *userdata_ip
   */
  virtual int set_ip_callback(CALLBACK_PKT cb_ip, void *userdata_ip) = 0;

  /**
   * 设置TCP层回调函数（一般为多线程）
   * @param CALLBACK_PCAP cb_tcp
   * @param void *userdata_tcp
   */
  virtual int set_tcp_callback(CALLBACK_PCAP cb_tcp, void *userdata_tcp) = 0;

  /**
   * 设置采样参数（仅针对DPDK）
   * @param int mode
   * @param int rate
   * @param CALLBACK_SAMPLE cb_sample
   */
  virtual void set_sample_callback(int mode, int rate, CALLBACK_SAMPLE cb_sample) = 0;
  
  /*
* 设置转发回调函数
*
*/
  virtual void set_flow_callback(CALLBACK_FLOW) = 0;

  /**
   * 收集网口信息（针对DPDK）
   * @param eth_info_t *p_st_eth_info
   * @param uint32_t *p_work_port_cnt
   */
  virtual int collect_eth_info(eth_info_t *p_st_eth_info, uint32_t *p_work_port_cnt) = 0;

  /**
   *  收集解析pcap文件信息(针对pcap) 
   *  @param pcap_probe_info_t *p_st_probe_info
   */
  virtual int collect_pcap_probe_info(pcap_probe_info_t *p_st_probe_info) = 0;

  /**
   *  根据派生类具体类型，收集信息
   * 
   */
  virtual int collect_info(gw_parser_stats_t *parser_stats) = 0;

  /**
   * 设置tcp_parser 
   */
  virtual void set_tcp_parser(CTcpParser *tcp_parser) = 0;

  /**
   * 获取日志输出信息
   */
  virtual void get_log_buf(char *log_buf, size_t log_buf_len) = 0;

  virtual int source_status() const = 0;

protected:
//   CGwCommon *m_comm;
//   volatile int m_quit_signal;
//   char m_name[32];

};

#endif // __SOURCE_H__