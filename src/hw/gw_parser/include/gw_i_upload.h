/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __UPLOAD_H__
#define __UPLOAD_H__

class CGwCommon;
struct UploadMsg;

class CUpload
{
public:
  virtual ~CUpload(void) = 0;

public:

  virtual void cache_clean() = 0;
  /**
   * 将消息推送到上传线程队列中。
   * @param UploadMsg*
   */
  virtual void put_msg(UploadMsg *) = 0;

  virtual void init() = 0;

  virtual void fini() = 0;

  virtual void run() = 0;

  /**
   * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
   */
  virtual const char *get_name(void) const = 0;

  /**
   * 获取版本号。
   */
  virtual const char *get_version(void) const = 0;

  /**
   * 设置全局公共类对象实例。
   * @param CGwCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm) = 0;

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *) = 0;

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void) = 0;

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void) = 0;

  virtual int upload_status() const = 0;
  // protected:
  //   CGwCommon *m_comm;
  //   volatile int m_quit_signal;
  //   char m_name[32];
};

#endif // __UPLOAD_H__