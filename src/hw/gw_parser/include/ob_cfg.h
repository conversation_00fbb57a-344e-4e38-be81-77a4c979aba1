/**
 * Project gw-hw
 * <AUTHOR>
 * @version 
 */

#ifndef __OB_CFG_H__
#define __OB_CFG_H__

#include <string>
#include <pthread.h>

class CGwCommon;

class CObCfg
{
public:
    CObCfg(void);
    virtual ~CObCfg(void);

    virtual void init();

    virtual void fini();

    virtual void run();

    virtual void set_gw_common(CGwCommon *comm);

    virtual void set_quit_signal(void);

    virtual void wait_for_stop(void);


private:
    std::string m_str_conf_filepath;

    std::string m_str_traffic_source_filepath;

    volatile int m_quit_signal;
    int m_notify_fd;
    CGwCommon *m_comm;
    int m_routine_ob_cfg_stat;
    pthread_t m_pthread_ob_cfg;

    bool load_conf(void);
    
    static int thread_routine_ob_cfg(void *arg_ptr);
    int routine_ob_conf();
    
    void update_cfg(void);
};

#endif //__OB_CFG_H__