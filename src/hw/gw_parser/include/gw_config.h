/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __GW_CONFIG_H__
#define __GW_CONFIG_H__

#include <string>

struct cJSON;

class CGwCommon;

class CGwConfig
{
public:
  CGwConfig(void);
  virtual ~CGwConfig(void);

public:
  virtual std::string read_conf_string(const char *section, const char *key) const;
  virtual int read_conf_int(const char *section, const char *key, int def) const;
  virtual int64_t read_conf_int_x(const char *section, const char *key, int64_t def) const;
  virtual int read_conf_array(const char *section, const char *key, int *array_size, char ***ppp_array_value) const;
  virtual int read_conf_array(const char *section, const char *key, int *array_size, 
    const char* key1, char*** ppp_array_key1_value,
    const char* key2, char*** ppp_array_key2_value);
  virtual int get_array_size(const char *section, const char *key, cJSON **obj_parser_key_);

  virtual void init();

  virtual void fini();

  /**
   * 设置配置文件路径。
   * @param const char *filename
   */
  virtual void set_config_path(const char *filename);

  /**
   * 获取配置文件路径 
   * @param const char*
   */
  virtual const char* get_config_path();

  /**
   * 获取JSON对象 
   */
  virtual const cJSON * get_json_conf();

  /**
   * @param const char* section
   * @param const char* key
   * @param void* value
   * @param int value_size
   */
  virtual int get_value(const char *section, const char *key, void *value, int value_size) const;

  /**
   * @param const char* section
   * @param const char* key
   * @param void* value
   */
  virtual int set_value(const char *section, const char *key, void *value);

  /**
   * 加载配置文件
   */
  virtual bool load(void);

  /**
   * 保存配置文件
   */
  virtual bool save(void);

  /**
   * 加载配置字符串
   * @param const char *
   */
  virtual bool load_string(const char *);

  /**
   * 保存配置字符串
   * @param char *
   * @param size_t
   */
  virtual bool save_string(char *, size_t);

  /**
   * 设置全局公共类对象实例。
   * @param CCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  /**
   * 获取json节点对象
   * @param const char *
   */
  virtual cJSON *get_section(const char *sec);

  virtual void free_array(char **pp_array, int k) const;
  
  bool test_find_section(const char *sec);

protected:
  bool match_section(cJSON *n, const char *s) const;

  /**
   * 查找节点
   # @param cJSON *
   # @param const char *
   */
  cJSON *find_section(cJSON *node, const char *sec) const;

protected:
  CGwCommon *m_comm;
  const char *m_conf_filename;
  cJSON *m_json_conf;

protected:
  cJSON *read_conf(const char *filename);

};

#endif // __GW_CONFIG_H__
