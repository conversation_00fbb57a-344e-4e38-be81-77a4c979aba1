/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __INTERACTIVESERVICE_H__
#define __INTERACTIVESERVICE_H__

#include <stdint.h>
#include <pthread.h>

#include <string>
#include <mutex>

class CGwCommon;
class CTcpParser;
class CSource;
class CParser;

class CInteractiveService
{
public:
  CInteractiveService(void);
  virtual ~CInteractiveService(void);

public:
  virtual void init();

  virtual void fini();

  virtual void run();

  /**
   * 触发退出信号时处理
   */
  virtual void set_quit_signal(void);

  /**
   * 等待运行结束
   */
  virtual void wait_for_stop(void);

  /**
   * 设置全局公共类对象实例。
   * @param CCommon *comm
   */
  virtual void set_gw_common(CGwCommon *comm);

  virtual void set_tcp_parser(CTcpParser *tcpparser);

  /**
   * 加载配置参数（Json字符串，支持动态）。
   * @param const char *
   */
  virtual bool load_conf(const char *);

  /* 设置source 对象 */
  void set_source_obj(CSource *csource);

  /* 设置parser对象 */
  void set_parser_obj(CParser *cparser);

protected:
  static int recv_command_run(void *arg);
  int recv_command();
  void parser_command(const char* command_buf, char *log_buf, size_t log_buf_len);
  int get_help_info(const char* command_buf, char *log_buf, size_t log_buf_len);
  int get_source_log_buf(const char* command_buf, char *log_buf, size_t log_buf_len);
  int get_parser_log_buf(const char* command_buf, char *log_buf, size_t log_buf_len);
  int get_tcp_info(const char* command_buf, char *log_buf, size_t log_buf_len);
  int get_stats_info(const char* command_buf, char *log_buf, size_t log_buf_len);
  int get_gene_file_info(const char* command_buf, char *log_buf, size_t log_buf_len);
  int start_risk_parser(const char *command_buf, char *log_buf, size_t log_buf_len);
  int stop_risk_parser(const char *command_buf, char *log_buf, size_t log_buf_len);
  int do_cache_clean(const char* command_buf, char *log_buf, size_t log_buf_len);
  int get_task_status(const char* command_buf, char *log_buf, size_t log_buf_len);
  int change_to_rsikeval();
  int change_to_default();
  int change_mode(int mode);
  
  int get_gwhw_status(const char* command_buf, char *log_buf, size_t log_buf_len);
  int drop_http_file_event(const char* command_buf, char *log_buf, size_t log_buf_len);
  int save_http_file_event(const char* command_buf, char *log_buf, size_t log_buf_len);
  int add_control_strategy(const char* command_buf, char *log_buf, size_t log_buf_len);
  int del_control_strategy(const char* command_buf, char *log_buf, size_t log_buf_len);

protected:
  CGwCommon *m_comm;
  CTcpParser *m_tcp_parser;
  volatile int m_quit_signal;
  uint16_t m_u16_port;
  pthread_t m_pthread_recv_command;
  int m_recv_command_pthread_stats;
  int m_timeout_sec;
  int m_target_type;  /* 1:dpdk 2 agent 3 history 4 pfring*/
  int m_save_history; /* 0: not save 1: save */
  std::string m_str_save_path;
  int m_command_log_mode; // 0: off, 1: stderr, 2:LOG_INFO
  int m_gwhw_mode; // 0: default, 1: riskEval

  private:
    CSource *m_source_file;
    CSource *m_source_dpdk;
    CSource *m_source_nic;
    CParser *m_http_parser;
    CParser *m_ftp_parser;
    CParser *m_mail_parser;
    std::mutex m_mode_mutex;
};

#endif // __INTERACTIVESERVICE_H__
