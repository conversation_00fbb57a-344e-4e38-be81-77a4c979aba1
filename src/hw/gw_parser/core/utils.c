/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <arpa/inet.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/syscall.h>
#include <dirent.h>
#include <sys/stat.h>
#include <iconv.h>
#include <errno.h>

#include "utils.h"

#define DEF_GET_LE_UINT(x) uint##x##_t get_le_uint##x(const char* p) { \
    uint##x##_t r = 0; \
    int i = 0; \
    int len = sizeof(uint##x##_t); \
    for (;i<len;++i) { \
      r |= ((unsigned const char*)p)[i] << (i * 8); \
    } \
    return r; \
  }

DEF_GET_LE_UINT(8)
DEF_GET_LE_UINT(16)
DEF_GET_LE_UINT(32)
DEF_GET_LE_UINT(64)

#undef DEF_GET_LE_UINT

#define DEF_GET_BE_UINT(x) uint##x##_t get_be_uint##x(const char* p) { \
    uint##x##_t r = 0; \
    int i = 0; \
    int len = sizeof(uint##x##_t); \
    for (;i<len;++i) { \
      r <<= 8; \
      r |= ((unsigned const char*)p)[i]; \
    } \
    return r; \
  }

DEF_GET_BE_UINT(8)
DEF_GET_BE_UINT(16)
DEF_GET_BE_UINT(32)
DEF_GET_BE_UINT(64)

#undef DEF_GET_BE_UINT



#define B64_WS                  0xE0
#define B64_ERROR               0xFF
#define B64_NOT_BASE64(a)       (((a)|0x13) == 0xF3)
#define B64_BASE64(a)           (!B64_NOT_BASE64(a))

#define conv_bin2ascii(a) (data_bin2ascii[(a) & 0x3f])
#define gettid() syscall(SYS_gettid)

static const unsigned char data_bin2ascii[65] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
const char *g_except_dir[] = {".", "..", "/bin/", "/boot/", "/dev/", "/etc/", "/lib/", "/lib64/", "/media/", "/mnt/", "/proc/", "/root/", "/opt/", "/home/", "/run/", "/sbin/", "/sys/", "usr", "/var/"};

static const unsigned char data_ascii2bin[128] = {
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xE0, 0xF0, 0xFF, 0xFF, 0xF1, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xE0, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0xFF, 0xFF, 0x3E, 0xFF, 0xF2, 0xFF, 0x3F,
    0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B,
    0x3C, 0x3D, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF,
    0xFF, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
    0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E,
    0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
    0x17, 0x18, 0x19, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
    0xFF, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20,
    0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28,
    0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F, 0x30,
    0x31, 0x32, 0x33, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF,
};

static unsigned char conv_ascii2bin(unsigned char a)
{
    if (a & 0x80)
        return B64_ERROR;
    return data_ascii2bin[a];
}

void ntos(uint64_t u64_num, char *str, size_t *p_offset)
{   
    if (u64_num == 0)
    {
        *str = '0';
        (*p_offset)++;
        return;
    }
    size_t offset = 0;
    char *p_next = str;
    char *p_tmp = p_next;
    uint64_t n = 0;
    while (u64_num > 0 && (n = u64_num % 10) >= 0)
    {   
        *p_next++ = n + '0';
        u64_num /= 10;
        offset++;
    }

    while (p_tmp < --p_next)
    {
        char c = *p_tmp;
        *p_tmp = *p_next;
        *p_next = c;
        p_tmp++;
    }

    *p_offset += offset;

    return ;
}

void get_ip6addr_str(uint32_t *p_u_ipv6addr, char *p_buf, size_t size)
{
  memset(p_buf, 0, size);
  unsigned u_ip_1 = ntohl(p_u_ipv6addr[0]);
  unsigned u_ip_2 = ntohl(p_u_ipv6addr[1]);
  unsigned u_ip_3 = ntohl(p_u_ipv6addr[2]);
  unsigned u_ip_4 = ntohl(p_u_ipv6addr[3]);
  static const char format[] = "%x:%x:%x:%x:%x:%x:%x:%x";
  sprintf (p_buf, format, u_ip_1 >> 16, u_ip_1 & 0x0000ffff, u_ip_2 >> 16, u_ip_2 & 0x0000ffff, u_ip_3 >> 16, u_ip_3 & 0x0000ffff, u_ip_4 >> 16, u_ip_4 & 0x0000ffff);

  return;
}

inline int get_ms_timeval(uint64_t *p_u64_timaval)
{
  int i_ret = 0;
  struct timeval st_time;
  i_ret = gettimeofday(&st_time, NULL);
  if (i_ret != 0)
  {
    printf("get timeval failed(%d)\n", i_ret);
    return -1;
  }

  *p_u64_timaval = st_time.tv_sec * 1000 + st_time.tv_usec / 1000;
  return 0;
}

void get_unique_event_id(const char *p_gw_ip, uint64_t u64_ms_timeval, uint32_t u32_ms_index, char *p_unique_code, size_t unique_code_len)
{
  if (NULL == p_unique_code || unique_code_len == 0)
  {
    return ;
  }
  size_t offset = 0;
  size_t gw_ip_len = strlen(p_gw_ip);
  memcpy(p_unique_code, p_gw_ip, gw_ip_len);
  offset += gw_ip_len;
  *(p_unique_code + offset++) = '_';
  ntos(getpid(), p_unique_code + offset, &offset);
  *(p_unique_code + offset++) = '_';
  ntos(gettid(), p_unique_code + offset, &offset);
  *(p_unique_code + offset++) = '_';
  ntos(u64_ms_timeval, p_unique_code + offset, &offset);
  *(p_unique_code + offset++) = '_';
  ntos(u32_ms_index, p_unique_code + offset, &offset);
  //snprintf(p_unique_code, unique_code_len, "%s_%d_%ld_%lu_%u", p_gw_ip, getpid(), gettid(), u64_ms_timeval, u32_ms_index);

  return;
}

void base64_encode(unsigned char *p_encode_buf, const unsigned char *p_src_buf, int i_src_buf_len)
{
  int i = 0;
  unsigned u_tmp;

  for (i = i_src_buf_len; i > 0; i -= 3)
  {
    u_tmp = 0;
    if (i >= 3)
    {
      u_tmp = (((unsigned)p_src_buf[0]) << 16) | (((unsigned)p_src_buf[1]) << 8) | p_src_buf[2];
      *(p_encode_buf++) = conv_bin2ascii(u_tmp>>18);
      *(p_encode_buf++) = conv_bin2ascii(u_tmp>>12);
      *(p_encode_buf++) = conv_bin2ascii(u_tmp>>6);
      *(p_encode_buf++) = conv_bin2ascii(u_tmp);
    }
    else
    {
      u_tmp = ((unsigned)p_src_buf[0]) << 16;
      if (i == 2)
      {
        u_tmp |= (((unsigned)p_src_buf[1]) << 8);
      }

      *(p_encode_buf++) = conv_bin2ascii(u_tmp >> 18);
      *(p_encode_buf++) = conv_bin2ascii(u_tmp >> 12);
      *(p_encode_buf++) = (i == 1) ? '=' : conv_bin2ascii(u_tmp >> 6);
      *(p_encode_buf++) = '=';
    }

    p_src_buf += 3;
  }

  *p_encode_buf = '\0';

  return;
}

char *base64_decode(const unsigned char *p_src_buf, int src_len)
{
    int i, ret = 0, a, b, c, d;
    unsigned long l;
    char *p_buf = NULL;
    int de_len = (src_len / 4 + 1) * 3;

    /* trim white space from the start of the line. */
    while ((conv_ascii2bin(*p_src_buf) == B64_WS) && (src_len > 0)) {
        p_src_buf++;
        src_len--;
    }

    /*
     * strip off stuff at the end of the line ascii2bin values B64_WS,
     * B64_EOLN, B64_EOLN and B64_EOF
     */
    while ((src_len > 3) && (B64_NOT_BASE64(conv_ascii2bin(p_src_buf[src_len - 1]))))
        src_len--;

    if (src_len % 4 != 0)
        return NULL;

    p_buf = (char *)malloc(de_len + 1);
    memset(p_buf, 0, de_len + 1);
    char *p_tmp = p_buf;

    for (i = 0; i < src_len; i += 4) {
        a = conv_ascii2bin(*(p_src_buf++));
        b = conv_ascii2bin(*(p_src_buf++));
        c = conv_ascii2bin(*(p_src_buf++));
        d = conv_ascii2bin(*(p_src_buf++));
        if ((a & 0x80) || (b & 0x80) || (c & 0x80) || (d & 0x80))
        {
            if (p_buf)
            {
              free(p_buf);
            }
            return NULL;
        }
        l = ((((unsigned long)a) << 18L) |
             (((unsigned long)b) << 12L) |
             (((unsigned long)c) << 6L) | (((unsigned long)d)));
        *(p_tmp++) = (unsigned char)(l >> 16L) & 0xff;
        *(p_tmp++) = (unsigned char)(l >> 8L) & 0xff;
        *(p_tmp++) = (unsigned char)(l) & 0xff;
        ret += 3;
    }

    return p_buf;
}

void base64_decode_with_len(const unsigned char *p_src_buf, int src_len, 
                          char **pp_des_buf, int *p_des_len)
{
  *pp_des_buf = base64_decode(p_src_buf, src_len);
  assert(!(src_len % 4));
  int des_len = src_len / 4 * 3;
  if ('=' == p_src_buf[src_len - 1]) 
  {
    des_len--;
    if ('=' == p_src_buf[src_len - 2]) des_len--;
  }
  *p_des_len = des_len;
}

static const char * iconv_code_name(const char* code)
{
  if(!strncasecmp(code, "gb", strlen("gb")))
    return "gb18030";
  return code;
}

int convert(const char *fromCode, const char *toCode, st_conv_paramer *paramer) 
{
    paramer->original_inbuf = paramer->inbuf;
    paramer->original_outbuf = paramer->outbuf;
    iconv_t cd = 0;
    if ( (iconv_t)-1 == (cd = iconv_open(toCode, iconv_code_name(fromCode))) )
    {
        // GWLOG_ERROR(NULL), "iconv_open failed. errno: %d", errno);
        return errno;
    }
    if (-1 == iconv(cd, &paramer->inbuf, &paramer->inbufSize, &paramer->outbuf, &paramer->outbufSize))
    {
        // GWLOG_ERROR(NULL, "iconv failed. errno: %d", errno);
        iconv_close(cd);
        return errno;
    }
    if (-1 == iconv_close(cd))
    {
        // GWLOG_ERROR(NULL, "iconv_close failed. errno: ", errno);
        return errno;
    }
    return 0;
}

int convert2utf8(const char *fromCode, st_conv_paramer *paramer)
{
    return convert(fromCode, "utf-8", paramer);
}

char* utf16le_2_utf8(const char *from, const int from_l, int *utf8_l) 
{
    if (!from_l) {
        return NULL;
    }
    st_conv_paramer pa = {0};
    char* p_utf8 = (char*) malloc(from_l * 2);
    memset((void*)p_utf8, 0, from_l * 2);
    pa.inbuf = (char*) from;
    pa.inbufSize = from_l;
    pa.outbuf = p_utf8;
    pa.outbufSize = from_l * 2;
    convert2utf8("UTF-16LE", &pa);
    if (utf8_l)
    {
      *utf8_l = pa.outbufSize;
    }
    return p_utf8;
}

uint64_t current_time_ms(void)
{
    struct timeval tv;
    gettimeofday (&tv, NULL); 
    return tv.tv_sec * 1000 + tv.tv_usec/1000;
}

void dfs_remove_dir(const char* path_raw)
{
  DIR *cur_dir = opendir(path_raw);
  // int i_ret = 0;
  char child_path[PATH_MAX] = {0};
  struct dirent *ent = NULL;
  struct stat st;

  if (!cur_dir)
  {
    perror("remove_dir opendir:");
    return;
  }

  while ((ent = readdir(cur_dir)) != NULL)
  {
    
    if (strcmp(ent->d_name, ".") == 0 || strcmp(ent->d_name, "..") == 0)
    {
      continue;
    }

    snprintf(child_path, PATH_MAX - 1, "%s/%s", path_raw, ent->d_name);
    stat(child_path, &st);
   
    if (S_ISDIR(st.st_mode))
    {
      dfs_remove_dir(child_path);
    }
    
    remove(child_path);
  }

  closedir(cur_dir);
}

// 删除目录及子目录中的文件
void remove_dir(const char *path_raw)
{
  //char old_path[PATH_MAX];

  if (!path_raw)
  {
    return;
  }

  size_t except_dir_len = sizeof(g_except_dir) / sizeof(char*);
  int i = 0;
  for (i = 0; i < except_dir_len; ++i)
  {
    if (strcmp(path_raw, g_except_dir[i]) == 0)
    {
      return;
    }
  }

  dfs_remove_dir(path_raw);
}

const char *my_strstr(const char *p, size_t length, const char *s, size_t s_len)
{
  size_t n;
  int i;
  char ch;
  if (p == NULL || s == NULL || length == 0 || s_len == 0)
  {
    return NULL;
  }

  n = s_len;
  ch = s[0];
  for (i = length - n; i >= 0; i--, p++)
  {
    if (p[0] == ch && 0 == memcmp(p, s, n))
    {
      return p;
    }
  }
  return NULL;
}

const char *my_strchr(const char *dest, size_t dest_len, char src)
{
  if (NULL == dest || dest_len <= 0)
  {
    return NULL;
  }

  int i = 0;
  for (i = 0; i < dest_len; i++, dest++)
  {
    if (src == dest[0])
    {
        return dest;
    }
  }

  return NULL;
}











