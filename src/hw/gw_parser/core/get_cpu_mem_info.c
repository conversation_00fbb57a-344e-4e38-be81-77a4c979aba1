#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "get_cpu_mem_info.h"

#define PROCESS_ITEM (14)            /* 进程CPU时间开始的项数 */
#define VMRSS_LINE   (18)

/* linux系统/proc/stat文件下有整个系统的CPU运行时间 */
int get_cpu_total_occupy(uint32_t *p_cpu_total_time)
{
    FILE *fp = NULL;     
    char a_buf[1024];  /* 定义局部变量buff数组为char类型大小为1024 */
    memset(a_buf, 0, sizeof(a_buf));
    uint32_t u32_user = 0;        /* 从系统启动开始累计到当前时刻，处于用户态的运行时间，不包含nice值为负进程 */
    uint32_t u32_nice = 0;        /* 从系统启动开始累计到当前时刻，nice值为负的进程所占用的CPU时间 */
    uint32_t u32_system = 0;      /* 从系统启动开始累计到当前时刻，处于核心态的运行时间 */
    uint32_t u32_idle = 0;        /* 从系统启动开始累计到当前时刻，除IO等待时间以外的其它等待时间 */ 

    fp = fopen ("/proc/stat", "r"); 
    if (fp == NULL)
    {
        printf("open /proc/stat file failed\n");
        return -1;
    }
    fgets (a_buf, sizeof(a_buf), fp);    /* 从fd文件中读取长度为buff的字符串再存到起始地址为buff这个空间里 */
    char a_name[16];
    memset(a_name, 0, sizeof(a_name));
    sscanf (a_buf, "%s %u %u %u %u", a_name, &u32_user, &u32_nice, &u32_system, &u32_idle);

    fclose(fp);     
    *p_cpu_total_time = u32_user + u32_nice + u32_system + u32_idle;
    return 0;
}

const char* get_items(const char *p_line_buf, uint32_t u32_ignore_item_num)
{
    if (p_line_buf == NULL)
    {
        return NULL;
    }

    const char *p_tmp = p_line_buf;
    uint32_t u32_space_cnt = 0;
    if (u32_ignore_item_num == 1 || u32_ignore_item_num < 1)
    {
        return p_tmp;
    }

    while (*p_tmp != '\0')
    {
        if (*p_tmp == ' ')
        {
            u32_space_cnt++;
            if (u32_space_cnt == u32_ignore_item_num - 1)
            {
                p_tmp++;
                break;
            }
        }
        p_tmp++;
    }

    if (*p_tmp == '\0')
    {
        return NULL;
    }

    return p_tmp;
}

int get_cpu_process_occupy(const pid_t pid, uint32_t *p_u32_proc_cputime)
{
    char a_filename[64];
    memset(a_filename, 0, sizeof(a_filename));
    //printf("gw parser pid = %u\n", pid);
    FILE *fp = NULL;         
    char a_line_buf[1024];  /* 读取行的缓冲区 */
    memset(a_line_buf, 0, sizeof(a_line_buf));
    uint32_t u32_utime = 0;       /* 该任务在用户态运行的时间，单位为jiffies */
    uint32_t u32_stime = 0;       /* 该任务在核心态运行的时间，单位为jiffies */
    uint32_t u32_cutime = 0;      /* 所有已死线程在用户态运行的时间，单位为jiffies */
    uint32_t u32_cstime = 0;      /* 所有已死在核心态运行的时间，单位为jiffies */
    sprintf(a_filename, "/proc/%u/stat", pid); 
                                                                                                 
    fp = fopen (a_filename, "r"); 
    if (fp == NULL)
    {
        return -1;
    }
    fgets (a_line_buf, sizeof(a_line_buf), fp); 
    fclose(fp);
    const char* p_buf = NULL;
    p_buf = get_items(a_line_buf, PROCESS_ITEM);                                       /* 取得从第14项开始的起始指针 */
    if (p_buf == NULL)
    {
        printf ("get items failed\n");
        return -1;
    }
    sscanf(p_buf, "%u %u %u %u", &u32_utime, &u32_stime, &u32_cutime,&u32_cstime);     /* 格式化第14,15,16,17项 */
    
    *p_u32_proc_cputime = u32_utime + u32_stime + u32_cstime + u32_cutime;
    return 0;
}

void get_proc_cpu_usage(uint32_t u32_core_id_num, pid_t pid, float *p_f_cpu_usage)
{
    int i_ret = 0;
    uint32_t u32_pre_total_cputime = 0;
    uint32_t u32_back_total_cputime = 0;
    uint32_t u32_pre_proc_cputime = 0;
    uint32_t u32_back_proc_putime = 0;
    i_ret = get_cpu_total_occupy(&u32_pre_total_cputime);
    if (i_ret != 0)
    {
        return;
    }
    i_ret = get_cpu_process_occupy(pid, &u32_pre_proc_cputime);
    if (i_ret != 0)
    {
        return ;
    }
    usleep(1000000);     /* 延迟1秒 */
    i_ret = get_cpu_total_occupy(&u32_back_total_cputime);
    if (i_ret != 0)
    {
        return ;
    }
    i_ret = get_cpu_process_occupy(pid, &u32_back_proc_putime);
    if (i_ret != 0)
    {
        return ;
    }
    //printf("pre_proc_cputime = %u, back_proc_cputime = %u\n", u32_pre_proc_cputime, u32_back_proc_putime);
    //printf("pre_total_cputime = %u, beck_total_cputime = %u\n", u32_pre_total_cputime, u32_back_total_cputime);
    *p_f_cpu_usage = 100.0 * u32_core_id_num * (u32_back_proc_putime - u32_pre_proc_cputime) / (u32_back_total_cputime - u32_pre_total_cputime);
    //printf("cpu uasge = %.3f\n", *p_f_cpu_usage);
}

int get_total_mem(uint32_t *p_u32_total_mem)
 {
    FILE *fp = NULL;         
    char a_line_buf[256];
    memset(a_line_buf, 0, sizeof(a_line_buf));                                                                                              
    fp = fopen ("/proc/meminfo", "r"); 
    if (fp == NULL)
    {
        return -1;
    }

    char a_name[32];
    memset(a_name, 0, sizeof(a_name));
    // uint32_t u32_memtotal;//存放内存峰值大小
    fgets (a_line_buf, sizeof(a_line_buf), fp);//读取memtotal这一行的数据,memtotal在第1行
    sscanf (a_line_buf, "%s %d", a_name, p_u32_total_mem);
    fclose(fp);     //关闭文件fd
    return 0;
}

int get_phy_mem(const pid_t pid, uint32_t *p_u32_proc_meminfo)
{
    char a_filename[64];
    memset(a_filename, 0, sizeof(a_filename));
    FILE *fp = NULL;         
    char a_line_buf[256]; 
    memset(a_line_buf, 0, sizeof(a_line_buf));
    sprintf(a_filename, "/proc/%d/status", pid);
                                                                                                
    fp = fopen (a_filename, "r"); 
    if (fp == NULL)
    {
        return -1;
    }

    /* 获取vmrss:实际物理内存占用 */
    char a_name[32];
    int i = 0;
    memset(a_name, 0, sizeof(a_name));
    for (i = 0; i < VMRSS_LINE; i++)
    {
        /* 读到18行 */        
        fgets (a_line_buf, sizeof(a_line_buf), fp);
    }

    sscanf (a_line_buf, "%s %d", a_name, p_u32_proc_meminfo);
    fclose(fp);     //关闭文件fd
    return 0;
}

/* param: pid :IN: 进程号 */
/*        p_f_mem_usage :OUT: 内存占用率 */
void get_proc_mem_usage(pid_t pid, float *p_f_mem_usage)
{
    int i_ret = 0;
    uint32_t u32_total_meminfo = 0;
    uint32_t u32_proc_meminfo = 0;
    i_ret = get_phy_mem(pid, &u32_proc_meminfo);
    if (i_ret != 0)
    {
        printf("get proc meminfo failed\n");
        return;
    }
    i_ret = get_total_mem(&u32_total_meminfo);
    if (i_ret != 0)
    {
        printf("get total meminfo failed\n");
        return;
    }

    *p_f_mem_usage = 100.0 * (u32_proc_meminfo * 1.0) / (u32_total_meminfo * 1.0); 
}