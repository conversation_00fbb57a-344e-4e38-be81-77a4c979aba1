#include <unistd.h>
#ifdef __GNUC__
#pragma GCC visibility push(default)
#endif
    #include <iostream>
    #include "aws/core/auth/AWSCredentialsProvider.h"
    #include "aws/s3/model/PutObjectRequest.h"
    #include "aws/s3/model/CreateBucketRequest.h"
    #include "aws/core/utils/HashingUtils.h"
#ifdef __GNUC__
#pragma GCC visibility pop
#endif
#include "utils.h"
#include "gw_common.h"
#include "gw_config.h"
#include "gw_logger.h"
#include "minio_upload.h"
#include "aws/core/Aws.h"
#include "aws/s3/S3Client.h"
#include <sys/time.h>

using namespace Aws::S3;
using namespace Aws::S3::Model;
using namespace std;
using Aws::SDKOptions; 

#define SLEEP_SECOND   (60)
#define SHA_FILE_LEN (64)
#define SHA_DIR_LEN (2)
#define MD5_FILE_LEN (32)

thread_local Aws::S3::S3Client *CMinioUpload::m_client; 

CMinioUpload::CMinioUpload(): m_quit_signal(0)
                            , m_check_bucker_thread_stats(0)
                            , m_comm(nullptr)
                            , m_options(new SDKOptions())
                            , m_upload_enable(0)
                            , m_is_bucket_fixed(true)
                            //, m_client(nullptr)
{
    m_options->loggingOptions.logLevel = Aws::Utils::Logging::LogLevel::Off;
}

CMinioUpload::~CMinioUpload()
{
    fini();
}

void CMinioUpload::init()
{
    if (m_comm == nullptr)
    {
        return;
    } 

    load_conf(nullptr);
    Aws::InitAPI(*m_options);
    new_client();

    return;
}

void CMinioUpload::fini()
{
    if (m_client)
    {
        delete m_client;
        m_client = nullptr;
    }

    if (m_options)
    {
        Aws::ShutdownAPI(*m_options);
        delete m_options;
        m_options = nullptr;
    }
}

void CMinioUpload::run()
{
    if (m_check_bucker_thread_stats == 1)
    {
        return ;
    }

    int i_ret = pthread_create(&m_minio_check_bucket, NULL, (void *(*)(void *))report_check_bucket, this);
    if (i_ret != 0)
    {
        GWLOG_ERROR(m_comm, "pthread create minio check bucket thread failed\n");
    }
    else
    {
        m_check_bucker_thread_stats = 1;
        GWLOG_INFO(m_comm, "minio check bucket thread create successfully\n");
    }

    return;
}

void CMinioUpload::set_quit_signal(void)
{
    m_quit_signal = 1;

    return;
}

void CMinioUpload::wait_for_stop(void)
{
    if (m_check_bucker_thread_stats == 1)
    {
        pthread_join(m_minio_check_bucket, NULL);
    }

    return;
}

void CMinioUpload::set_gw_common(CGwCommon *comm)
{
    m_comm = comm;
}

bool CMinioUpload::load_conf(const char *)
{
    CGwConfig *pgwc = m_comm->get_gw_config();
    if (pgwc == NULL)
    {
        return false;
    }

    m_str_endpoint = pgwc->read_conf_string("minio", "endpoint");
    m_str_access_key = pgwc->read_conf_string("minio", "access_key");
    m_str_secret_key = pgwc->read_conf_string("minio", "secret_key");
    m_str_buckets = pgwc->read_conf_string("minio","buckets");
    m_upload_enable = pgwc->read_conf_int("minio", "enable", m_upload_enable);

    GWLOG_INFO(m_comm, "endpoint = %s\n", m_str_endpoint.c_str());
    GWLOG_INFO(m_comm, "access key = %s\n", m_str_access_key.c_str());
    GWLOG_INFO(m_comm, "secret key = %s\n", m_str_secret_key.c_str());
    GWLOG_INFO(m_comm, "buckets = %s\n", m_str_buckets.c_str());

    if (m_str_buckets.length() == 0)
    {
        m_is_bucket_fixed = false;
    }

    return true;
}

void CMinioUpload::modify_status(int enable) {
    GWLOG_INFO(m_comm, "CMinioUpload modify status: %d\n", enable);
    if (enable > 1)
        enable = 1;
    __sync_val_compare_and_swap(&m_upload_enable, enable^1, enable);
}

std::string CMinioUpload::get_file_sha256(const char* data, const uint32_t data_len)
{
    Aws::String str_body(data, data_len);
    auto digest = Aws::Utils::HashingUtils::CalculateSHA256(str_body);
    size_t length = digest.GetLength();
    char buf[SHA_FILE_LEN] = {0};
    for (size_t i = 0; i < length; i++)
    {
        sprintf(buf+ (i * 2), "%02x", digest[i]);
    }

    return buf;
}

void CMinioUpload::new_client()
{
    if (m_str_endpoint.empty())
    {
        return;
    }

    Aws::Client::ClientConfiguration cfg;
    cfg.endpointOverride = m_str_endpoint.c_str();
    cfg.scheme = Aws::Http::Scheme::HTTP;
    cfg.verifySSL = false;

    Aws::Auth::AWSCredentials cred(m_str_access_key.c_str(), m_str_secret_key.c_str());  // 认证的Key
    m_client = new S3Client(cred, cfg, false, false);
    if (m_client == NULL)
    {
        return;
    }

    return;
}

upload_stats_t CMinioUpload::upload_file(const char *p_file_data, size_t file_len, const char *p_file_name)
{
    upload_stats_t st_upload_stats;

    st_upload_stats.bucket_name = m_str_buckets;

    if (!m_upload_enable) {
        st_upload_stats.ret = -1;
        return st_upload_stats;
    }

    if (!m_client)
    {
        new_client();
    }
    
    if (!m_client)
    {
        st_upload_stats.ret = -1;
        GWLOG_ERROR(m_comm, "s3 client object invaild\n");

        return st_upload_stats;
    }

    Aws::String str_body(p_file_data, file_len);
    std::string file_sha256 = get_file_sha256(p_file_data, file_len);

    std::string str_dir(file_sha256.c_str(), SHA_DIR_LEN);
    std::string str_child_dir(file_sha256.c_str() + 2, SHA_DIR_LEN);
    //str_file = str_file + p_file_name;
    std::string str_path = str_dir + "/" + str_child_dir + "/" + file_sha256;

    PutObjectRequest out_obj_request;
    out_obj_request.SetBucket(m_str_buckets.c_str());
    out_obj_request.SetKey(str_path.c_str());
    auto input_data = Aws::MakeShared<Aws::StringStream>("gwhw");
    *input_data << str_body;
    out_obj_request.SetBody(input_data);
    auto put_object_result = m_client->PutObject(out_obj_request);

    if (!put_object_result.IsSuccess())
    {
        GWLOG_ERROR(m_comm, "upload failed, (buckets = %s), (key = %s), (message = %s)\n", m_str_buckets.c_str()
                                                                                         , str_path.c_str()
                                                                                         , put_object_result.GetError().GetMessage().c_str());

        st_upload_stats.ret = -1;

        return st_upload_stats;
    }

    st_upload_stats.ret = 0;
    st_upload_stats.str_file_path = std::move(str_path);
    st_upload_stats.str_sha256 = file_sha256;
    
    return st_upload_stats;
}

int CMinioUpload::report_check_bucket(void *arg)
{
    CMinioUpload *p_this = (CMinioUpload*)arg;
    p_this->check_bucket();
    
    return 0;
}

void CMinioUpload::check_bucket()
{
    new_client();
    if (m_client == nullptr)
    {
        return;
    }

    time_t timestamp;
    struct tm time_info;
    char buf[16] = {0};//gwhw-yyyy-mm-dd
    while (!m_quit_signal)
    {
        if (!m_upload_enable) {
            sleep(SLEEP_SECOND);
            continue;
        }
        bool is_find_buckets = false;
        /* 判断gwhw buckets是否存在，不存在则创建 */
        auto response = m_client->ListBuckets();
        if (!response.IsSuccess())
        {
            GWLOG_ERROR(m_comm, "Error while ListBuckets %s, %s\n", response.GetError().GetExceptionName().c_str(), response.GetError().GetMessage().c_str());
            sleep(SLEEP_SECOND);
            continue;
        }

        if (false == m_is_bucket_fixed)
        {
            time(&timestamp);
            localtime_r(&timestamp, &time_info);
            snprintf(buf, sizeof(buf), "gwhw-%04d-%02d-%02d",
                     time_info.tm_year + 1900,
                     time_info.tm_mon + 1,
                     time_info.tm_mday);
            m_str_buckets = buf;
        }
        
        auto buckets = response.GetResult().GetBuckets();
        for (auto iter = buckets.cbegin(); iter != buckets.cend(); ++iter)
        {
            if ((iter->GetName()).size() == m_str_buckets.size() && memcmp((iter->GetName()).c_str(), m_str_buckets.c_str(), m_str_buckets.size()) == 0)
            {
                is_find_buckets = true;
                break;
            }
        }
        
        if (!is_find_buckets)
        {
            /* 创建buckets */
            CreateBucketRequest create_bucket_req;
            create_bucket_req.WithBucket(m_str_buckets.c_str());
            auto response = m_client->CreateBucket(create_bucket_req);
            if (!response.IsSuccess())
            {
                GWLOG_ERROR(m_comm, "create buckets(%s) faield, reason(%s)\n", m_str_buckets.c_str(), response.GetError().GetMessage().c_str());
                sleep(SLEEP_SECOND);
                continue;
            }
        }

        sleep(SLEEP_SECOND);
    }

    fini();
    return;
}

std::string md5sum(const char* p, size_t l)
{
    if (!p || l == 0) 
    {
        return "";
    }
    Aws::String str_body(p, l);
    auto digest = Aws::Utils::HashingUtils::CalculateMD5(str_body);
    size_t length = digest.GetLength();
    char buf[MD5_FILE_LEN] = {0};
    for (size_t i = 0; i < length; i++)
    {
        sprintf(buf+ (i * 2), "%02x", digest[i]);
    }
    return std::string(buf, MD5_FILE_LEN);
}