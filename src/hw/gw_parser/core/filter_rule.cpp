#include "filter_rule.h"
#include "gw_common.h"

CFilterRule::CFilterRule(void)
{

}

CFilterRule::~CFilterRule(void)
{

}

int CFilterRule::hit(unsigned data)
{
    return 0;
}

/**
 * 命中URL转发规则
 * @param const char *url
 */
int CFilterRule::forward(const char *url)
{
    return 0;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CFilterRule::set_gw_common(CGwCommon *comm)
{

}

/**
 *  设置TcpParser对象实例 
 *  @paramount CTcpParser *tcpparser
 */
void CFilterRule::set_tcp_parser(CTcpParser *tcpparser)
{
    
}

/**
 *  IP过滤参数动态更新 
 */
void CFilterRule::ip_filter_for_mon(int is_for_nacos, const char *s)
{
    
}

/**
 *  IP白名单参数动态更新 
 */
void CFilterRule::ip_white_for_mon(int is_for_nacos, const char *s)
{

}

/**
 *  PORT参数更新 
 */
void CFilterRule::port_filter_for_mon(int is_for_nacos, const char *s)
{

}

/**
 *  动态加载URL转发规则 
 *  @param const char*
 */
void CFilterRule::read_conf_forward_for_mon(const char* filename)
{

}

/**
 *  动态加载账号转发规则 
 *  @param const char*
 */
void CFilterRule::read_conf_user_info_for_mon(const char *filename)
{

}

void CFilterRule::init()
{

}

void CFilterRule::fini()
{
    
}