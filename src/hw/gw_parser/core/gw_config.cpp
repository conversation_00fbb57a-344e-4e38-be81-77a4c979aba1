/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <cstdlib>

#include "gw_config.h"

#include "gw_common.h"
#include "gw_logger.h"

#include "utils.h"

#include "cJSON.h"

/**
 * CGwConfig implementation
 *
 * 全局配置类。
 * 通过CJSonData对象读写。
 * 读写配置文件。
 */

CGwConfig::CGwConfig(void) : m_comm(NULL)
                           , m_conf_filename(NULL)
                           , m_json_conf(NULL)
{

}

CGwConfig::~CGwConfig(void)
{
  cJSON_Delete(m_json_conf);
  m_comm = NULL;
}

void CGwConfig::init()
{
  ASSERT(m_comm != NULL);
}

void CGwConfig::fini()
{
  ASSERT(m_comm != NULL);
}

/**
 * 设置配置文件路径。
 * @param const char *filename
 */
void CGwConfig::set_config_path(const char *filename)
{
  m_conf_filename = filename;

  // cJSON_Delete(m_json_conf);
  // m_json_conf = read_conf(m_conf_filename);
}

 /**
 * 获取配置文件路径 
 * @param const char*
 */
const char* CGwConfig::get_config_path()
{
  return m_conf_filename;
}

/**
 * 获取JSON对象 
 */
const cJSON * CGwConfig::get_json_conf()
{
  return m_json_conf;
}

bool CGwConfig::match_section(cJSON *n, const char *s) const
{
  return cJSON_IsObject(n) && n->string && !strcmp(n->string, s);
}

cJSON *CGwConfig::find_section(cJSON *node, const char *section) const
{
  if(node)
  {
    if (match_section(node, section))
    {
      return node;
    }

    cJSON *tmp;
    if (node->child)
    {
      tmp = find_section(node->child, section);
      if (match_section(tmp, section))
      {
        return tmp;
      }
    }

    while ((node = node->next) != NULL)
    {
      if (cJSON_IsObject(node))
      {
        tmp = find_section(node, section);
        if (match_section(tmp, section))
        {
          return tmp;
        }
      }
    }
  }
  return NULL;
}

/**
 * 获取json节点对象
 * @param const char *
 */
cJSON *CGwConfig::get_section(const char *sec)
{
  return find_section(m_json_conf, sec);
}

void CGwConfig::free_array(char **pp_array, int k) const
{
  if (pp_array == NULL || k == 0)
  {
    return;
  }

  for (int i = 0; i < k; i++)
  {
    free(pp_array[i]);
    pp_array[i] = NULL;
  }

  free(pp_array);
}

bool CGwConfig::test_find_section(const char *sec)
{
  cJSON *node = find_section(m_json_conf, sec);
  return node != NULL;
}

/**
 * @param const char* section
 * @param const char* key
 * @param void* value
 * @param int value_size
 */
int CGwConfig::get_value(const char *section, const char *key, void *value, int value_size) const
{
  unsigned int cnt = 0;
  cJSON *obj_parser;
  cJSON *obj_parser_key;

  if (m_json_conf == NULL)
  {
    return -1;
  }

  if (section)
  {
    obj_parser = find_section(m_json_conf, section);
    if (obj_parser == NULL)
    {
      return -100;
    }
  }
  else
  {
    obj_parser = m_json_conf;
  }

  obj_parser_key = cJSON_GetObjectItem(obj_parser, key);
  if (obj_parser_key == NULL)
  {
    return -200;
  }

  if (cJSON_IsString(obj_parser_key))
  {
    cnt = MIN((unsigned int)value_size, strlen(obj_parser_key->valuestring));
    memcpy(value, obj_parser_key->valuestring, cnt);

    GWLOG_DEBUG(m_comm, "***config get_value*** %s: %s   %s\n", key, obj_parser_key->valuestring, value);
  }
  else if (cJSON_IsNumber(obj_parser_key))
  {
    snprintf((char*)value, value_size - 1, "%lf", obj_parser_key->valuedouble);
    cnt = strlen((char*)value);
    // TODO Dump json
    // cJSON_PrintBuffered();
  }
  

  return cnt;
}

/**
 * @param const char* section
 * @param const char* key
 * @param void* value
 */
int CGwConfig::set_value(const char *section, const char *key, void *value)
{
  if (m_json_conf == NULL)
  {
    return -1;
  }

  return 0;
}

/**
 * 加载配置文件
 */
bool CGwConfig::load(void)
{
  cJSON_Delete(m_json_conf);
  m_json_conf = read_conf(m_conf_filename);

  return (m_json_conf != NULL);
}

/**
 * 保存配置文件
 */
bool CGwConfig::save(void)
{
  char *s = cJSON_Print(m_json_conf);
  if (s == NULL)
  {
    return false;
  }

  FILE *fp = fopen(m_conf_filename, "wb");
  if (fp != NULL)
  {
    fputs(s, fp);
    fclose(fp);
  }

  cJSON_free(s);
  return true;
}

/**
 * 加载配置字符串
 * @param const char *
 */
bool CGwConfig::load_string(const char *json_string)
{
  cJSON_Delete(m_json_conf);
  m_json_conf = cJSON_Parse(json_string);

  return (m_json_conf != NULL);
}

/**
 * 保存配置字符串
 * @param char *
 * @param size_t
 */
bool CGwConfig::save_string(char *str, size_t size)
{
  char *s = cJSON_PrintUnformatted(m_json_conf);
  if (s == NULL)
  {
    return false;
  }

  str[size - 1] = '\0';
  strncpy(str, s, size);

  cJSON_free(s);

  return true;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CGwConfig::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

std::string CGwConfig::read_conf_string(const char *section, const char *key) const
{
  char value[8192] = {0};

  if (get_value(section, key, value, COUNTOF(value) - 1) > 0)
  {
    return value;
  }

  return "";
  // return NULL;
}

int CGwConfig::read_conf_int(const char *section, const char *key, int def) const
{
  std::string s = read_conf_string(section, key);
  if (s.size() == 0)
  {
    return def;
  }

  return std::atoi(s.c_str());
}

int64_t CGwConfig::read_conf_int_x(const char *section, const char *key, int64_t def) const
{
  std::string s = read_conf_string(section, key);
  if (s.size() == 0)
  {
    return def;
  }

  char *end = NULL;

  return std::strtoll(s.c_str(), &end, 0);
}

int CGwConfig::read_conf_array(const char *section, const char *key, int *array_size, 
    const char* key1, char*** ppp_array_key1_value,
    const char* key2, char*** ppp_array_key2_value) {
  cJSON *obj_parser;
  cJSON *obj_parser_key;
  char **pp_array_key1_value = NULL;
  char **pp_array_key2_value = NULL;

  if (m_json_conf == NULL)
  {
    return -1;
  }

  obj_parser = cJSON_GetObjectItem(m_json_conf, section);
  if (obj_parser == NULL)
  {
    return -100;
  }

  obj_parser_key = cJSON_GetObjectItem(obj_parser, key);
  if (obj_parser_key == NULL)
  {
    return -200;
  }
  if (!cJSON_IsArray(obj_parser_key))
  {
    return -1;
  }
  int size = cJSON_GetArraySize(obj_parser_key);
  pp_array_key1_value = (char**)malloc(sizeof(char*) * size);
  pp_array_key2_value = (char**)malloc(sizeof(char*) * size);
  if (!pp_array_key1_value || !pp_array_key2_value)
  {
    GWLOG_ERROR(m_comm, "out memory\n");
    goto end;
  }
  for (int i=0; i<size; i++) {
    cJSON *arg_obj = cJSON_GetArrayItem(obj_parser_key, i);
    if (!cJSON_IsObject(arg_obj)) {
      goto end;
    }
    cJSON *key1_obj = cJSON_GetObjectItem(arg_obj, key1);
    cJSON *key2_obj = cJSON_GetObjectItem(arg_obj, key2);
    if (!cJSON_IsString(key1_obj) && !cJSON_IsString(key2_obj)) {
      goto end;
    }
    pp_array_key1_value[i] = strdup(key1_obj->valuestring);
    pp_array_key2_value[i] = strdup(key2_obj->valuestring);
  }
  *ppp_array_key1_value = pp_array_key1_value;
  *ppp_array_key2_value = pp_array_key2_value;
  *array_size = size;
  return 0;
end:
    SAFE_FREE(pp_array_key1_value);
    SAFE_FREE(pp_array_key2_value);
    return -1;
}

int CGwConfig::read_conf_array(const char *section, const char *key, int *array_size, char ***ppp_array_value) const
{
  cJSON *obj_parser;
  cJSON *obj_parser_key;
  int i = 0;
  int k = 0;
  int size = 0;
  cJSON *arg_obj = NULL;
  char **pp_array_value = NULL;

  if (m_json_conf == NULL)
  {
    return -1;
  }

  if (section)
  {
    obj_parser = cJSON_GetObjectItem(m_json_conf, section);
    if (obj_parser == NULL)
    {
      return -100;
    }
  }
  else
  {
    obj_parser = m_json_conf;
  }

  if (key)
  {
    obj_parser_key = cJSON_GetObjectItem(obj_parser, key);
    if (obj_parser_key == NULL)
    {
      return -200;
    }
  }
  else
  {
    obj_parser_key = m_json_conf;
  }

  if (!cJSON_IsArray(obj_parser_key))
  {
    return -1;
  }

  size = cJSON_GetArraySize(obj_parser_key);
  pp_array_value = (char**)malloc(sizeof(char*) * size);
  if (pp_array_value == NULL)
  {
    GWLOG_ERROR(m_comm, "out memory\n");
    return -1;
  }
  memset(pp_array_value, 0, sizeof(char*) * size);

  for (k = i = 0; i < size; ++i)
  {
    arg_obj = cJSON_GetArrayItem(obj_parser_key, i);
    if (cJSON_IsString(arg_obj))
    {
      char *s = (char*)malloc(arg_obj->valuestring_size + 1);
      if (s == NULL)
      {
        free_array(pp_array_value, k);
        return 0;
      }
      memcpy(s, arg_obj->valuestring, arg_obj->valuestring_size);
      s[arg_obj->valuestring_size] = '\0';
      //pp_array_value[k] = arg_obj->valuestring;
      pp_array_value[k] = s;
      k++;
    }
  }

  *ppp_array_value = pp_array_value;
  *array_size = k;

  return 0;
}

int CGwConfig::get_array_size(const char *section, const char *key, cJSON **obj_parser_key_)
{
  if (m_json_conf == NULL)
  {
    return -1;
  }

  cJSON *obj_parser = NULL;
  if (section)
  {
    obj_parser = cJSON_GetObjectItem(m_json_conf, section);
    if (NULL == obj_parser)
    {
      return -1;
    }
  }
  else
  {
    obj_parser = m_json_conf;
  }

  cJSON *obj_parser_key = NULL;
  if (key)
  {
    obj_parser_key = cJSON_GetObjectItem(obj_parser, key);
    if (obj_parser_key == NULL)
    {
      return -1;
    }
  }
  else
  {
    obj_parser_key = obj_parser;
  }

  if (!cJSON_IsArray(obj_parser_key))
  {
    return -1;
  }

  if (obj_parser_key_)
  {
    *obj_parser_key_ = obj_parser_key;
  }
  return cJSON_GetArraySize(obj_parser_key);
}

// 读取conf文件中， 解析得到json参数
cJSON *CGwConfig::read_conf(const char *filename)
{
  FILE *fp = NULL;
  cJSON *conf = NULL;
  char *json_string = NULL;
  long size;

  if (filename == NULL)
  {
    return NULL;
  }

  fp = fopen(filename, "rb");
  if (fp == NULL)
  {
    // TODO
    // 配置文件不存在， 取系统内置默认值
    //return cJSON_CreateObject();
    goto end;
  }

  fseek(fp, 0, SEEK_END);
  size = ftell(fp);

  if (size < 0)
  {
    goto end;
  }

  json_string = (char *)malloc(sizeof(char) * (1 + size));
  if (json_string == NULL)
  {
    goto end;
  }

  fseek(fp, 0, SEEK_SET);
  fread(json_string, size, 1, fp);
  json_string[size] = '\0';

  GWLOG_DEBUG(m_comm, "json strlen() = %d\n", strlen(json_string));
  conf = cJSON_Parse(json_string);

end:

  if (fp != NULL)
  {
    fclose(fp);
  }

  if (json_string != NULL)
  {
    free(json_string);
  }

  return conf;
}

