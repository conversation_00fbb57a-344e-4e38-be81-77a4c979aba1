/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/stat.h>

#include "mon_conf.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "utils.h"
#include "gw_config.h"
#include "ipfilter_rule.h"
#include "gw_license.h"
#include "gw_stats.h"
#include "event_analyze.h"
#include "gw_i_parser.h"
#include "interactive_service.h"

#define MON_CONF_NAME "mon_conf"

CMonConf::CMonConf(void) : m_str_conf_filepath("")
                         , m_mon_conf({0})
                         , m_str_url_filepath("")
                         , m_mon_conf_url({0})
                         , m_str_accout_filepath("")
                         , m_mon_conf_accout({0})
                         , m_quit_signal(0)
                         , m_comm(NULL)
                         , m_routine_mon_conf_stat(0)
                         , m_pthread_mon_conf(0)
                         , m_str_urlbase_filepath("")
                         , m_mon_conf_urlbase({0})
                         , m_str_interactive_filepath("")
                         , m_mon_conf_interactive({0})
{
}

CMonConf::~CMonConf(void)
{

}

void CMonConf::init()
{
    ASSERT(m_comm != NULL);
    load_conf();
}

void CMonConf::fini()
{

}

void CMonConf::run()
{
    int err = 0;
    ASSERT(m_comm != NULL);
    if (0 != (err = pthread_create(&m_pthread_mon_conf, NULL, (void *(*)(void *))thread_routine_mon_conf, this)))
    {
        GWLOG_ERROR(m_comm, "routine mon conf thread create failed(%s)\n", strerror(err));
    }
    else
    {
        GWLOG_INFO(m_comm, "routine mon conf thread create successfully\n");
        m_routine_mon_conf_stat = 1;
    }
}

void CMonConf::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);
    m_comm = comm;
}

void CMonConf::set_quit_signal(void)
{
    m_quit_signal = 1;
}

void CMonConf::wait_for_stop(void)
{
    if (m_routine_mon_conf_stat == 1)
    {
        pthread_join(m_pthread_mon_conf, NULL);
    }
}

bool CMonConf::load_conf(void)
{
    CGwConfig *pgwc = m_comm->get_gw_config();
    ASSERT(pgwc != NULL);

    /* 获取主配置文件路径 */
    const char* conf_filepath = pgwc->get_config_path();
    if (conf_filepath) {
        m_str_conf_filepath = conf_filepath;
    }
    

    /* url过滤配置文件路径 */
    m_str_url_filepath = pgwc->read_conf_string("parser", "rule_forward_conf");

    /* accout过滤配置文件路径 */
    m_str_accout_filepath = pgwc->read_conf_string("parser", "rule_conf");

    /* url过滤文件库路径 */
    m_str_urlbase_filepath = pgwc->read_conf_string("parser", "url_filter_base_path");
    if (m_str_urlbase_filepath.empty())
    {
        m_str_urlbase_filepath = "/opt/urlbase/url_filter_base.file";
    }

    m_str_interactive_filepath = "/opt/urlbase/url_filter_dynamic.file";

    return true;
}

int CMonConf::thread_routine_mon_conf(void *arg_ptr)
{
    CMonConf *pThis = (CMonConf*)arg_ptr;
    ASSERT(pThis != NULL);

    return pThis->routine_mon_conf();
}

int CMonConf::routine_mon_conf()
{
    while (!m_quit_signal)
    {
        /* 过滤IP、PORT以及采样信息 */
        check_conf();

        /* URL转发规则 */
        check_conf_forward();

        /* 账号信息解析 */
        check_conf_user_info();

        /* url过滤 */
        check_conf_url_base_info();

        sleep(1);
    }

    return 0;
}

void CMonConf::load_conf_file_stat(const char *filename, conf_file_stat_t *p_cfs)
{
    //conf_file_stat_t fst = {0};
    struct stat st;

    if (0 == stat(filename, &st))
    {
        int file_size = st.st_size;     //get file size (byte)
        long modify_time = st.st_mtime; //latest modification time (seconds passed from 01/01/00:00:00 1970 UTC)

        p_cfs->file_size = file_size;
        p_cfs->modify_time = modify_time;
    }

    return ;
} 

int CMonConf::has_load_conf(conf_file_stat_t *p_cfs, const char *filename)
{
    conf_file_stat_t new_fst = {0};
    load_conf_file_stat(filename, &new_fst);

    // printf("file name=%s\n", filename);

    if (new_fst.modify_time == 0)
    {
        return 0;
    }

    if (p_cfs->modify_time == 0)
    {
        // 首次使用
        memcpy(p_cfs, &new_fst, sizeof(conf_file_stat_t));
    }

    if (0 != memcmp(&new_fst, p_cfs, sizeof(conf_file_stat_t)))
    {
        memcpy(p_cfs, &new_fst, sizeof(conf_file_stat_t));
        return 1;
    }

    return 0;
}

void CMonConf::check_conf(void)
{
    // 检查 conf 文件是否修改时间变化 文件大小是否有变化
    if (!has_load_conf(&m_mon_conf, m_str_conf_filepath.c_str()))
    {
        return;
    }    

    /* 重新加载配置文件 */
    ASSERT(m_comm != NULL);
    CGwConfig *pgwc = m_comm->get_gw_config();
    ASSERT(pgwc != NULL);
    pgwc->set_config_path(m_str_conf_filepath.c_str());
    if (!pgwc->load())
    {
        return ;
    }

    GWLOG_INFO(m_comm, "[%s] conf changed : %s\n", MON_CONF_NAME, m_str_conf_filepath.c_str());

    /* 更新 显示详细信息参数 */
    GWLOG_INFO(m_comm, "[%s] verbose \n", MON_CONF_NAME);
    m_comm->load_conf(NULL);

    /* 更新流量限制参数 */
    m_comm->get_gw_stats()->load_conf(NULL);

    /* 更新interactive_service配置 */
    m_comm->get_interactive_service()->load_conf(NULL);

    /* 更新 过滤IP */
    GWLOG_INFO(m_comm, "[%s] ip filter\n", MON_CONF_NAME);
    m_comm->get_ip_filter_rule()->ip_filter_for_mon(0);

    /* 更新 IP白名单 */
    GWLOG_INFO(m_comm, "[%s] ip white\n", MON_CONF_NAME);
    m_comm->get_ip_filter_rule()->ip_white_for_mon(0);

    /* 更新 过滤PORT */
    GWLOG_INFO(m_comm, "[%s] port filter\n", MON_CONF_NAME);
    m_comm->get_port_filter_rule()->port_filter_for_mon(0);

    CEventAnalyze::instance().load_conf(NULL);

    CParser *sa[256] = {0};
    int n = m_comm->get_parser_array(sa, COUNTOF(sa));

    for (int i = 0; i < n; i++)
    {
        const std::string str_parser_name = (sa[i])->get_name();
        if (str_parser_name.find("CHttpParser") != std::string::npos)
        {
            sa[i]->read_conf_filetype_for_mon();
        }
    }

    /* 读取转发配置参数文件 */
    std::string str_url_filepath;
    str_url_filepath = pgwc->read_conf_string("parser", "rule_forward_conf");
    if (str_url_filepath.size() > 0)
    {
        if (str_url_filepath.compare(m_str_url_filepath) != 0)
        {
            m_str_url_filepath.assign(str_url_filepath);
            /* 强制加载配置文件 */ 
            m_mon_conf_url.modify_time = -1;
        }
    }

    /* 读取用户配置参数文件 */
    std::string str_accout_filepath;
    str_accout_filepath = pgwc->read_conf_string("parser", "rule_conf");
    if (str_accout_filepath.size() > 0)
    {
        if (str_accout_filepath.compare(m_str_accout_filepath) != 0)
        {
            m_str_accout_filepath.assign(str_accout_filepath);
            m_mon_conf_accout.modify_time = -1;
        }
    }
}

void CMonConf::check_conf_forward(void)
{
    // 检查 conf 文件是否修改时间变化 文件大小是否有变化
    if (!has_load_conf(&m_mon_conf_url, m_str_url_filepath.c_str()))
    {
        return;
    }

    GWLOG_INFO(m_comm, "[%s] forward conf changed: %s\n", MON_CONF_NAME, m_str_url_filepath.c_str());
    m_comm->get_url_filter_rule()->read_conf_forward_for_mon(m_str_url_filepath.c_str());
}

void CMonConf::check_conf_user_info(void)
{
    // 检查 conf 文件是否修改时间变化 文件大小是否有变化
    if (!has_load_conf(&m_mon_conf_accout, m_str_accout_filepath.c_str()))
    {
        return;
    }

    GWLOG_INFO(m_comm, "[%s] forward conf changed: %s\n", MON_CONF_NAME, m_str_accout_filepath.c_str());
    m_comm->get_accout_filter_rule()->read_conf_user_info_for_mon(m_str_accout_filepath.c_str());
}


void CMonConf::check_conf_url_base_info(void)
{
    bool interactive_change = has_load_conf(&m_mon_conf_interactive, m_str_interactive_filepath.c_str());
    bool urlbase_change = has_load_conf(&m_mon_conf_urlbase, m_str_urlbase_filepath.c_str());
    if (!interactive_change && !urlbase_change)
    {
        return;
    }
    else if(interactive_change && !urlbase_change)
    {
        GWLOG_INFO(m_comm, "[%s] interactive url conf changed: %s\n", MON_CONF_NAME, m_str_interactive_filepath.c_str());
    }
    else if(urlbase_change && !interactive_change)
    {
        GWLOG_INFO(m_comm, "[%s] url base conf changed: %s\n", MON_CONF_NAME, m_str_urlbase_filepath.c_str());
    }
    else
    {
        GWLOG_INFO(m_comm, "[%s] interactive url conf changed: %s\n", MON_CONF_NAME, m_str_interactive_filepath.c_str());
        GWLOG_INFO(m_comm, "[%s] url base conf changed: %s\n", MON_CONF_NAME, m_str_urlbase_filepath.c_str());
    }
    CParser *sa[256] = {0};
    int n = m_comm->get_parser_array(sa, COUNTOF(sa));

    for (int i = 0; i < n; i++)
    {
        sa[i]->read_conf_urlbase_for_mon();
    }

    return;
}