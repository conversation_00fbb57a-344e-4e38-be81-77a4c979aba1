/**
 * Project gw-hw
 * <AUTHOR>
 * @version 
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h> 
#include <sys/stat.h>
#include <sys/inotify.h>
#include <fstream>

#include "ob_cfg.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "utils.h"
#include "gw_config.h"
#include "ipfilter_rule.h"
#include "gw_license.h"
#include "gw_stats.h"
#include "event_analyze.h"
#include "gw_i_parser.h"
#include "interactive_service.h"

CObCfg::CObCfg(void) : m_str_conf_filepath("")
                         , m_str_traffic_source_filepath("")
                         , m_quit_signal(0)
                         , m_comm(NULL)
                         , m_routine_ob_cfg_stat(0)
                         , m_pthread_ob_cfg(0)
{
}

CObCfg::~CObCfg(void)
{

}

void CObCfg::init()
{
    ASSERT(m_comm != NULL);
    load_conf();

    std::ifstream ip_file(m_str_traffic_source_filepath);
    if (!ip_file) { // 文件不存在
        std::ofstream new_ip_file(m_str_traffic_source_filepath);
        if (new_ip_file) {
            GWLOG_INFO(m_comm, "%s create success!\n", m_str_traffic_source_filepath.c_str());
        } else {
            GWLOG_ERROR(m_comm, "%s create failed!\n", m_str_traffic_source_filepath.c_str());
        }
    }

    m_notify_fd = inotify_init();
    if (m_notify_fd == -1) {
        GWLOG_ERROR(m_comm, "inotify_init failed\n");
    }

    std::vector<std::string> files_to_watch = {m_str_traffic_source_filepath};

    std::map<int, std::string> watch_to_path;
    for (const auto& path : files_to_watch) {
        int wd = inotify_add_watch(m_notify_fd, m_str_traffic_source_filepath.c_str(), IN_CLOSE_WRITE);
        if (wd == -1) {
            GWLOG_ERROR(m_comm, ("Failed to add watch for: " + path + "\n").c_str());
        }
        watch_to_path[wd] = path;
    }
}

void CObCfg::fini()
{
    close(m_notify_fd);
}

void CObCfg::run()
{
    int err = 0;
    ASSERT(m_comm != NULL);
    if (0 != (err = pthread_create(&m_pthread_ob_cfg, NULL, (void *(*)(void *))thread_routine_ob_cfg, this)))
    {
        GWLOG_INFO(m_comm, "routine obverse config thread create failed(%s)\n", strerror(err));
    }
    else
    {
        GWLOG_INFO(m_comm, "routine obverse config thread create successfully\n");
        m_routine_ob_cfg_stat = 1;
    }
}

void CObCfg::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);
    m_comm = comm;
}

void CObCfg::set_quit_signal(void)
{
    m_quit_signal = 1;
}

void CObCfg::wait_for_stop(void)
{
    if (m_routine_ob_cfg_stat == 1)
    {
        pthread_join(m_pthread_ob_cfg, NULL);
    }
}

bool CObCfg::load_conf(void)
{
    CGwConfig *pgwc = m_comm->get_gw_config();
    ASSERT(pgwc != NULL);

    const char* conf_filepath = pgwc->get_config_path();
    if (conf_filepath) {
        m_str_conf_filepath = conf_filepath;
    }
    
    m_str_traffic_source_filepath = pgwc->read_conf_string("parser", "agent_client_ip_file_path");

    return true;
}

int CObCfg::thread_routine_ob_cfg(void *arg_ptr)
{
    CObCfg *pThis = (CObCfg*)arg_ptr;
    ASSERT(pThis != NULL);

    return pThis->routine_ob_conf();
}

int CObCfg::routine_ob_conf()
{
    while (!m_quit_signal)
    {
        char buf[16384];
        ssize_t length = read(m_notify_fd, buf, sizeof(buf));
        if (length < 0 && errno != EAGAIN) {
            GWLOG_ERROR(m_comm, "notify read failed\n");
            continue;
        }
        
        struct inotify_event *event = (struct inotify_event *) &buf;
        // GWLOG_INFO(m_comm, "notify file path: %s\n", event->name);,适合监控一个目录
        if (!(event->mask & IN_CLOSE_WRITE)) {
            GWLOG_ERROR(m_comm, "notify read failed\n");
            continue;
        }

        update_cfg();
    }

    return 0;
}

void CObCfg::update_cfg(void)
{
    CParser *pa[256] = {0};
    int n = m_comm->get_parser_array(pa, COUNTOF(pa));

    for (int i = 0; i < n; i++)
    {
        if (strncmp(pa[i]->get_name(), "CHttpParser", 11) == 0)
        {
            pa[i]->read_conf_urlbase_for_mon();
        }
    }

    return;
}