/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include "gw_i_upload.h"

/**
 * CUpload implementation
 *
 * 消息上传基类
 */

CUpload::~CUpload(void)
{
}

/**
 * 将消息推送到上传线程队列中。
 * @param UploadMsg*
 */
void CUpload::put_msg(UploadMsg *pum)
{
}

void CUpload::init()
{
  // ASSERT(m_comm != NULL);
  // m_quit_signal = 0;
}

void CUpload::fini()
{
  // ASSERT(m_comm != NULL);
}

void CUpload::run()
{
  // ASSERT(m_comm != NULL);
}

/**
 * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
 */
const char *CUpload::get_name(void) const
{
  // return m_name;
  return "CUpload";
}

/**
 * 获取版本号。
 */
const char *CUpload::get_version(void) const
{
  return "";
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CUpload::set_gw_common(CGwCommon *comm)
{
  // m_comm = comm;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CUpload::load_conf(const char *)
{
  return true;
}

/**
 * 触发退出信号时处理
 */
void CUpload::set_quit_signal(void)
{
  // m_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CUpload::wait_for_stop(void)
{
}
