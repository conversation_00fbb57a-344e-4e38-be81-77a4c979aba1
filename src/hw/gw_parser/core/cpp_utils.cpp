#include <fnmatch.h>
#include <algorithm>

#include "cpp_utils.h"


bool is_repeatedchar(std::vector<std::string> &src_vec, std::string dest_str)
{
  if (find(src_vec.begin(), src_vec.end(), dest_str) == src_vec.end())
  {
    return false;
  }
  
  return true;
}

void addstring_to_array(std::vector<std::string> &src_vec, std::string dest_str, bool repeatedCharIgnored)
{
  if (dest_str.empty())
  {
    return;
  }

  if (repeatedCharIgnored)
  {
    bool is_repeated = is_repeatedchar(src_vec, dest_str);
    if (is_repeated == false)
    {
      src_vec.push_back(dest_str);
    }
  }
  else
  {
    src_vec.push_back(dest_str);
  }
}

std::vector<std::string> split_string(std::string srcStr, std::string delimStr, bool repeatedCharIgnored)
{
  std::vector<std::string> resultStringVector;
  
  size_t pos = srcStr.find(delimStr.at(0));
  std::string addedString = "";
  while (pos != std::string::npos)
  {
    addedString = srcStr.substr(0, pos);
    addstring_to_array(resultStringVector, addedString, repeatedCharIgnored);

    srcStr.erase(srcStr.begin(), srcStr.begin() + pos + 1);
    pos = srcStr.find(delimStr.at(0));
  }
  addedString = srcStr;
  
  addstring_to_array(resultStringVector, addedString, repeatedCharIgnored);
  return resultStringVector;
} 

void split_string(std::string srcStr, std::string delimStr, std::vector<std::string> &resultStringVector, bool repeatedCharIgnored)
{
  size_t pos = srcStr.find(delimStr.at(0));
  std::string addedString = "";
  while (pos != std::string::npos)
  {
    addedString = srcStr.substr(0, pos);
    addstring_to_array(resultStringVector, addedString, repeatedCharIgnored);

    srcStr.erase(srcStr.begin(), srcStr.begin() + pos + 1);
    pos = srcStr.find(delimStr.at(0));
  }
  addedString = srcStr;
  
  addstring_to_array(resultStringVector, addedString, repeatedCharIgnored);

  return;
}

/*
* 通配符匹配 (*) 
*/
bool wildcmp(const std::string& pattern, const std::string& str)
{
  return fnmatch(pattern.c_str(), str.c_str(), FNM_EXTMATCH) == 0;
}