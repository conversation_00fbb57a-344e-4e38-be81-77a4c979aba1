/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>

#include <cstdlib>
#include <string>
#include <algorithm>
#include <unistd.h>

#include <functional>

#include "gw_common.h"

#include "gw_i_source.h"
#include "gw_i_parser.h"
#include "gw_i_upload.h"

#include "worker_queue.h"
#include "gw_license.h"
#include "gw_config.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "watch_dog.h"
#include "gw_stats.h"
#include "interactive_service.h"
#include "filter_rule.h"
#include "ipfilter_rule.h"
#include "portfilter_rule.h"
#include "urlfilter_rule.h"
#include "accoutfilter_rule.h"
#include "mon_conf.h"
#include "ob_cfg.h"
#include "nacos_listen_conf.h"
#include "minio_upload.h"
#include "session_mgt.h"
#include "session.h"

#include "utils_core.h"
#include "utils.h"
#include "cpp_utils.h"

#include "pp.h"

// #define MAX_WORKER_SESSION_NUM (32)
#define MAX_WORKER_SESSION_NUM WORKER_PARAMS_MAX_NUM

// 从类名取出对应的解析协议名称
std::string get_proto_string_from_parser_name(const std::string &srcStr)
{
  std::string s;
  s = split_string(srcStr.c_str(), "-")[0];
  std::transform(s.begin(), s.end(), s.begin(), ::tolower);

  s.erase(0, 1);
  s.erase(s.rfind("parser", s.length()));

  return s;
}

// 匹配消息上传类名是否匹配当前协议解析类
bool match_upload_and_parser_name(const char *upload_name, const char *parser_upload_name, const char *parser_proto_name)
{
  std::string s;
  s = split_string(upload_name, "-")[0];
  std::transform(s.begin(), s.end(), s.begin(), ::tolower);

  std::string spun = parser_upload_name;
  std::transform(spun.begin(), spun.end(), spun.begin(), ::tolower);

  if (parser_proto_name != NULL && strlen(parser_proto_name) > 0)
  {
    std::vector<std::string> vs = split_string(s.c_str(), "_");
    if (vs.size() < 2 || vs[1] != parser_proto_name)
    {
      return false;
    }

    s = vs[0];
  }

  s.erase(0, 1);
  s.erase(s.rfind("upload", s.length()));

  return (s == spun);
}

/**
 * CGwCommon implementation
 *
 * 各模块查询公共对象实例。
 */

CGwCommon::CGwCommon(void) : m_gw_license(new CGwLicense())
                           , m_interactive_service(new CInteractiveService())
                           , m_gw_stats(new CGwStats())
                           , m_gw_config(new CGwConfig())
                           , m_gw_logger(new CGwLogger())
                           , m_watch_dog(new CWatchDog())
                           , m_mon_conf(new CMonConf())
                           , m_ob_cfg(new CObCfg())
                           , m_ip_filter_rule(new CIpfilterRule())
                           , m_port_filter_rule(new CPortfilterRule())
                           , m_url_filter_rule(new CUrlfilterRule())
                           , m_user_filter_rule(new CAccoutfilterRule())
                           , m_upload_client_ip_filter_rule(new CIpfilterRule())
                           , m_upload_server_ip_filter_rule(new CIpfilterRule())
                           , m_nacos_listen(new CNacosListen())
                           , m_minio_upload(new CMinioUpload())
                           , m_conf_verbose(0)
                           , m_conf_ssl_verbose(0)
                           , m_i_exit_flag(0)
                           , m_max_session_num(10000)
                           , m_session_mgt_disable_timeout_check(0)
{
  for (size_t i = 0; i < MAX_WORKER_SESSION_NUM; i++)
  {
    m_vec_session_mgt.push_back(new CSessionMgt());
  }

  set_myself_to_module();

  struct timespec tp;
  clock_gettime(CLOCK_MONOTONIC_RAW, &tp);
  m_time_ts = tp.tv_sec;
  m_time_ms = (uint64_t)tp.tv_sec * 1000 + tp.tv_nsec / (1000LL * 1000LL);

  clock_gettime(CLOCK_REALTIME, &tp);
  m_real_time_ts = tp.tv_sec;
  m_real_time_ms = (uint64_t)tp.tv_sec * 1000 + tp.tv_nsec / (1000LL * 1000LL);;
  int err = 0;
  if (0 != (err = pthread_create(&m_thread_time_produce, NULL, (void *(*)(void *))update_current_gw_time, this)))
  {
    fprintf(stderr, "update current gw time thread create failed(%s)\n", strerror(err));
  }
}

CGwCommon::~CGwCommon(void)
{
  m_vec_source.clear();
  m_vec_source_so.clear();

  m_vec_parser.clear();
  m_vec_parser_so.clear();

  m_vec_upload.clear();
  m_vec_upload_so.clear();

  for (size_t i = 0; i < m_vec_session_mgt.size(); i++)
  {
    delete m_vec_session_mgt[i];
  }
  m_vec_session_mgt.clear();
  delete m_nacos_listen;
  delete m_upload_server_ip_filter_rule;
  delete m_upload_client_ip_filter_rule;
  
  delete m_user_filter_rule;
  delete m_url_filter_rule;
  delete m_port_filter_rule;
  delete m_ip_filter_rule;
  delete m_mon_conf;
  delete m_gw_logger;
  delete m_gw_config;
  delete m_interactive_service;
  delete m_gw_stats;
  delete m_gw_license;
  delete m_watch_dog;
  delete m_minio_upload;

  m_i_exit_flag = 1;
  pthread_join(m_thread_time_produce, NULL);
}

void CGwCommon::cache_clean() 
{
  for (auto &it : m_vec_source) 
  {
    it->cache_clean();
  }
  for (auto &it : m_vec_parser)
  {
    it->cache_clean();
  }
  for (auto &it : m_vec_upload)
  {
    it->cache_clean();
  }
  for (auto &it : m_vec_session_mgt)
  {
    it->free_all_session();
  }
}

void CGwCommon::init()
{
  load_conf(NULL);
  eaelier_init_logger_and_sessionmgt();

  init_module();  
}

void * CGwCommon::update_current_gw_time(void *arg_ptr)
{
  CGwCommon *p_this = (CGwCommon*)arg_ptr;
  p_this->update_current_gw_ts();
  return NULL;
}


void CGwCommon::update_current_gw_ts()
{
  struct timespec tp;
  while(!m_i_exit_flag) {
    
    clock_gettime(CLOCK_MONOTONIC_RAW, &tp);
    m_time_ts = tp.tv_sec;
    m_time_ms = (uint64_t)tp.tv_sec * 1000 + tp.tv_nsec / (1000LL * 1000LL);
    usleep(1000);
    clock_gettime(CLOCK_REALTIME, &tp);
    m_real_time_ts = tp.tv_sec;
    m_real_time_ms = (uint64_t)tp.tv_sec * 1000 + tp.tv_nsec / (1000LL * 1000LL);;
  }
}

/**
 * 返回时间戳秒级（不受系统时间更改影响）
 */
time_t CGwCommon::gw_time(void) const
{
  return m_time_ts;
}

/**
 * 返回时间戳毫秒级（不受系统时间更改影响）
 */
uint64_t CGwCommon::gw_time_ms(void) const
{
  return m_time_ms;
}

/**
 * 返回时间戳毫秒级（系统实际时间）
 */
uint64_t CGwCommon::gw_real_time_ms(void) const
{
  return m_real_time_ms;
}



/**
 * 返回时间戳毫秒级（系统实际时间）
 */
uint64_t CGwCommon::gw_real_time_ts(void) const
{
  return m_real_time_ts;
}


/**
 * 动态内存分配
 * @param int size
 */
void *CGwCommon::mem_malloc(int size) const
{
  return malloc(size);
}

/**
 * 动态内存释放
 * @param void*
 */
void CGwCommon::mem_free(void *ptr) const
{
  free(ptr);
}

/**
 * 动态内存调整
 * @param void*
 * @param int size
 */
void *CGwCommon::mem_realloc(void *p, int size) const
{
  return realloc(p, size);
}

/**
 * 内存不足提示。
 * @param const char *
 */
void CGwCommon::no_mem(const char *func) const
{
  fprintf(stderr, "Out of memory in %s.\n", func);
  exit(1);
}

//#if !_DISABLE_LICENSE_
/**
 * 获取授权管理对象实例
 */
CGwLicense *CGwCommon::get_gw_license(void) const
{
  return m_gw_license;
}
//#endif

/**
 * 获取配置对象实例
 */
CGwConfig *CGwCommon::get_gw_config(void) const
{
  return m_gw_config;
}

/**
 * 获取全局状态对象实例
 */
CGwStats *CGwCommon::get_gw_stats(void) const
{
  return m_gw_stats;
}

/**
 * 获取守护对象实例
 */
CWatchDog *CGwCommon::get_watchdog(void) const
{
  return m_watch_dog;
}

/**
 * 获取日志对象实例
 */
CGwLogger *CGwCommon::get_gw_logger(void) volatile const
{
  return m_gw_logger;
}

/**
 * 获取动态参数配置实例 
 */
CMonConf *CGwCommon::get_mon_conf(void) const
{
  return m_mon_conf;
}

/**
 * 获取配置热变更实例 
 */
CObCfg *CGwCommon::get_ob_cfg(void) const
{
  return m_ob_cfg;
}


/**
 * 获取conn连接地址字符串(src_ip,src_port,dst_ip,dst_port)
 * @param char buf[256]
 * @param struct conn *pcon
 */
const char *CGwCommon::get_conn_addr(char buf[256], size_t size, const struct conn *pcon) volatile const
{
  buf[size - 1] = '\0';
  char a_src_ip[128] = {0};
  char a_dst_ip[128] = {0};

  // TODO IPv6
  strncpy(a_src_ip, int_ntoa(pcon->client.ipv4), COUNTOF(a_src_ip) - 1);
  strncpy(a_dst_ip, int_ntoa(pcon->server.ipv4), COUNTOF(a_dst_ip) - 1);


  snprintf(buf, size - 1, "%s,%i,%s,%i", a_src_ip, pcon->client.port, a_dst_ip, pcon->server.port);

  return buf;
}

/**
 * 获取IP地址字符串
 * @param char buf[256]
 * @param struct addr *addr
 */
const char *CGwCommon::get_ip_addr(char buf[256], size_t size, const struct addr *addr) volatile const
{
  buf[size - 1] = '\0';

  strncpy(buf, int_ntoa(addr->ipv4), size - 1);
  return buf;
}

/**
 * 获取IP过滤规则对象实例
 * @param int type
 */
CFilterRule *CGwCommon::get_ip_filter_rule(int type) const
{
  return m_ip_filter_rule;
}

/**
 * 获取端口过滤规则对象实例
 * @param int type
 */
CFilterRule *CGwCommon::get_port_filter_rule(int type) const
{
  return m_port_filter_rule;
}

/**
 * 获取URL过滤规则对象实例
 * @param int type
 */
CFilterRule *CGwCommon::get_url_filter_rule(int type) const
{
  return m_url_filter_rule;
}

/**
 * 获取账号过滤规则对象实例
 * @param int type
 */
CFilterRule *CGwCommon::get_accout_filter_rule(int type) const
{
  return m_user_filter_rule;
}

/**
 * 获取交互服务对象实例
 */
CInteractiveService *CGwCommon::get_interactive_service(void) const
{
  return m_interactive_service;
}

CNacosListen* CGwCommon::get_nacos_listen_conf() const
{
  return m_nacos_listen;
}

CMinioUpload* CGwCommon::get_minio_upload() const
{
  return m_minio_upload;
}

/**
 * 获取所有网络包数据源对象。
 * @param CSource*
 * @param int size
 */
int CGwCommon::get_source_array(CSource *p[], int size) const
{
  unsigned int i = 0;

  for (i = 0; i < m_vec_source.size(); i++)
  {
    p[i] = m_vec_source[i];

    if (i >= (unsigned int)size)
    {
      break;
    }
  }

  return i;
}

/**
 * 获取所有网络包解析对象。
 * @param CParser*
 * @param int size
 */
int CGwCommon::get_parser_array(CParser *p[], int size) const
{
  unsigned int i = 0; 

  for (i = 0; i < m_vec_parser.size(); i++)
  {
    p[i] = m_vec_parser[i];

    if (i >= (unsigned int)size)
    {
      break;
    }
  }

  return i;
}

/**
 * 获取所有消息上传对象。
 * @param CUpload*
 * @param int size
 */
int CGwCommon::get_upload_array(CUpload *p[], int size) const
{
  unsigned int i = 0;

  for (i = 0; i < m_vec_upload.size(); i++)
  {
    p[i] = m_vec_upload[i];

    if (i >= (unsigned int)size)
    {
      break;
    }
  }

  return i;
}

/**
 * 为模块中创建线程和队列实例。
 */
CWorkerQueue *CGwCommon::create_worker_queue(void) const
{
  return new CWorkerQueue();
}

/**
 * 删除线程和队列实例。
 */
void CGwCommon::destory_worker_queue(CWorkerQueue *p) const
{
  delete p;
}

/**
 * 获取全局会话管理对象实例数量。
 */
int CGwCommon::get_session_mgt_num(void) const
{
  return m_vec_session_mgt.size();
}

/**
 * 获取全局会话管理对象实例。
 */
CSessionMgt *CGwCommon::get_session_mgt(int worker_no) const
{
  if ((unsigned int)worker_no < 0 || (unsigned int)worker_no >= m_vec_session_mgt.size())
  {
    return NULL;
  }

  return m_vec_session_mgt[worker_no];
}

int CGwCommon::get_parser_name_type(const char *parser_name) const
{
  std::map<const std::string, int>::const_iterator iter = m_map_parser_name_type.find(parser_name);
  if (iter == m_map_parser_name_type.end())
  {
    return -1;
  }

  return iter->second;
}

int CGwCommon::get_parser_type(const CParser *p) const
{
  std::map<const CParser *, int>::const_iterator iter = m_map_parser_type.find(p);
  if (iter == m_map_parser_type.end())
  {
    return -1;
  }

  return iter->second;
}

CUpload *CGwCommon::get_upload_from_parser(const CParser *parser, const char *upload_name)
{
  if (m_map_parser_upload.find(parser) == m_map_parser_upload.end())
  {
    CUpload *p_upload = NULL;
    std::string parser_proto_string = get_proto_string_from_parser_name(parser->get_name());

    if (upload_name != NULL)
    {
      for (size_t i = 0; i < m_vec_upload.size(); i++)
      {
        if (match_upload_and_parser_name(m_vec_upload[i]->get_name(), upload_name, parser_proto_string.c_str()))
        {
          p_upload = m_vec_upload[i];
          break;
        }
      }
    }

    if (p_upload == NULL)
    {
      for (size_t i = 0; i < m_vec_upload.size(); i++)
      {
        if (match_upload_and_parser_name(m_vec_upload[i]->get_name(), upload_name, NULL))
        {
          p_upload = m_vec_upload[i];
        }
      }
    }

    m_map_parser_upload[parser] = p_upload;

    if (p_upload)
    {
      GWLOG_TEST(this, "upload name:%s\n", p_upload->get_name());
    }
  }

  return m_map_parser_upload[parser];
}

CUpload *CGwCommon::get_upload_from_name(const char *upload_name)
{
  for (size_t i = 0; i < m_vec_upload.size(); i++)
  {
    if (match_upload_and_parser_name(m_vec_upload[i]->get_name(), upload_name, NULL))
    {
      return m_vec_upload[i];
    }
  }

  return NULL;
}

void CGwCommon::get_parser_data_dep(CParser *p, std::vector<CParser *>& vp) const
{
  parser_dep_map_t::const_iterator iter = m_map_parser_data_dep.find(p);
  if (iter == m_map_parser_data_dep.end())
  {
    return;
  }
  vp = iter->second;
}

int CGwCommon::get_verbose(int type) const
{

  switch (type)
  {
  case CONF_VB_SSL:
    return m_conf_ssl_verbose;
    break;

  case CONF_VB_GOBAL:
    return m_conf_verbose;
    break;

  default:
    break;
  }

  return m_conf_verbose;
}

bool CGwCommon::deal_parser(CSession *p_session, CParser *p_parent, deal_parser_param_t *pdpp, CParser *&parser_out) const
{
  CParser *parser;
  bool found;
  int res;
  parser_dep_map_t::const_iterator iter = m_map_parser_dep.find(p_parent);
  ASSERT(p_session != NULL);

  if (iter == m_map_parser_dep.end())
  {
    return false;
  }

  GWLOG_TRACE(this, "deal p_parent=%p %s\n", p_parent, (p_parent ? p_parent->get_name() : NULL));
  // 从当前会话中获取解析对象链
  if (p_session->parser_chain_get(p_parent, parser))
  {
    if (parser == NULL)
    {
      // 已经解析到最后一个了。
      return false;
    }

    found = false;
    switch (pdpp->state)
    {
    case TCP_STATE_DATA:
      res = parser->parse(pdpp->p_smgt, pdpp->p_app_stream, pdpp->pcon, p_session);
      found = true;
      break;

    case TCP_STATE_RESET:
      res = parser->parse_on_reset(pdpp->p_smgt, pdpp->p_app_stream, pdpp->pcon, p_session);
      found = true;
      break;

    case TCP_STATE_CLOSE:
    case TCP_STATE_TIMEOUT:
      res = parser->parse_on_close(pdpp->p_smgt, pdpp->p_app_stream, pdpp->pcon, p_session);
      found = true;
      break;

    default:
      break;
    }

    if (found)
    {
      if (iter->second.size() == 0)
      {
        p_session->parser_chain_add(parser, NULL);
      }
      if (0 <= res)
      {
        // 成功解析，直接返回，不再遍历其它解析器
        parser_out = parser;
        return true;
      }
      else if (res < 0)
      {
        // 不再继续尝试解析
        parser_out = NULL;
        return false;
      }
    }
  }

  StreamData* p_tcp_data = p_session->get_stream_data_from_type(SESSION_PROTO_TYPE_TCP);
  tcp_stream* a_tcp = p_tcp_data->a_tcp;
  for (size_t i = 0; i < iter->second.size(); i++)
  {
    // GWLOG_DEBUG(this, "%d %s\n", i, iter->second[i]->get_name());
    parser = iter->second[i];
    GWLOG_TRACE(this, "p_parent=%p %s  ==> %d parser=%p %s\n", p_parent, (p_parent ? p_parent->get_name() : NULL), i, parser, (parser ? parser->get_name() : NULL));
    if (parser == NULL)
    {
      return false;
    }

    found = false;
    switch (pdpp->state)
    {
    case TCP_STATE_DATA:
        {
            a_tcp->reverse = 0;
            if (!parser->probe(pdpp->p_smgt, pdpp->p_app_stream, pdpp->pcon, p_session))
            {
                break;
            }
            p_session->set_reverse(!!a_tcp->reverse);
            res = parser->parse(pdpp->p_smgt, pdpp->p_app_stream, pdpp->pcon, p_session);
            found = true;
            break;
        }

    case TCP_STATE_RESET:
      // GWLOG_TEST(this, "p_parent=%p parser=%p\n", p_parent, parser);
      if (!parser->probe_on_reset(pdpp->p_smgt, pdpp->p_app_stream, pdpp->pcon, p_session))
      {
        break;
      }
      // 探测出当前可用协议
      res = parser->parse_on_reset(pdpp->p_smgt, pdpp->p_app_stream, pdpp->pcon, p_session);
      found = true;
      break;

    case TCP_STATE_CLOSE:
    case TCP_STATE_TIMEOUT:
      if (!parser->probe_on_close(pdpp->p_smgt, pdpp->p_app_stream, pdpp->pcon, p_session))
      {
        break;
      }
      // 探测出当前可用协议
      res = parser->parse_on_close(pdpp->p_smgt, pdpp->p_app_stream, pdpp->pcon, p_session);
      found = true;
      break;

    default:
      break;
    }

    if (found)
    {
      GWLOG_TRACE(this, "res=%d state=%d p_parent=%p %s  ==> parser=%p %s\n", res, pdpp->state, p_parent, (p_parent ? p_parent->get_name() : NULL), parser, (parser ? parser->get_name() : NULL));
      p_session->parser_chain_add(p_parent, parser);
      if (0 == res)
      {
        // 成功解析，直接返回，不再遍历其它解析器
        parser_out = parser;
        return true;
        break;
      }
      else if (res < 0)
      {
        // 不再继续尝试解析
        parser_out = NULL;
        return false;
      }
    }
  }
  
  a_tcp->probe_num++;
  return false;
}


int CGwCommon::get_upload_mode(const char *name)
{
  if (NULL != name)
  {
    if (0 == strcasecmp("web", name))
    {
      // web
      return CONF_UPLOAD_MODE_WEB;
    }
    else if (0 == strcasecmp("kafka", name))
    {
      // kafka
      return CONF_UPLOAD_MODE_KAFKA;
    }
    else if (0 == strcasecmp("log", name))
    {
      // log
      return CONF_UPLOAD_MODE_LOG;
    }
    else if (0 == strcasecmp("test", name))
    {
      // test
      return CONF_UPLOAD_MODE_TEST;
    }
  }

    return 0;
}

/**
 * 设置进程退出标识
 */
void CGwCommon::set_gwparser_exit()
{
  m_i_exit_flag = 1;
}

/**
 * 获取进程退出标识
 */
int CGwCommon::get_gwparser_exit()
{
  return m_i_exit_flag;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CGwCommon::load_conf(const char *)
{
  ASSERT(m_gw_config != NULL);
  m_conf_verbose = m_gw_config->read_conf_int("parser", "verbose", m_conf_verbose);
  m_conf_ssl_verbose = m_gw_config->read_conf_int("parser", "ssl_verbose", m_conf_ssl_verbose);
  m_str_logger_level = m_gw_config->read_conf_string("logger", "level");
  m_max_session_num = m_gw_config->read_conf_int("parser", "session_max_num", m_max_session_num);
  m_session_mgt_disable_timeout_check = m_gw_config->read_conf_int("parser", "session_mgt_disable_timeout_check", m_session_mgt_disable_timeout_check);

  return true;
}

void CGwCommon::set_myself_to_module(void)
{
  m_gw_config->set_gw_common(this);
  m_gw_license->set_gw_common(this);
  m_gw_logger->set_gw_common(this);
  m_gw_stats->set_gw_common(this);
  m_watch_dog->set_gw_common(this);
  m_mon_conf->set_gw_common(this);
  m_ob_cfg->set_gw_common(this);
  m_interactive_service->set_gw_common(this);
  m_ip_filter_rule->set_gw_common(this);
  m_port_filter_rule->set_gw_common(this);
  m_url_filter_rule->set_gw_common(this);
  m_user_filter_rule->set_gw_common(this);
  m_upload_client_ip_filter_rule->set_gw_common(this);
  m_upload_server_ip_filter_rule->set_gw_common(this);
  m_nacos_listen->set_gw_common(this);
  m_minio_upload->set_gw_common(this);
  for (auto iter = m_vec_session_mgt.begin(); iter != m_vec_session_mgt.end(); iter++)
  {
    (*iter)->set_gw_common(this);
  }
}

int CGwCommon::gwhw_parser_status()
{
  int i_ret = 0;
  for (const auto &it : m_vec_source) 
  {
    i_ret = it->source_status();
    if (i_ret != 0)
    {
      return i_ret;
    }
  }

  for (const auto &it : m_vec_parser)
  {
    i_ret = it->parser_status();
    if (i_ret != 0)
    {
      return i_ret;
    }
  }

  for (const auto &it : m_vec_upload)
  {
    i_ret = it->upload_status();
    if (i_ret != 0)
    {
      return i_ret;
    }
  }
  
  return 0;
}

void CGwCommon::eaelier_init_logger_and_sessionmgt()
{
  m_gw_logger->set_level_name(m_str_logger_level.c_str());
  for (auto iter = m_vec_session_mgt.begin(); iter != m_vec_session_mgt.end(); iter++)
  {
    (*iter)->init_session(m_max_session_num);
    (*iter)->set_disable_timeout_check(m_session_mgt_disable_timeout_check);
  }
}

void CGwCommon::init_module()
{
  m_nacos_listen->init();
  m_gw_config->init();
  m_gw_license->init();
  m_gw_logger->init();
  m_gw_stats->init();
  m_watch_dog->init();
  m_interactive_service->init();
  m_mon_conf->init();
  m_ob_cfg->init();
  m_ip_filter_rule->init();
  m_port_filter_rule->init();
  m_url_filter_rule->init();
  m_user_filter_rule->init();
  m_upload_client_ip_filter_rule->init();
  m_upload_server_ip_filter_rule->init();
  m_minio_upload->init();
  get_nacos_listen_conf()->add_conf_handle("ip_filter", std::bind(&CFilterRule::ip_filter_for_mon, m_ip_filter_rule, 1, std::placeholders::_1));
  get_nacos_listen_conf()->add_conf_handle("ip_white", std::bind(&CFilterRule::ip_white_for_mon, m_ip_filter_rule, 1, std::placeholders::_1));
  get_nacos_listen_conf()->add_conf_handle("port_filter", std::bind(&CFilterRule::port_filter_for_mon, m_port_filter_rule, 1, std::placeholders::_1));
  get_nacos_listen_conf()->add_conf_handle_int("minio_upload_enable", std::bind(&CMinioUpload::modify_status, m_minio_upload, std::placeholders::_1));

  for (auto iter = m_vec_session_mgt.begin(); iter != m_vec_session_mgt.end(); iter++)
  {
    (*iter)->init();
  }
}
