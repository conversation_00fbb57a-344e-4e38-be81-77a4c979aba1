
#include <stdlib.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "addr_port_collect.h"
#include <algorithm>
using namespace std;


CAddrPortCollect::CAddrPortCollect() : addr_port_nums({{0}})
                                     , writing(0)
{
}

CAddrPortCollect::~CAddrPortCollect()
{
}

void CAddrPortCollect::add_addr_port(uint32_t dst_addr, uint32_t src_addr, uint16_t dst_port, uint32_t flow, int first_link){
    writing = 1;
    int i = 0;
    for (i = 0; i < MAX_ADDR_PROT_NUM; ++i) 
    {
        if (addr_port_nums[i].src_addr == 0 && addr_port_nums[i].dst_addr == 0 && addr_port_nums[i].dst_port == 0) 
        {
            addr_port_nums[i].src_addr = src_addr;
            addr_port_nums[i].dst_addr = dst_addr;
            addr_port_nums[i].dst_port = dst_port;
            addr_port_nums[i].count = 1;
            addr_port_nums[i].total = flow;
            break;
        }
        if (addr_port_nums[i].src_addr == src_addr && addr_port_nums[i].dst_addr == dst_addr && addr_port_nums[i].dst_port == dst_port) 
        {
            addr_port_nums[i].total += flow;
            if (first_link) 
            {
                ++addr_port_nums[i].count;
            }
            break;
        }
    }

    if (i >= MAX_ADDR_PROT_NUM) 
    {
        i = MAX_ADDR_PROT_NUM - 1;
        addr_port_nums[i].src_addr = src_addr;
        addr_port_nums[i].dst_addr = dst_addr;
        addr_port_nums[i].dst_port = dst_port;
        addr_port_nums[i].count = 1;
        addr_port_nums[i].total = flow;
    }

    /* 更新最新的信息到最前面 */
    if (i > 0) 
    {
        struct AddrPortCount tmp = addr_port_nums[i];
        memmove(&addr_port_nums[1], &addr_port_nums[0], i * sizeof(addr_port_nums[0]));
        addr_port_nums[0] = tmp;
    }

    writing = 0;
}


void CAddrPortCollect::clear()
{
    
    memset(&addr_port_nums[0], 0x00, sizeof(addr_port_nums));
}

int CAddrPortCollect::get_top_items(struct AddrPortCount *rets, int num, int order_field){

    struct AddrPortCount items[MAX_ADDR_PROT_NUM] = {0};
    while(writing);
    
    memmove(&items[0], &addr_port_nums[0], sizeof(items));

    sort(&items[0], &items[MAX_ADDR_PROT_NUM], [&order_field](const struct AddrPortCount &a, const struct AddrPortCount& b) { 
        if (order_field == STREAM_ORDER_BY_COUNT) return a.count > b.count;
        return a.total > b.total;
        });
    int i = 0;
    int num_new = num < MAX_ADDR_PROT_NUM ? num : MAX_ADDR_PROT_NUM;
    for (i = 0;i < num_new; i += 1) 
    {
        rets[i] = items[i];
        if (items[i].total == 0) 
        {
            i += 1;
            break;
        }
    }
    return i;
}