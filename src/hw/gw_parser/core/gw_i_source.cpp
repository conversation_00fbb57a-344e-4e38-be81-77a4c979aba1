/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include "gw_i_source.h"

/**
 * CSource implementation
 *
 * 网络包数据源基类
 */

CSource::~CSource(void)
{
}

/**
 * 模块初始时调用
 */
void CSource::init()
{
  // ASSERT(m_comm != NULL);
  // m_quit_signal = 0;
}

/**
 * 模块结束时调用
 */
void CSource::fini()
{
  // ASSERT(m_comm != NULL);
}

/**
 * 运行调用
 */
void CSource::run()
{
  // ASSERT(m_comm != NULL);
}

/**
 * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
 */
const char *CSource::get_name(void) const
{
  // return m_name;
  return "";
}

/**
 * 获取版本号。
 */
const char *CSource::get_version(void) const
{
  return "CSource";
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CSource::set_gw_common(CGwCommon *comm)
{
  // m_comm = comm;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CSource::load_conf(const char *)
{
  return true;
}

/**
 * 触发退出信号时处理
 */
void CSource::set_quit_signal(void)
{
  // m_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CSource::wait_for_stop(void)
{
}

/**
 * 设置IP层回调函数（通常为单线程）
 * @param CALLBACK_PKT cb_ip
 * @param void *userdata_ip
 */
int CSource::set_ip_callback(CALLBACK_PKT cb_ip, void *userdata_ip)
{
  return 0;
}

/**
 * 设置TCP层回调函数（一般为多线程）
 * @param CALLBACK_PCAP cb_tcp
 * @param void *userdata_tcp
 */
int CSource::set_tcp_callback(CALLBACK_PCAP cb_tcp, void *userdata_tcp)
{
  return 0;
}

/**
 * 设置采样参数（仅针对DPDK）
 * @param int mode
 * @param int rate
 * @param CALLBACK_SAMPLE cb_sample
 */
void CSource::set_sample_callback(int mode, int rate, CALLBACK_SAMPLE cb_sample)
{
}

/*
* 设置转发回调函数
*
*/
void CSource::set_flow_callback(CALLBACK_FLOW) 
{

}

/**
 * 收集网口信息（针对DPDK）
 * @param eth_info_t *p_st_eth_info
 * @param uint32_t *p_work_port_cnt
 */
int CSource::collect_eth_info(eth_info_t *p_st_eth_info, uint32_t *p_work_port_cnt)
{
  return 0;
}

/**
 *  收集解析pcap文件信息(针对pcap) 
 *  @param pcap_probe_info_t *p_st_probe_info
 */
int CSource::collect_pcap_probe_info(pcap_probe_info_t *p_st_probe_info)
{
  return 0;
}

void CSource::set_tcp_parser(CTcpParser *tcp_parser)
{ 
}
