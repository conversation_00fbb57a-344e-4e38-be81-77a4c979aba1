/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include "gw_i_parser.h"

/**
 * CParser implementation
 *
 * 网络包协议解析基类
 */

CParser::~CParser(void)
{
}

/**
 * 在接收数据时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
bool CParser::probe(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return false;
}

/**
 * 在连接关闭时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
bool CParser::probe_on_close(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return false;
}

/**
 * 在连接重置时，探测数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
bool CParser::probe_on_reset(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return false;
}

/**
 * 在接收数据时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
int CParser::parse(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return 0;
}

/**
 * 在接收数据时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
int CParser::parse_clear(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return 0;
}

/**
 * 在连接关闭时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
int CParser::parse_on_close(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return 0;
}

/**
 * 在连接重置时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream *
 * @paramstruct conn *
 */
int CParser::parse_on_reset(CSessionMgt *, const app_stream *, const struct conn *, CSession*)
{
  return 0;
}

/**
 * 获取当前流解析出来的数据。
 * @param struct StreamData *
 * @param int dir
 * @param int *data_len
 * @param int *offset_out
 */
const char *CParser::get_data(const struct StreamData *, int dir, int *data_len, int *offset_out)
{
  *data_len = 0;
  *offset_out = 0;
  return NULL;
}

/**
 * 已解析处理字节数。
 * @param struct StreamData *
 * @param int dir
 * @param int num
 */
bool CParser::discard(struct StreamData *, int dir, int num)
{
  return false;
}

/**
 * 已处理字节数，同时更新数据。
 * @param struct StreamData *
 * @param int dir
 * @param int num
 */
bool CParser::discard_and_update(struct StreamData *, int dir, int num)
{
  return false;
}

// /**
//  * 删除解析对象中在会话管理中的单边数据。
//  * @param HalfStreamData*
//  */
// void CParser::del_session_half_stream(HalfStreamData *)
// {
//   return;
// }

/**
 * @param StreamData*
 */
void CParser::del_session_stream(StreamData *)
{
}

/**
 * @param SessionMgtData*
 */
void CParser::del_session_param(SessionMgtData *)
{
}

void CParser::init()
{
  // ASSERT(m_comm != NULL);
  // m_quit_signal = 0;
}

void CParser::fini()
{
  // ASSERT(m_comm != NULL);
}

void CParser::run()
{
  // ASSERT(m_comm != NULL);
}

/**
 * 获取对象名。以-为分隔符，前半部分为类名，后半部分为实例地址尾部分。
 */
const char *CParser::get_name(void) const
{
  // return m_name;
  return "CParser";
}

/**
 * 获取版本号。
 */
const char *CParser::get_version(void) const
{
  return "";
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CParser::set_gw_common(CGwCommon *comm)
{
  // m_comm = comm;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CParser::load_conf(const char *)
{
  return true;
}

/**
 * 触发退出信号时处理
 */
void CParser::set_quit_signal(void)
{
  // m_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CParser::wait_for_stop(void)
{
}

/**
 * 设置过滤规则。
 * @param CFilterRule*rule
 */
void CParser::set_url_filter_rule(CFilterRule *rule)
{
}

/**
 *  设置账号过滤规则 
 *  @param CFilterRule *rule
 */
void CParser::set_accout_filter_rule(CFilterRule *rule)
{
}

void CParser::set_upload_filter_rule(CFilterRule *client_rule, CFilterRule *server_rule) 
{

}

/**
 * 增加上层协议解析对象。
 * @param CParser *parser
 */
void CParser::add_upstream(CParser *parser)
{
}

/**
 * 清空上层协议解析对象
 */
void CParser::reset_upstream(void)
{
}

/**
 * 推送到上层消息(异步方式, Json序列化数据)
 * @param char *s
 * @param size_t *length
 */
void CParser::push_upstream_msg(char *s, size_t length)
{
}

/**
 * 是否使用当前协议解析流数据
 * @param struct StreamData*
 */
bool CParser::is_parsed(const struct StreamData *) const
{
  return false;
}

/**
 * 克隆会话流数据到队列中使用(预留)
 * @param struct StreamData*
 */
struct StreamData *CParser::clone_stream_data(const struct StreamData *)
{
  return NULL;
}

/**
 *  获取解析http数量(针对http parser) 
 */
uint64_t CParser::get_parser_http_cnt()
{
  return 0;
}

/**
 *  获取解析http成功的数量(针对http parser) 
 */
uint64_t CParser::get_succ_parser_http_cnt()
{
  return 0;
}

/**
   *  获取解析parser的状态数据，以便于进行查看Parser内部状态
   */
void* CParser::get_parser_status()
{
  return NULL;
}

/**
 * 设置解析对象type
 */
void CParser::set_parser_type(int type)
{

}

void CParser::read_conf_urlbase_for_mon()
{

}
