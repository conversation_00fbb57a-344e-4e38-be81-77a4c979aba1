#include <unistd.h>
#include <iostream>
#include "cpp_utils.h"
#include "utils.h"
#include "gw_common.h"
#include "gw_config.h"
#include "gw_logger.h"
#include "ipfilter_rule.h"
#include "portfilter_rule.h"
#include "gw_i_parser.h"
#include "listen/Listener.h"
#include "nacos_listen_conf.h"
// #include "DebugAssertion.h"
// #include "config/NacosConfigService.h"
#include "constant/PropertyKeyConst.h"
#include "Properties.h"
// #include "init/init.h"
// #include "NacosExceptions.h"
#include "cJSON.h"
#include "Nacos.h"

#define IP_FILTER "ip_filter"
#define IP_WHITE  "ip_white"
#define PORT_FILTER "port_filter"
#define HOST_FILTER "host_filter"
#define HOST_WHITE  "host_white"
#define URL_FILTER "url_filter"
#define URL_WHITE "url_white"

#define BLACK_WHITE_TYPE "blackWhiteType"
#define BLACK_TYPE "BLACK"
#define WHITE_TYPE "WHITE"
#define HOST_KEY    "hosts"
#define URL_KEY    "apiUris"
#define IPS_KEY     "ips"
#define IPSEG_KEY   "ipSeg" 

#define GWHW_MODE_KEY "env.auditapiv2.default"

using namespace nacos;

typedef struct
{
    const char *p_server_addr;
    CNacosListen *p_nacoslisten_obj;
}thread_param_t;

class CkeyChangeListener : public Listener
{
    private:
	    NacosString key;
    protected:
        CNacosListen *m_p_nacos_listen;
    public:
        void setKey(const std::string &_key);
    
        std::string getKey() const;

        void set_nacos_listen(CNacosListen *p_nacos_listen);

        virtual void receiveConfigInfo(const std::string &configInfo)=0;
};

void CkeyChangeListener::CkeyChangeListener::setKey(const std::string &_key)
{
    key = _key;
}

std::string CkeyChangeListener::CkeyChangeListener::getKey() const
{
    return key;
}

void CkeyChangeListener::set_nacos_listen(CNacosListen *p_nacos_listen)
{
    m_p_nacos_listen = p_nacos_listen;
}

class CkeyChangeListenerFilter : public CkeyChangeListener
{
public:
    virtual void receiveConfigInfo(const std::string &configInfo);
};

void CkeyChangeListenerFilter::receiveConfigInfo(const std::string &configInfo)
{
    m_p_nacos_listen->deal_mon_conf(configInfo, this);
    
    return;
}

class CkeyChangeListenerMode : public CkeyChangeListener
{
public:
    virtual void receiveConfigInfo(const std::string &configInfo);
};

void CkeyChangeListenerMode::receiveConfigInfo(const std::string &configInfo)
{
    m_p_nacos_listen->deal_mode_conf(configInfo, this);
    
    return;
}

class CkeyChangeListenerSwitch : public CkeyChangeListener
{
private:
    std::mutex m_lock;
public:
    virtual void receiveConfigInfo(const std::string &configInfo);
};

void CkeyChangeListenerSwitch::receiveConfigInfo(const std::string &configInfo)
{
    std::lock_guard<std::mutex> lock(m_lock);
    m_p_nacos_listen->deal_switch_conf(configInfo, this);
    
    return;
}

class CkeyChangeListenerLimit : public CkeyChangeListener
{
private:
    std::mutex m_lock;
public:
    virtual void receiveConfigInfo(const std::string &configInfo);
};

void CkeyChangeListenerLimit::receiveConfigInfo(const std::string &configInfo)
{
    std::lock_guard<std::mutex> lock(m_lock);
    m_p_nacos_listen->deal_limit_conf(configInfo, this);
    
    return;
}

CNacosListen::CNacosListen(): m_comm(NULL)
                            , m_quit_signal(0)
                            , m_nacos_log_level(4)
{

}

CNacosListen::~CNacosListen()
{
    
}

void CNacosListen::init()
{
    if (!m_comm)
    {
        return;
    }

    load_conf(NULL);

    auto iter = m_mp_nacos_api_param.cbegin();
    for ( ; iter != m_mp_nacos_api_param.cend(); ++iter)
    {
        GWLOG_INFO(m_comm, "server addr = %s\n", (iter->first).c_str());

        for (auto iter_1 = iter->second.begin(); iter_1 != iter->second.end(); ++iter_1)
        {
            GWLOG_INFO(m_comm, "data id = %s\n", (iter_1->str_data_id).c_str());
            GWLOG_INFO(m_comm, "group = %s\n", (iter_1->str_group).c_str());
        }
    }

    if (m_nacos_log_level > 4 || m_nacos_log_level < 0)
    {
        m_nacos_log_level = 4;
    }
    // Init::doInit((LOG_LEVEL)m_nacos_log_level);

    return;
}

void CNacosListen::fini()
{
    auto iter = m_v_change_listener.begin();
    for (; iter != m_v_change_listener.end(); ++iter)
    {
        delete *iter;
    }

    m_v_change_listener.clear();
    m_conf_handle_map.clear();
    m_conf_handle_int_map.clear();
    m_mp_nacos_api_param.clear();

    // Init::doDeinit();
}

void CNacosListen::run()
{
    for (auto iter = m_mp_nacos_api_param.begin(); iter != m_mp_nacos_api_param.end(); ++iter)
    {
        pthread_t nacos_listen;
        thread_param_t *p_st_thread_param = (thread_param_t *)malloc(sizeof(thread_param_t));
        p_st_thread_param->p_server_addr = (iter->first).c_str();
        p_st_thread_param->p_nacoslisten_obj = this;
        int i_ret = pthread_create(&nacos_listen, NULL, (void *(*)(void *))thread_nacos_listen_config, p_st_thread_param);
        if (i_ret == 0)
        {
            GWLOG_INFO(m_comm, "create nacos listen conf thread successfully\n");
            m_v_nacos_listen.push_back(nacos_listen);
        }
        else
        {
            GWLOG_ERROR(m_comm, "create nacos listen conf thread failed\n");
        }
    }

    return;
}

void CNacosListen::set_gw_common(CGwCommon *comm)
{
    assert(comm != NULL);
    m_comm = comm;
}

void CNacosListen::set_quit_signal(void)
{
    m_quit_signal = 1;
}

void CNacosListen::wait_for_stop(void)
{
    for (auto iter = m_v_nacos_listen.begin(); iter != m_v_nacos_listen.end(); ++iter)
    {
        pthread_join(*iter, NULL);
    }
}

bool CNacosListen::load_conf(const char *)
{
    if (!m_comm)
    {
        return false;
    }

    CGwConfig *pgwc = m_comm->get_gw_config();
    if (!pgwc)
    {
        return false;
    }

    m_nacos_log_level = pgwc->read_conf_int("nacos", "nacos_log_level", m_nacos_log_level);
    nacos_api_param_t st_nacos_api_param;

    cJSON *obj_parser_key = NULL;
    int arr_size = pgwc->get_array_size("nacos", "nacos_list", &obj_parser_key);

    for (int i = 0; i < arr_size; ++i)
    {
        cJSON *obj_arr = cJSON_GetArrayItem(obj_parser_key, i);
        cJSON *obj_server_addr = cJSON_GetObjectItem(obj_arr, "nacos_server_addr");

        char *server_addr = obj_server_addr->valuestring;

        cJSON *obj_dataid_group = cJSON_GetObjectItem(obj_arr, "nacos_dataid_group");

        std::vector<nacos_api_param_t> v_nacos_api_param;
        int size = cJSON_GetArraySize(obj_dataid_group);

        for (int j = 0; j < size; j++)
        {
            nacos_api_param_t st_nacos_api_param;
            cJSON *obj_param = cJSON_GetArrayItem(obj_dataid_group, j);
            cJSON *obj_dataid = cJSON_GetObjectItem(obj_param, "data_id");
            cJSON *obj_group = cJSON_GetObjectItem(obj_param, "group");
            cJSON *obj_conftype = cJSON_GetObjectItem(obj_param, "conf_type");

            st_nacos_api_param.str_data_id = obj_dataid->valuestring;
            st_nacos_api_param.str_group = obj_group->valuestring;
            if (obj_conftype)
                st_nacos_api_param.conf_type = obj_conftype->valuestring;

            v_nacos_api_param.push_back(st_nacos_api_param);
        }

        m_mp_nacos_api_param[server_addr] = v_nacos_api_param;

        nacos_auth auth;

        cJSON *obj_auth_enable = cJSON_GetObjectItem(obj_arr, "auth_enable");
        if (NULL == obj_auth_enable)
        {
            GWLOG_INFO(m_comm, "no auth enablt\n");
            continue;
        }

        if (NULL != obj_auth_enable->valuestring)
        {
            auth.auth_enable = atoi(obj_auth_enable->valuestring);//为了配置文件格式统一
        }

        cJSON *obj_auth_username = cJSON_GetObjectItem(obj_arr, "auth_username");
        if (NULL == obj_auth_username)
        {
            GWLOG_INFO(m_comm, "no auth username\n");
            continue;
        }
        auth.auth_username = obj_auth_username->valuestring;

        cJSON *obj_auth_password = cJSON_GetObjectItem(obj_arr, "auth_password");
        if (NULL == obj_auth_password)
        {
            GWLOG_INFO(m_comm, "no auth password\n");
            continue;
        }
        auth.auth_password = obj_auth_password->valuestring;

        m_auth[server_addr] = auth;
    }

    return true;
}

void CNacosListen::add_conf_handle(const std::string &key, std::function<void(const char* s)> f)
{
    if (!key.empty() && f != NULL) 
    {
        m_conf_handle_map[key] = f;
    }
}
void CNacosListen::add_conf_handle_int(const std::string &key, std::function<void(int)> f)
{
    if (!key.empty() && f != NULL) 
    {
        m_conf_handle_int_map[key] = f;
    }
}

int CNacosListen::parser_key(CGwConfig *p_config, const char *section, const char *key, std::string &value, int is_ipseg)
{
    char **pp_array = NULL;
    int  array_size = 0;
    int i = 0;
    int i_ret = 0;

    i_ret = p_config->read_conf_array(section, key, &array_size, &pp_array);
    if (i_ret == 0)
    {
        if (is_ipseg && array_size % 2 != 0)
        {
            for (i = 0; i < array_size; i++)
            {
                SAFE_FREE(pp_array[i]);
            }
            SAFE_FREE(pp_array);
            return -1;
        }

        for (i = 0; i < array_size; i++)
        {
            if (!value.empty())
            {
                value += ",";
            }

            value += std::move(std::string(pp_array[i]));
            if (is_ipseg)
            {
                SAFE_FREE(pp_array[i]);
                i++;
                value += ("-" + std::move(std::string(pp_array[i])));
            }

            SAFE_FREE(pp_array[i]);
        }
       
        SAFE_FREE(pp_array);
    }

    return i_ret;
}

void CNacosListen::mon_ip_conf(std::string &str_ips, std::string &str_ipseg, const char *p_flag)
{
    if (str_ips.empty())
    {
        use_conf_handle(str_ipseg, p_flag);
    }
    else
    {
        if (!str_ipseg.empty())
        {
            str_ips += "," + std::move(str_ipseg);
        }
        use_conf_handle(str_ips, p_flag);
    }

    return;
}

void CNacosListen::use_conf_handle(std::string &str_value, const char *p_key)
{
    auto iter = m_conf_handle_map.find(p_key);
    if (iter != m_conf_handle_map.end())
    {
        iter->second(str_value.c_str());
    }
    
    return;
}

void CNacosListen::use_conf_handle_int(int value, const char *p_key) 
{
    auto iter = m_conf_handle_int_map.find(p_key);
    if (iter != m_conf_handle_int_map.end())
    {
        iter->second(value);
    }
    
    return;
}

void CNacosListen::deal_limit_conf(const std::string &nacos_info, CkeyChangeListener *change_listen)
{
    GWLOG_INFO(m_comm, "nacos key %s, conf value: %s\n", change_listen->getKey().c_str(), nacos_info.c_str());
    if (nacos_info.empty()) {
        return;
    }
    CGwConfig *p_config = new CGwConfig();
    p_config->set_gw_common(m_comm);
    bool result = p_config->load_string(nacos_info.c_str());
    if (!result)
    {
        GWLOG_ERROR(m_comm, "nacos info json format invaild\n");
    }

    int http_drop_percent = p_config->read_conf_int(NULL, "currentFlowLimitRate", 0);
    use_conf_handle_int(http_drop_percent, "http_drop_percent");
    delete p_config;
}

void CNacosListen::deal_switch_conf(const std::string &nacos_info, CkeyChangeListener *change_listen)
{
    GWLOG_INFO(m_comm, "nacos key %s, conf value: %s\n", change_listen->getKey().c_str(), nacos_info.c_str());
    if (nacos_info.empty()) {
        return;
    }
    CGwConfig *p_config = new CGwConfig();
    p_config->set_gw_common(m_comm);
    bool result = p_config->load_string(nacos_info.c_str());
    if (!result)
    {
        GWLOG_ERROR(m_comm, "nacos info json format invaild\n");
    }
    int ssl_parser_mode = p_config->read_conf_int(NULL, "ssl_parser_mode", 0);
    int ssl_parser_enable = p_config->read_conf_int(NULL, "ssl_parser_enable", 0);
    int minio_upload_enable = p_config->read_conf_int(NULL, "minio_upload_enable", 1);
    int ftp_parser_enable = p_config->read_conf_int(NULL, "ftp_parser_enable", 0);
    int http_parser_enable = p_config->read_conf_int(NULL, "http_parser_enable", 0);
    int mail_parser_enable = p_config->read_conf_int(NULL, "mail_parser_enable", 0);
    int nfs_parser_enable = p_config->read_conf_int(NULL, "nfs_parser_enable", 0);
    int smb_parser_enable = p_config->read_conf_int(NULL, "smb_parser_enable", 0);
    int send_empty_http_rsp_enable = p_config->read_conf_int(NULL, "send_empty_http_rsp_enable", 0);
    int send_empty_http_req_enable = p_config->read_conf_int(NULL, "send_empty_http_req_enable", 0);
    int http_upload_protobuf_enable = p_config->read_conf_int(NULL, "http_upload_protobuf_enable", 0);
    int http_upload_file_events_enable = p_config->read_conf_int(NULL, "http_upload_file_events_enable", 1);
    std::string upload_file_type = p_config->read_conf_string(NULL, "upload_file_type");


    use_conf_handle_int(ssl_parser_mode, "ssl_parser_mode");
    use_conf_handle_int(ssl_parser_enable, "ssl_parser_enable");
    use_conf_handle_int(minio_upload_enable, "minio_upload_enable");
    use_conf_handle_int(ftp_parser_enable, "ftp_parser_enable");
    use_conf_handle_int(http_parser_enable, "http_parser_enable");
    use_conf_handle_int(mail_parser_enable, "mail_parser_enable");
    use_conf_handle_int(nfs_parser_enable, "nfs_parser_enable");
    use_conf_handle_int(smb_parser_enable, "smb_parser_enable");
    use_conf_handle_int(send_empty_http_rsp_enable, "send_empty_http_rsp_enable");
    use_conf_handle_int(send_empty_http_req_enable, "send_empty_http_req_enable");
    use_conf_handle_int(http_upload_protobuf_enable, "http_upload_protobuf_enable");
    use_conf_handle_int(http_upload_file_events_enable, "http_upload_file_events_enable");
    use_conf_handle(upload_file_type, "upload_file_type");

    delete p_config;
}

void CNacosListen::deal_mon_conf(const std::string &nacos_info, CkeyChangeListener *change_listen)
{
    GWLOG_INFO(m_comm, "nacos key %s, conf value: %s\n", change_listen->getKey().c_str(), nacos_info.c_str());

    CGwConfig *p_config = new CGwConfig();
    p_config->set_gw_common(m_comm);
    bool result = false;
    std::vector<std::string> v_str_result;

    result = p_config->load_string(nacos_info.c_str());
    if (!result && !nacos_info.empty())
    {
        GWLOG_ERROR(m_comm, "nacos info json format invaild\n");
    }
    
    int array_size = 0;
    array_size = p_config->get_array_size(NULL, NULL, NULL);
    
    const cJSON *p_json = p_config->get_json_conf();
    for (int i = 0; i < array_size; i++)
    {
        cJSON *json_array = cJSON_GetArrayItem(p_json, i);
        char *p_data = cJSON_Print(json_array);
        if (!p_data)
        {
            continue;
        }
        v_str_result.push_back(p_data);
        cJSON_free_safe(p_data);
    }
    
    std::string str_hosts_filter = "";
    std::string str_hosts_white = "";
    std::string str_url_filter = "";
    std::string str_url_white = "";
    std::string str_ips_filter = "";
    std::string str_ipseg_filter = "";
    std::string str_ips_white = "";
    std::string str_ipseg_white = "";

    for (auto iter = v_str_result.begin(); iter != v_str_result.end(); ++iter)
    {
        bool result = p_config->load_string((*iter).c_str());
        if (!result)
        {
            GWLOG_ERROR(m_comm, "parser nacos info json format failed\n");
            delete p_config;
            return;
        }

        std::string black_white_type = p_config->read_conf_string(NULL, BLACK_WHITE_TYPE);
        if (black_white_type != WHITE_TYPE && black_white_type != BLACK_TYPE)
        {
            GWLOG_ERROR(m_comm, "invaild black white type (%s)\n", black_white_type.c_str());
            delete p_config;
            return;
        }

        if (black_white_type == BLACK_TYPE)
        {
			//修复页面同时配置ip，ip掩码引起的ip filter读取失败的问题
			if (!str_ips_filter.empty())
			{
				str_ips_filter = str_ips_filter + ',';		
			}
            parser_key(p_config, NULL, HOST_KEY, str_hosts_filter, 0);
            parser_key(p_config, NULL, URL_KEY, str_url_filter, 0);
            parser_key(p_config, NULL, IPS_KEY, str_ips_filter, 0);
            parser_key(p_config, NULL, IPSEG_KEY, str_ipseg_filter, 1);
        }
        else
        {
            parser_key(p_config, NULL, HOST_KEY, str_hosts_white, 0); 
            parser_key(p_config, NULL, URL_KEY, str_url_white, 0);
            parser_key(p_config, NULL, IPS_KEY, str_ips_white, 0); 
            parser_key(p_config, NULL, IPSEG_KEY, str_ipseg_white, 1);
        }

    }

    use_conf_handle(str_hosts_filter, HOST_FILTER);
    use_conf_handle(str_hosts_white, HOST_WHITE);
    use_conf_handle(str_url_filter, URL_FILTER);
    use_conf_handle(str_url_white, URL_WHITE);
    mon_ip_conf(str_ips_filter, str_ipseg_filter, IP_FILTER);
    mon_ip_conf(str_ips_white, str_ipseg_white, IP_WHITE);

    delete p_config;
}

void CNacosListen::deal_mode_conf(const std::string &nacos_info, CkeyChangeListener *change_listen)
{
    GWLOG_INFO(m_comm, "nacos key %s, conf value: %s\n", change_listen->getKey().c_str(), nacos_info.c_str());
    auto confs = split_string(nacos_info, "\n");
    for (auto conf : confs) 
    {
        //  空行、注释行
        if (conf.size() && conf[0] != '#')
        {
            auto vv = split_string(conf, "=");
            if (vv.size() == 2 && vv[0] == GWHW_MODE_KEY)
            {
                use_conf_handle(vv[1], GWHW_MODE_KEY);
            }
        }
    }
}

int CNacosListen::thread_nacos_listen_config(void *p_arg)
{
    thread_param_t *p_thread_param = (thread_param_t*)p_arg;
    CNacosListen *pThis = p_thread_param->p_nacoslisten_obj;
    
    pThis->nacos_listen_config(p_thread_param->p_server_addr);

    SAFE_FREE(p_arg);
    return 0;
}

int CNacosListen::nacos_listen_config(const char *p_server_addr)
{
    Properties props;
    auto iter_nacos = m_mp_nacos_api_param.find(p_server_addr);
    if (iter_nacos == m_mp_nacos_api_param.end())
    {
        return -1;
    }

    props[PropertyKeyConst::SERVER_ADDR] = iter_nacos->first;
    props[PropertyKeyConst::LOG_PATH] = "/opt/apigw/gwhw/nacos";

    auto iter_auth = m_auth.find(p_server_addr);
    if (iter_auth != m_auth.end() && iter_auth->second.auth_enable)
    {
        props[PropertyKeyConst::AUTH_USERNAME] = iter_auth->second.auth_username;
        props[PropertyKeyConst::AUTH_PASSWORD] = iter_auth->second.auth_password;
    }

    INacosServiceFactory *factory = NacosFactoryFactory::getNacosFactory(props);
    ResourceGuard <INacosServiceFactory> _guardFactory(factory);
    ConfigService *n = NULL;
    try
    {
        n = factory->CreateConfigService();
    }
    catch (std::exception &e)
    {
        GWLOG_ERROR(m_comm, "nacos init fail, %s, server addr=%s, username=%s\n", e.what(), iter_nacos->first.c_str(), iter_auth->second.auth_username.c_str());
        return -1;
    }

    ResourceGuard <ConfigService> _serviceFactory(n);

    auto iter = (iter_nacos->second).cbegin();
    for (; iter != (iter_nacos->second).cend(); ++iter)
    {
        CkeyChangeListener *thelistener = NULL;
        if (iter->conf_type == "" || iter->conf_type == "filter")
        {
            GWLOG_INFO(m_comm, "create filter listener\n");
            thelistener = new CkeyChangeListenerFilter();
        }
        else if (iter->conf_type == "1" || iter->conf_type == "mode")
        {
            GWLOG_INFO(m_comm, "create mode listener\n");
            thelistener = new CkeyChangeListenerMode();
        }
        else if (iter->conf_type == "switch") 
        {
            GWLOG_INFO(m_comm, "create switch listener\n");
            thelistener = new CkeyChangeListenerSwitch();
        }
        else if (iter->conf_type == "limit")
        {
            GWLOG_INFO(m_comm, "create limit listener\n");
            thelistener = new CkeyChangeListenerLimit();
        }
        else 
        {
            GWLOG_ERROR(m_comm, "unknow conf_type: %d\n", iter->conf_type.c_str());
            continue;
        }
        
        try
        {
            thelistener->setKey(iter->str_data_id);
            thelistener->set_nacos_listen(this);
            std::string data = n->getConfig(iter->str_data_id, iter->str_group, 1000);
            if (!data.empty())
            {
                thelistener->receiveConfigInfo(data);
            }
        }
        catch (std::exception &e)
        {
            GWLOG_WARN(m_comm, "try get data id=%s, group id=%s config fail, %s\n", iter->str_data_id.c_str(), iter->str_group.c_str(), e.what());
        }

        while (!m_quit_signal)
        {
            try
            {
                n->addListener(iter->str_data_id, iter->str_group, thelistener);
                GWLOG_INFO(m_comm, "add data id=%s\n", iter->str_data_id.c_str());
                break;
            }
            catch (std::exception &e)
            {
                //GWLOG_ERROR(m_comm, "addListener failed(%s)\n", e.what());
                sleep(10);
            }
        }
        
        m_v_change_listener.push_back(thelistener);
    }

    while (!m_quit_signal)
    {
        sleep(1);
    }

    return 0;
}
