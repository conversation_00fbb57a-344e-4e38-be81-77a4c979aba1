/*

*/

#ifndef _PROTO_PARSER_UTIL_H
#define _PROTO_PARSER_UTIL_H
#include "utils.h"

#define mknew(x) (x *)test_malloc(sizeof(x))
#define b_comp(x, y) (!memcmp(&(x), &(y), sizeof(x)))

typedef void(fastcall *proc_func)(void *, void *, int);
typedef void(fastcall *lurker_func)(void *, void *, void **);

typedef void(fastcall *addr_port_hit_func)(void *, unsigned int, short, unsigned int, short ,unsigned int ,int );
typedef int(fastcall *port_filter_hit_func)(void *, unsigned short);
typedef int(fastcall *port_white_hit_func)(void *, unsigned short);
typedef int(fastcall *single_stream_hit_func)(void *, void* , int , int , int);

struct proc_node
{
  proc_func item;
  struct proc_node *next;
};

struct lurker_node
{
  lurker_func item;
  void *data;
  char whatto;
  struct lurker_node *next;
};

void gw_pp_no_mem(const char *);
char *test_malloc(int);
void register_callback(struct proc_node **procs, void(*x));
void unregister_callback(struct proc_node **procs, void(*x));

static inline int
before(u_int seq1, u_int seq2)
{
  return ((int)(seq1 - seq2) < 0);
}

static inline int
after(u_int seq1, u_int seq2)
{
  return ((int)(seq2 - seq1) < 0);
}

#endif /* _PROTO_PARSER_UTIL_H */
