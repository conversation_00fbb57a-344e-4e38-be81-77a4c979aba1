/*
  
*/

#include <sys/types.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <netinet/in_systm.h>
#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <arpa/inet.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include "pp.h"
//#include "pp_checksum.h"
#include "ip_fragment.h"
//#include "pp_tcp.h"
#include "util.h"

#define IP_CE 0x8000     /* Flag: "Congestion" */
#define IP_DF 0x4000     /* Flag: "Don't Fragment" */
#define IP_MF 0x2000     /* Flag: "More Fragments" */
#define IP_OFFSET 0x1FFF /* "Fragment Offset" part */

#define IP_FRAG_TIME (30 * 1000) /* fragment lifetime */

#define UNUSED 314159
#define FREE_READ UNUSED
#define FREE_WRITE UNUSED
#define GFP_ATOMIC UNUSED
#define NETDEBUG(x)

struct sk_buff
{
  char *data;
  int truesize;
};

struct timer_list
{
  struct timer_list *prev;
  struct timer_list *next;
  int expires;
  // void (*function)();
  void (*function)(void *, unsigned long);
  unsigned long data;
  // struct ipq *frags;
};

struct hostfrags
{
  struct ipq *ipqueue;
  struct ipq6 *ip6queue;
  int ip_frag_mem;
  u_int ip;
  int hash_index;
  struct hostfrags *prev;
  struct hostfrags *next;
};

/* Describe an IP fragment. */
struct ipfrag
{
  int offset;          /* offset of fragment in IP datagram    */
  int end;             /* last byte of data in datagram        */
  int len;             /* length of this fragment              */
  struct sk_buff *skb; /* complete received fragment           */
  unsigned char *ptr;  /* pointer into real fragment data      */
  struct ipfrag *next; /* linked list pointers                 */
  struct ipfrag *prev;
};

/* Describe an entry in the "incomplete datagrams" queue. */
struct ipq
{
  unsigned char *mac;       /* pointer to MAC header                */
  struct ip *iph;           /* pointer to IP header                 */
  int len;                  /* total length of original datagram    */
  short ihlen;              /* length of the IP header              */
  short maclen;             /* length of the MAC header             */
  struct timer_list timer;  /* when will this queue expire?         */
  struct ipfrag *fragments; /* linked list of received fragments    */
  struct hostfrags *hf;
  struct ipq *next; /* linked list pointers                 */
  struct ipq *prev;
  // struct device *dev;	/* Device - for icmp replies */
};

struct ipq6
{
  struct ip6_hdr *ip6h;     /* pointer to IPv6 header                             */
  int len;                  /* total length of original datagram                  */
  unsigned defrag_iden;     /* defrangment ID */
  short ihlen;              /* length of the IPv6 header(include excliude header) */
  struct timer_list timer;   /* when will this queue expire?                       */
  struct ipfrag *fragments; /* linked list of received fragments                  */
  struct hostfrags *hf;
  struct ipq6 *prev;
  struct ipq6 *next;
};

/*
  Fragment cache limits. We will commit 256K at one time. Should we
  cross that limit we will prune down to 192K. This should cope with
  even the most extreme cases without allowing an attacker to
  measurably harm machine performance.
*/
#define IPFRAG_HIGH_THRESH (256 * 1024)
#define IPFRAG_LOW_THRESH (192 * 1024)

/*
  This fragment handler is a bit of a heap. On the other hand it works
  quite happily and handles things quite well.
*/

volatile ip_reassemble_lost_packets reassemble_lost;

static int
jiffies(void *par)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct timeval tv;

  if (pwp->timenow)
    return pwp->timenow;
  gettimeofday(&tv, 0);
  pwp->timenow = (tv.tv_sec - pwp->time0) * 1000 + tv.tv_usec / 1000;

  return pwp->timenow;
}

/* Memory Tracking Functions */
static void
atomic_sub(int ile, int *co)
{
  *co -= ile;
}

static void
atomic_add(int ile, int *co)
{
  *co += ile;
}

static void
kfree_skb(struct sk_buff *skb, int type)
{
  (void)type;
  free(skb);
}

static void
panic(const char *str)
{
  fprintf(stderr, "%s", str);
  exit(1);
}

static void
add_timer(void *par, struct timer_list *x)
{
  worker_params_t *pwp = (worker_params_t *)par;
  if (pwp->timer_tail)
  {
    pwp->timer_tail->next = x;
    x->prev = pwp->timer_tail;
    x->next = 0;
    pwp->timer_tail = x;
  }
  else
  {
    x->prev = 0;
    x->next = 0;
    pwp->timer_tail = pwp->timer_head = x;
  }
}

static void
del_timer(void *par, struct timer_list *x)
{
  worker_params_t *pwp = (worker_params_t *)par;
  if (x->prev)
    x->prev->next = x->next;
  else
    pwp->timer_head = x->next;
  if (x->next)
    x->next->prev = x->prev;
  else
    pwp->timer_tail = x->prev;
}

static void
frag_kfree_skb(void *par, struct sk_buff *skb, int type)
{
  worker_params_t *pwp = (worker_params_t *)par;
  if (pwp->this_host)
    atomic_sub(skb->truesize, &pwp->this_host->ip_frag_mem);
  kfree_skb(skb, type);
}

static void
frag_kfree_s(void *par, void *ptr, int len)
{
  worker_params_t *pwp = (worker_params_t *)par;
  if (pwp->this_host)
    atomic_sub(len, &pwp->this_host->ip_frag_mem);
  free(ptr);
}

static void *
frag_kmalloc(void *par, int size, int dummy)
{
  worker_params_t *pwp = (worker_params_t *)par;
  void *vp = (void *)malloc(size);
  (void)dummy;
  if (!vp)
    return NULL;
  atomic_add(size, &pwp->this_host->ip_frag_mem);

  return vp;
}

/* Create a new fragment entry. */
static struct ipfrag *
ip_frag_create(void *par, int offset, int end, struct sk_buff *skb, unsigned char *ptr)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipfrag *fp;

  fp = (struct ipfrag *)frag_kmalloc(pwp, sizeof(struct ipfrag), GFP_ATOMIC);
  if (fp == NULL)
  {
    // NETDEBUG(printk("IP: frag_create: no memory left !\n"));
    gw_pp_params.no_mem("ip_frag_create");
    return (NULL);
  }
  memset(fp, 0, sizeof(struct ipfrag));

  /* Fill in the structure. */
  fp->offset = offset;
  fp->end = end;
  fp->len = end - offset;
  fp->skb = skb;
  fp->ptr = ptr;

  /* Charge for the SKB as well. */
  pwp->this_host->ip_frag_mem += skb->truesize;

  return (fp);
}

static int
frag_index(void *par, struct ip *iph)
{
  worker_params_t *pwp = (worker_params_t *)par;
  unsigned int ip = ntohl(iph->ip_dst.s_addr);

  return (ip % pwp->hash_size);
}

static int frag_ip6_index(void *par, struct ip6_hdr *ip6h)
{
  worker_params_t *pwp = (worker_params_t *)par;
  unsigned ip6 = ntohl(ip6h->ip6_dst.s6_addr32[3]);

  return (ip6 % pwp->hash_size);
}

static int
hostfrag_find(void *par, struct ip *iph)
{
  worker_params_t *pwp = (worker_params_t *)par;
  int hash_index = frag_index(pwp, iph);
  struct hostfrags *hf;

  pwp->this_host = 0;
  for (hf = pwp->fragtable[hash_index]; hf; hf = hf->next)
  {
    if (hf->ip == iph->ip_dst.s_addr)
    {
      pwp->this_host = hf;
      break;
    }
  }
    
  if (!pwp->this_host)
    return 0;
  else
    return 1;
}

static int hostfrag_ip6_find(void *par, struct ip6_hdr *ip6h)
{
  worker_params_t *pwp = (worker_params_t *)par;
  /* 使用IPv6目的地址最后32位生成index */
  int hash_index = 0;
  hash_index = frag_ip6_index(pwp, ip6h);
 
  struct hostfrags *hf = NULL;

  pwp->this_host = 0;
  for (hf = pwp->fragtable[hash_index]; hf; hf = hf->next)
  {
    if (hf->ip == ip6h->ip6_dst.s6_addr32[3])
    {
      pwp->this_host = hf;
      break;
    }
  }

  if (!pwp->this_host)
  {
    return 0;
  }
  return 1;
}

static void
hostfrag_create(void *par, struct ip *iph)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct hostfrags *hf = mknew(struct hostfrags);
  int hash_index = frag_index(pwp, iph);

  hf->prev = 0;
  hf->next = pwp->fragtable[hash_index];
  if (hf->next)
    hf->next->prev = hf;
  pwp->fragtable[hash_index] = hf;
  hf->ip = iph->ip_dst.s_addr;
  hf->ipqueue = 0;
  hf->ip_frag_mem = 0;
  hf->hash_index = hash_index;
  pwp->this_host = hf;
}

static void hostfrag_ip6_create(void *par, struct ip6_hdr *ip6h)
{
  worker_params_t *pwp = (worker_params_t *)par;
  /* 申请一个hostfrag */
  struct hostfrags *hf = mknew(struct hostfrags);
  int hash_index = frag_ip6_index(pwp, ip6h);
  
  hf->prev = 0;
  hf->next = pwp->fragtable[hash_index];
  if (hf->next)
  {
    hf->next->prev = hf;
  }
  pwp->fragtable[hash_index] = hf;

  hf->hash_index = hash_index;
  hf->ip6queue = 0;
  hf->ip_frag_mem = 0;
  hf->ip = ip6h->ip6_dst.s6_addr32[3];
  pwp->this_host = hf;
}

static void
rmthis_host(void *par)
{
  worker_params_t *pwp = (worker_params_t *)par;
  int hash_index = pwp->this_host->hash_index;

  if (pwp->this_host->prev)
  {
    pwp->this_host->prev->next = pwp->this_host->next;
    if (pwp->this_host->next)
      pwp->this_host->next->prev = pwp->this_host->prev;
  }
  else
  {
    pwp->fragtable[hash_index] = pwp->this_host->next;
    if (pwp->this_host->next)
      pwp->this_host->next->prev = 0;
  }
  free(pwp->this_host);
  pwp->this_host = 0;
}

/*
  Find the correct entry in the "incomplete datagrams" queue for this
  IP datagram, and return the queue entry address if found.
*/
static struct ipq *
ip_find(void *par, struct ip *iph)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipq *qp;
  // __attribute__((unused)) struct ipq *qplast;

  //qplast = NULL;
  for (qp = pwp->this_host->ipqueue; qp != NULL; qp = qp->next)
  {
    if (iph->ip_id == qp->iph->ip_id &&
        iph->ip_src.s_addr == qp->iph->ip_src.s_addr &&
        iph->ip_dst.s_addr == qp->iph->ip_dst.s_addr &&
        iph->ip_p == qp->iph->ip_p)
    {
      del_timer(pwp, &qp->timer); /* So it doesn't vanish on us. The timer will
				   be reset anyway */
      return (qp);
    }
  }
  return (NULL);
}

static struct ipq6* ip6_find(void *par, struct ip6_hdr *ip6h)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipq6 *qp6 = NULL;
  //struct ipq6 *qp6_last = NULL;

  for (qp6 = pwp->this_host->ip6queue; qp6; qp6 = qp6->next)
  {
    if (memcmp(qp6->ip6h->ip6_src.s6_addr16, ip6h->ip6_src.s6_addr16, sizeof(ip6h->ip6_src.s6_addr16)) == 0
     && memcmp(qp6->ip6h->ip6_src.s6_addr16, ip6h->ip6_src.s6_addr16, sizeof(ip6h->ip6_src.s6_addr16)) == 0
     && qp6->defrag_iden == pwp->st_ip6_defrag_info.u_idenfy_id)
    {
      del_timer(pwp, &qp6->timer);
      return qp6;
    }
  }

  return NULL;
}

/*
  Remove an entry from the "incomplete datagrams" queue, either
  because we completed, reassembled and processed it, or because it
  timed out.
*/
static int
ip_free(void *par, struct ipq *qp)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipfrag *fp;
  struct ipfrag *xp;
  int free_num = 0;

  /* Stop the timer for this entry. */
  del_timer(pwp, &qp->timer);

  /* Remove this entry from the "incomplete datagrams" queue. */
  if (qp->prev == NULL)
  {
    pwp->this_host->ipqueue = qp->next;
    if (pwp->this_host->ipqueue != NULL)
      pwp->this_host->ipqueue->prev = NULL;
    else
      rmthis_host(pwp);
  }
  else
  {
    qp->prev->next = qp->next;
    if (qp->next != NULL)
      qp->next->prev = qp->prev;
  }
  /* Release all fragment data. */
  fp = qp->fragments;
  while (fp != NULL)
  {
    xp = fp->next;
    frag_kfree_skb(pwp, fp->skb, FREE_READ);
    frag_kfree_s(pwp, fp, sizeof(struct ipfrag));
    fp = xp;
    free_num++;
  }
  /* Release the IP header. */
  frag_kfree_s(pwp, qp->iph, 64 + 8);

  /* Finally, release the queue descriptor itself. */
  frag_kfree_s(pwp, qp, sizeof(struct ipq));
  return free_num;
}

static int ip6_free(void *par, struct ipq6 *pq6)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipfrag *fp = NULL;
  struct ipfrag *xp = NULL;
  int free_num = 0;
  del_timer(pwp, &pq6->timer);
  if (pq6->prev == NULL)
  {
    pwp->this_host->ip6queue = pq6->next;
    if (pwp->this_host->ip6queue != NULL)
    {
      pwp->this_host->ip6queue->prev = NULL;
    }
    else
    {
      rmthis_host(pwp);
    }
  }
  else
  {
    pq6->prev->next = pq6->next;
    if (pq6->next != NULL)
    {
      pq6->next->prev = pq6->prev;
    }   
  }

  /* Release all fragment data. */
  fp = pq6->fragments;
  while (fp != NULL)
  {
    xp = fp->next;
    frag_kfree_skb(pwp, fp->skb, FREE_READ);
    frag_kfree_s(pwp, fp, sizeof(struct ipfrag));
    fp = xp;
    free_num++;
  }

  frag_kfree_s(pwp, pq6->ip6h, pq6->ihlen - 8);
  frag_kfree_s(pwp, pq6, sizeof(struct ipq6));
  return free_num;
}

/* Oops- a fragment queue timed out.  Kill it and send an ICMP reply. */
static void
ip_expire(void *par, unsigned long arg)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipq *qp;

  qp = (struct ipq *)arg;

  /* Nuke the fragment queue. */
  int num = ip_free(pwp, qp);
  __sync_fetch_and_add(&(reassemble_lost.timeout),num);
}

static void ip6_expire(void *par, unsigned long arg)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipq6 *pq6;

  pq6 = (struct ipq6 *)arg;

  /* Nuke the fragment queue. */
  int num = ip6_free(pwp, pq6);
  __sync_fetch_and_add(&(reassemble_lost.timeout),num);
}

/*
  Memory limiting on fragments. Evictor trashes the oldest fragment
  queue until we are back under the low threshold.
*/
static void
ip_evictor(void *par)
{
  worker_params_t *pwp = (worker_params_t *)par;
  // fprintf(stderr, "ip_evict:numpack=%i\n", pwp->numpack);
  while (pwp->this_host && pwp->this_host->ip_frag_mem > IPFRAG_LOW_THRESH)
  {
    if (!pwp->this_host->ipqueue)
      panic("ip_evictor: memcount");
    int num = ip_free(pwp, pwp->this_host->ipqueue);
    __sync_fetch_and_add(&(reassemble_lost.over_cache),num);
  }
}

static void ip6_evictor(void *par)
{
  worker_params_t *pwp = (worker_params_t *)par;
  // fprintf(stderr, "ip_evict:numpack=%i\n", pwp->numpack);
  while (pwp->this_host && pwp->this_host->ip_frag_mem > IPFRAG_LOW_THRESH)
  {
    if (!pwp->this_host->ip6queue)
    {
      panic("ip_evictor: memcount");
    }
    int num = ip6_free(pwp, pwp->this_host->ip6queue);
    __sync_fetch_and_add(&(reassemble_lost.over_cache),num);
  }
}

/*
  Add an entry to the 'ipq' queue for a newly received IP datagram.
  We will (hopefully :-) receive all other fragments of this datagram
  in time, so we just create a queue for this datagram, in which we
  will insert the received fragments at their respective positions.
*/
static struct ipq *
ip_create(void *par, struct ip *iph)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipq *qp;
  int ihlen;

  qp = (struct ipq *)frag_kmalloc(pwp, sizeof(struct ipq), GFP_ATOMIC);
  if (qp == NULL)
  {
    // NETDEBUG(printk("IP: create: no memory left !\n"));
    gw_pp_params.no_mem("ip_create");
    return (NULL);
  }
  memset(qp, 0, sizeof(struct ipq));

  /* Allocate memory for the IP header (plus 8 octets for ICMP). */
  ihlen = iph->ip_hl * 4;
  qp->iph = (struct ip *)frag_kmalloc(pwp, 64 + 8, GFP_ATOMIC);
  if (qp->iph == NULL)
  {
    //NETDEBUG(printk("IP: create: no memory left !\n"));
    gw_pp_params.no_mem("ip_create");
    frag_kfree_s(pwp, qp, sizeof(struct ipq));
    return (NULL);
  }
  memcpy(qp->iph, iph, ihlen + 8);
  qp->len = 0;
  qp->ihlen = ihlen;
  qp->fragments = NULL;
  qp->hf = pwp->this_host;

  /* Start a timer for this entry. */
  qp->timer.expires = jiffies(pwp) + IP_FRAG_TIME; /* about 30 seconds     */
  qp->timer.data = (unsigned long)qp;              /* pointer to queue     */
  qp->timer.function = ip_expire;                  /* expire function      */
  add_timer(pwp, &qp->timer);

  /* Add this entry to the queue. */
  qp->prev = NULL;
  qp->next = pwp->this_host->ipqueue;
  if (qp->next != NULL)
    qp->next->prev = qp;
  pwp->this_host->ipqueue = qp;

  return (qp);
}

static struct ipq6* ip6_create(void *par, struct ip6_hdr *ip6h)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipq6 *pq6 = NULL;

  pq6 = (struct ipq6*)frag_kmalloc(pwp, sizeof(struct ipq6), GFP_ATOMIC);
  if (pq6 == NULL)
  {
    gw_pp_params.no_mem("ipv6 create");
    return NULL;
  }

  memset(pq6, 0, sizeof(struct ipq6));

  int i6hlen = pwp->i_ipv6_header_len - 8;

  /* 不拷贝分段头部信息(分段头部信息固定长度为8字节) */
  pq6->ip6h = (struct ip6_hdr*)frag_kmalloc(pwp, i6hlen, GFP_ATOMIC);
  if (pq6->ip6h == NULL)
  {
    gw_pp_params.no_mem("ipv6 create");
    frag_kfree_s(pwp, pq6, sizeof(struct ipq6));
    return NULL;
  }

  memcpy(pq6->ip6h, ip6h, i6hlen);
  pq6->len = 0;
  pq6->defrag_iden = pwp->st_ip6_defrag_info.u_idenfy_id;
  pq6->ihlen = i6hlen;
  pq6->hf = pwp->this_host;
  pq6->fragments = NULL;

  pq6->timer.expires = jiffies(pwp) + IP_FRAG_TIME;
  pq6->timer.data = (unsigned long)pq6;
  pq6->timer.function = ip6_expire;
  add_timer(pwp, &pq6->timer);

  pq6->prev = NULL;
  pq6->next = pwp->this_host->ip6queue;
  if (pq6->next != NULL)
  {
    pq6->next->prev = pq6;
  }

  pwp->this_host->ip6queue = pq6;

  return pq6;
}

/* See if a fragment queue is complete. */
static int
ip_done(struct ipq *qp)
{
  struct ipfrag *fp;
  int offset;

  /* Only possible if we received the final fragment. */
  if (qp->len == 0)
    return (0);

  /* Check all fragment offsets to see if they connect. */
  fp = qp->fragments;
  offset = 0;
  while (fp != NULL)
  {
    if (fp->offset > offset)
      return (0); /* fragment(s) missing */
    offset = fp->end;
    fp = fp->next;
  }
  /* All fragments are present. */
  return (1);
}

/* See if a fragment queue is complete. */
static int ip6_done(struct ipq6 *pq6)
{
  struct ipfrag *fp = NULL;
  int offset = 0;

  if (pq6->len == 0)
  {
    return 0;
  }

  fp = pq6->fragments;
  while (fp != NULL)
  {
    if (fp->offset > offset)
    {
      return 0;
    }

    offset = fp->end;
    fp = fp->next;
  }

  return 1;
}

/*
  Build a new IP datagram from all its fragments.
 
  FIXME: We copy here because we lack an effective way of handling
  lists of bits on input. Until the new skb data handling is in I'm
  not going to touch this with a bargepole.
*/
static char *
ip_glue(void *par, struct ipq *qp)
{
  worker_params_t *pwp = (worker_params_t *)par;
  char *skb;
  struct ip *iph;
  struct ipfrag *fp;
  unsigned char *ptr;
  int count, len;

  /* Allocate a new buffer for the datagram. */
  len = qp->ihlen + qp->len;

  if (len > 65535)
  {
    gw_pp_params.syslog(PP_WARN_IP, PP_WARN_IP_OVERSIZED, qp->iph, 0, 0);
    int num = ip_free(pwp, qp);
    __sync_fetch_and_add(&(reassemble_lost.failed),num);
    return NULL;
  }
  if ((skb = (char *)malloc(len)) == NULL)
  {
    // NETDEBUG(printk("IP: queue_glue: no memory for gluing queue %p\n", qp));
    gw_pp_params.no_mem("ip_glue");
    int num = ip_free(pwp, qp);
    __sync_fetch_and_add(&(reassemble_lost.no_memory),num);
    return (NULL);
  }
  /* Fill in the basic details. */
  ptr = (unsigned char *)skb;
  memcpy(ptr, ((unsigned char *)qp->iph), qp->ihlen);
  ptr += qp->ihlen;
  count = 0;

  /* Copy the data portions of all fragments into the new buffer. */
  fp = qp->fragments;
  while (fp != NULL)
  {
    if (fp->len < 0 || fp->offset + qp->ihlen + fp->len > len)
    {
      //NETDEBUG(printk("Invalid fragment list: Fragment over size.\n"));
      gw_pp_params.syslog(PP_WARN_IP, PP_WARN_IP_INVLIST, qp->iph, 0, 0);
      int num = ip_free(pwp, qp);
      __sync_fetch_and_add(&(reassemble_lost.failed),num);
      //kfree_skb(skb, FREE_WRITE);
      //ip_statistics.IpReasmFails++;
      free(skb);
      return NULL;
    }
    memcpy((ptr + fp->offset), fp->ptr, fp->len);
    count += fp->len;
    fp = fp->next;
  }
  /* We glued together all fragments, so remove the queue entry. */
  ip_free(pwp, qp);

  /* Done with all fragments. Fixup the new IP header. */
  iph = (struct ip *)skb;
  iph->ip_off = 0;
  iph->ip_len = htons((iph->ip_hl * 4) + count);
  // skb->ip_hdr = iph;

  return (skb);
}

static char *ip6_glue(void *par, struct ipq6 *pq6)
{
  worker_params_t *pwp = (worker_params_t *)par;
  char *skb = NULL;
  struct ip6_hdr *ip6h = NULL;
  struct ipfrag *fp = NULL;
  unsigned char *ptr = NULL;
  int count = 0;
  int len = 0;
  int i6hlen = 0;
  
  len = pq6->ihlen + pq6->len;
  if (len > 65535)
  {
    gw_pp_params.syslog(PP_WARN_IP6, PP_WARN_IP6_OVERSIZED, ip6h, 0, 1);
    int num = ip6_free(pwp, pq6);
    __sync_fetch_and_add(&(reassemble_lost.failed),num);
    return NULL;
  }

  if ((skb = (char *)malloc(len)) == NULL)
  {
    gw_pp_params.no_mem("ipv6_glue");
    int num = ip6_free(pwp, pq6);
    __sync_fetch_and_add(&(reassemble_lost.no_memory),num);
    return NULL;
  }

  ptr = (unsigned char*)skb;
  /* 去除分段头，将指向分段头的信息指向4层协议 */
  uint8_t next_header_type = pq6->ip6h->ip6_nxt;
  i6hlen -= sizeof(struct ip6_hdr);

  if (next_header_type == IPPROTO_FRAGMENT)
  {
    pq6->ip6h->ip6_nxt = pwp->u8_l4_protocol;
  }
  else
  {
    unsigned char* tmp = (unsigned char*)pq6->ip6h + sizeof(struct ip6_hdr);
    while(i6hlen > 0)
    {
      struct ip6_hbh *p_st_hbh = NULL;     /* Hop-by-Hop options header */
      struct ip6_rthdr *p_st_rthdt = NULL; /* routing header */
      struct ip6_dest *p_st_dest = NULL;   /* destination options header */
      if (next_header_type == IPPROTO_HOPOPTS)
      {
        p_st_hbh = (struct ip6_hbh *)((char *)tmp);
        next_header_type = p_st_hbh->ip6h_nxt;
        if (next_header_type == IPPROTO_FRAGMENT)
        {
          p_st_hbh->ip6h_nxt = pwp->u8_l4_protocol;
          break;
        }
        tmp += (p_st_hbh->ip6h_len + 1);
        i6hlen -= (p_st_hbh->ip6h_len + 1);
      }
      else if (next_header_type == IPPROTO_ROUTING)
      {
        p_st_rthdt = (struct ip6_rthdr *)((char *)tmp);
        next_header_type = p_st_rthdt->ip6r_nxt;
        if (next_header_type == IPPROTO_FRAGMENT)
        {
          p_st_rthdt->ip6r_nxt = pwp->u8_l4_protocol;
          break;
        }
        tmp += (p_st_rthdt->ip6r_len + 1);
        i6hlen -= (p_st_rthdt->ip6r_len + 1);
      }
      else if (next_header_type == IPPROTO_DSTOPTS) 
      {
        p_st_dest = (struct ip6_dest*)((char*)tmp);
        next_header_type = p_st_dest->ip6d_nxt;
        if (next_header_type == IPPROTO_FRAGMENT)
        {
          p_st_dest->ip6d_nxt = pwp->u8_l4_protocol;
          break;
        }
        tmp += (p_st_dest->ip6d_len + 1);
        i6hlen -= (p_st_dest->ip6d_len + 1);
      }  
      else
      {
        break;
      }   
    }
  }

  memcpy(ptr, (unsigned char*)pq6->ip6h, pq6->ihlen);
  ptr += pq6->ihlen;
  fp = pq6->fragments;
  while (fp != NULL)
  {
    if (fp->len < 0 || fp->offset + pq6->ihlen + fp->len > len)
    {
      int num = ip6_free(pwp, pq6);
      __sync_fetch_and_add(&(reassemble_lost.failed),num);
      free(skb);
      return NULL;
    }

    memcpy((ptr + fp->offset), fp->ptr, fp->len);
    count += fp->len;
    fp = fp->next;
  }

  
  ip6h = (struct ip6_hdr*)skb;
  ip6h->ip6_plen = htons(count + pq6->ihlen - sizeof(struct ip6_hdr)); /* 转化网络字节序 */
  /* 更新ipv6的数据头长度，减去分段头部长度 */
    pwp->i_ipv6_header_len -= 8;
  ip6_free(pwp, pq6);

  return skb;
}

/* Process an incoming IP datagram fragment. */
static char *
ip_defrag(void *par, struct ip *iph, struct sk_buff *skb)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipfrag *prev, *next, *tmp;
  struct ipfrag *tfp;
  struct ipq *qp;
  char *skb2;
  unsigned char *ptr;
  int flags, offset;
  int i, ihl, end;

  if (!hostfrag_find(pwp, iph) && skb)
    hostfrag_create(pwp, iph);

  /* Start by cleaning up the memory. */
  if (pwp->this_host)
    if (pwp->this_host->ip_frag_mem > IPFRAG_HIGH_THRESH)
      ip_evictor(pwp);

  /* Find the entry of this IP datagram in the "incomplete datagrams" queue. */
  if (pwp->this_host)
    qp = ip_find(pwp, iph);
  else
    qp = 0;

  /* Is this a non-fragmented datagram? */
  offset = ntohs(iph->ip_off);
  flags = offset & ~IP_OFFSET;
  offset &= IP_OFFSET;
  if (((flags & IP_MF) == 0) && (offset == 0))
  {
    if (qp != NULL)
      ip_free(pwp, qp); /* Fragmented frame replaced by full
				   unfragmented copy */
    return 0;
  }

  /* ip_evictor() could have removed all queues for the current host */
  if (!pwp->this_host)
    hostfrag_create(pwp, iph);

  offset <<= 3; /* offset is in 8-byte chunks */
  ihl = iph->ip_hl * 4;

  /*
    If the queue already existed, keep restarting its timer as long as
    we still are receiving fragments.  Otherwise, create a fresh queue
    entry.
  */
  if (qp != NULL)
  {
    /* ANK. If the first fragment is received, we should remember the correct
       IP header (with options) */
    if (offset == 0)
    {
      qp->ihlen = ihl;
      memcpy(qp->iph, iph, ihl + 8);
    }
    del_timer(pwp, &qp->timer);
    qp->timer.expires = jiffies(pwp) + IP_FRAG_TIME; /* about 30 seconds */
    qp->timer.data = (unsigned long)qp;              /* pointer to queue */
    qp->timer.function = ip_expire;                  /* expire function */
    add_timer(pwp, &qp->timer);
  }
  else
  {
    /* If we failed to create it, then discard the frame. */
    if ((qp = ip_create(pwp, iph)) == NULL)
    {
      kfree_skb(skb, FREE_READ);
      __sync_fetch_and_add(&(reassemble_lost.no_memory),1);
      return NULL;
    }
  }
  /* Attempt to construct an oversize packet. */
  if (ntohs(iph->ip_len) + (int)offset > 65535)
  {
    gw_pp_params.syslog(PP_WARN_IP, PP_WARN_IP_OVERSIZED, iph, 0, 0);
    kfree_skb(skb, FREE_READ);
    __sync_fetch_and_add(&(reassemble_lost.failed),1);
    return NULL;
  }
  /* Determine the position of this fragment. */
  end = offset + ntohs(iph->ip_len) - ihl;

  /* Point into the IP datagram 'data' part. */
  if (!skb || !skb->data)
  {
    return NULL;  
  }
  ptr = (unsigned char *)(skb->data + ihl);

  /* Is this the final fragment? */
  if ((flags & IP_MF) == 0)
    qp->len = end;

  /*
    Find out which fragments are in front and at the back of us in the
    chain of fragments so far.  We must know where to put this
    fragment, right?
  */
  prev = NULL;
  for (next = qp->fragments; next != NULL; next = next->next)
  {
    if (next->offset >= offset)
      break; /* bingo! */
    prev = next;
  }
  /*
    We found where to put this one.  Check for overlap with preceding
    fragment, and, if needed, align things so that any overlaps are
    eliminated.
  */
  if (prev != NULL && offset < prev->end)
  {
    gw_pp_params.syslog(PP_WARN_IP, PP_WARN_IP_OVERLAP, iph, 0, 0);
    i = prev->end - offset;
    offset += i; /* ptr into datagram */
    ptr += i;    /* ptr into fragment data */
  }
  /*
    Look for overlap with succeeding segments.
    If we can merge fragments, do it.
  */
  for (tmp = next; tmp != NULL; tmp = tfp)
  {
    tfp = tmp->next;
    if (tmp->offset >= end)
      break; /* no overlaps at all */
    gw_pp_params.syslog(PP_WARN_IP, PP_WARN_IP_OVERLAP, iph, 0, 0);

    i = end - next->offset; /* overlap is 'i' bytes */
    tmp->len -= i;          /* so reduce size of    */
    tmp->offset += i;       /* next fragment        */
    tmp->ptr += i;
    /*
      If we get a frag size of <= 0, remove it and the packet that it
      goes with. We never throw the new frag away, so the frag being
      dumped has always been charged for.
    */
    if (tmp->len <= 0)
    {
      if (tmp->prev != NULL)
        tmp->prev->next = tmp->next;
      else
        qp->fragments = tmp->next;

      if (tmp->next != NULL)
        tmp->next->prev = tmp->prev;

      next = tfp; /* We have killed the original next frame */

      frag_kfree_skb(pwp, tmp->skb, FREE_READ);
      frag_kfree_s(pwp, tmp, sizeof(struct ipfrag));
    }
  }
  /* Insert this fragment in the chain of fragments. */
  tfp = NULL;
  tfp = ip_frag_create(pwp, offset, end, skb, ptr);

  /*
    No memory to save the fragment - so throw the lot. If we failed
    the frag_create we haven't charged the queue.
  */
  if (!tfp)
  {
    gw_pp_params.no_mem("ip_defrag");
    kfree_skb(skb, FREE_READ);
      __sync_fetch_and_add(&(reassemble_lost.no_memory),1);
    return NULL;
  }
  /* From now on our buffer is charged to the queues. */
  tfp->prev = prev;
  tfp->next = next;
  if (prev != NULL)
    prev->next = tfp;
  else
    qp->fragments = tfp;

  if (next != NULL)
    next->prev = tfp;

  /*
    OK, so we inserted this new fragment into the chain.  Check if we
    now have a full IP datagram which we can bump up to the IP
    layer...
  */
  if (ip_done(qp))
  {
    skb2 = ip_glue(pwp, qp); /* glue together the fragments */
    return (skb2);
  }
  return (NULL);
}

/* Process an incoming IPv6 datagram fragment. */
static char *ip6_defrag(void *par, struct ip6_hdr *ip6h, struct sk_buff *skb)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct ipq6 *pq6 = NULL;
  int offset = 0;
  int frag_len = 0;
  int flags = 0;
  int end = 0;
  unsigned char *ptr = NULL;
  struct ipfrag *prev = NULL;
  struct ipfrag *next = NULL;
  struct ipfrag *tmp = NULL;
  struct ipfrag *tfp = NULL;
  char *skb2 = NULL;

  if (!hostfrag_ip6_find(pwp, ip6h) && skb)
  {
    hostfrag_ip6_create(pwp, ip6h);
  }

  /* Start by cleaning up the memory. */
  if (pwp->this_host)
  {
    if (pwp->this_host->ip_frag_mem > IPFRAG_HIGH_THRESH)
    {
      printf ("evitor\n");
      ip6_evictor(pwp);
    }
  }

  /* Find the entry of this IP datagram in the "incomplete datagrams" queue. */
  if (pwp->this_host)
  {
    pq6 = ip6_find(pwp, ip6h);
  }

  offset = ntohs(pwp->st_ip6_defrag_info.u16_defrag_offset); /* 偏移量 */
  frag_len = pwp->st_ip6_defrag_info.u_defrag_len;  /* 分片数据长度 */
  flags = offset & ntohs(IP6F_MORE_FRAG);
  offset = offset & ntohs(IP6F_OFF_MASK);

  if (pq6 != NULL)
  {
    if (offset == 0)
    {
      /* 更新头部信息 */
      pq6->ihlen = pwp->i_ipv6_header_len - 8;
      memcpy(pq6->ip6h, ip6h, pq6->ihlen);
    }
    del_timer(pwp, &pq6->timer);
    pq6->timer.expires = jiffies(pwp) + IP_FRAG_TIME;
    pq6->timer.data = (unsigned long)pq6;
    pq6->timer.function = ip6_expire;
    add_timer(pwp, &pq6->timer);
  }
  else
  {
    pq6 = ip6_create(pwp, ip6h);
    if (pq6 == NULL)
    {
      kfree_skb(skb, FREE_READ);
      __sync_fetch_and_add(&(reassemble_lost.no_memory),1);
      return NULL;
    }
  }

  if (offset + ntohs(ip6h->ip6_plen) > 65535)
  {
    // gw_pp_params.syslog(PP_WARN_IP6, PP_WARN_IP6_OVERSIZED, ip6h, 1); // TODO review
    gw_pp_params.syslog(PP_WARN_IP6, PP_WARN_IP6_OVERSIZED, ip6h, 0, 1);
    kfree_skb(skb, FREE_READ);
    __sync_fetch_and_add(&(reassemble_lost.failed),1);
    return NULL;
  }

  end = offset + frag_len;
  ptr = (unsigned char*)(skb->data + pwp->i_ipv6_header_len);

  /* 最后一个分片 */
  if (flags == 0)
  {
    pq6->len = end;
  }

  prev = NULL;
  for (next = pq6->fragments; next != NULL; next = next->next)
  {
    if (next->offset >= offset)
    {
      break; /* bingo! */
    }
    prev = next;
  }

  if (prev != NULL && prev->end > offset)
  {
    offset += (prev->end - offset);
    ptr += (prev->end - offset);
  }

  for (tmp = next; tmp != NULL; tmp = tfp)
  {
    tfp = tmp->next;
    if (tmp->offset >= end)
    {
      break;
    }

    tmp->len -= (end - tmp->offset);
    tmp->ptr += (end - tmp->offset);
    tmp->offset += (end - tmp->offset);

    if (tmp->len <= 0)
    {
      if (tmp->prev != NULL)
      {
        tmp->prev->next = tmp->next;
      }
      else
      {
        pq6->fragments = tmp;
      }

      if (tmp->next != NULL)
      {
        tmp->next->prev = tmp->prev;
      }

      next = tfp;
      frag_kfree_skb(pwp, tmp->skb, FREE_READ);
      frag_kfree_s(pwp, tmp, sizeof(struct ipfrag));
    }
  }

  tfp = ip_frag_create(pwp, offset, end, skb, ptr);
  if (!tfp)
  {
    gw_pp_params.no_mem("ip_defrag");
    kfree_skb(skb, FREE_READ);
    __sync_fetch_and_add(&(reassemble_lost.no_memory),1);
    return NULL;
  }

  tfp->prev = prev;
  tfp->next = next;
  if (tfp->prev != NULL)
  {
    tfp->prev->next = tfp;
  }
  else
  {
    pq6->fragments = tfp;
  }
  if (tfp->next != NULL)
  {
    tfp->next->prev = tfp;
  }

  /*
    OK, so we inserted this new fragment into the chain.  Check if we
    now have a full IP datagram which we can bump up to the IP
    layer...
  */
  if (ip6_done(pq6))
  {
    skb2 = ip6_glue(pwp, pq6); /* glue together the fragments */
    return (skb2);
  }

  return NULL;
}

int ip_defrag_stub(void *par, struct ip *iph, struct ip **defrag)
{
  worker_params_t *pwp = (worker_params_t *)par;
  int offset, flags, tot_len;
  struct sk_buff *skb;

  pwp->numpack++;
  pwp->timenow = 0;
  while (pwp->timer_head && pwp->timer_head->expires < jiffies(pwp))
  {
    pwp->this_host = ((struct ipq *)(pwp->timer_head->data))->hf;
    pwp->timer_head->function(par, pwp->timer_head->data); // call ip_expire()
  }
  offset = ntohs(iph->ip_off);
  flags = offset & ~IP_OFFSET;
  offset &= IP_OFFSET;
  if (((flags & IP_MF) == 0) && (offset == 0))
  {
    ip_defrag(pwp, iph, 0);
    return IPF_NOTF;
  }
  tot_len = ntohs(iph->ip_len);
  skb = (struct sk_buff *)malloc(tot_len + sizeof(struct sk_buff));
  if (!skb){
    gw_pp_params.no_mem("ip_defrag_stub");
    __sync_fetch_and_add(&(reassemble_lost.no_memory),1);
    return IPF_ISF;
  }
  skb->data = (char *)(skb + 1);
  memcpy(skb->data, iph, tot_len);
  skb->truesize = tot_len + 16 + gw_pp_params.dev_addon;
  skb->truesize = (skb->truesize + 15) & ~15;
  skb->truesize += gw_pp_params.sk_buff_size;

  if ((*defrag = (struct ip *)ip_defrag(pwp, (struct ip *)(skb->data), skb)))
    return IPF_NEW;

  return IPF_ISF;
}

int ip6_defrag_stub(void *par, struct ip6_hdr *ip6h, struct ip6_hdr **defrag)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct sk_buff *skb = NULL;
  int tot_len = 0;
  pwp->numpack++;
  //printf ("numpcak = %d\n", pwp->numpack);
  pwp->timenow = 0;
  while (pwp->timer_head && pwp->timer_head->expires < jiffies(pwp)) /* 超时 */
  {
    pwp->this_host = ((struct ipq *)(pwp->timer_head->data))->hf;
    pwp->timer_head->function(par, pwp->timer_head->data);
  }

  if (pwp->st_ip6_defrag_info.i8_defrag_flag == 0)
  {
    //printf ("not defrag\n");
    return IPF_NOTF;
  }
  else
  {
    //printf ("defrag payload len = %d, next header = %hhu\n", ntohs(ip6h->ip6_plen), ip6h->ip6_nxt);
  }

  tot_len = ntohs(ip6h->ip6_plen) + sizeof (struct ip6_hdr);
  skb = (struct sk_buff *)malloc(tot_len + sizeof(struct sk_buff));
  if (!skb)
  {
    gw_pp_params.no_mem("ip6_defrag_stub");
    __sync_fetch_and_add(&(reassemble_lost.no_memory),1);
    return IPF_ISF;
  }
  skb->data = (char*)(skb + 1);
  memcpy(skb->data, ip6h, tot_len);
  skb->truesize = tot_len + 16 + gw_pp_params.dev_addon;
  skb->truesize = (skb->truesize + 15) & ~15;
  skb->truesize += gw_pp_params.sk_buff_size;

  if ((*defrag = (struct ip6_hdr *)ip6_defrag(pwp, (struct ip6_hdr *)(skb->data), skb)))
  {
    return IPF_NEW;
  }

  return IPF_ISF;
}

void ip_frag_init(void *par, int n)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct timeval tv;

  memset((void*)&reassemble_lost,0,sizeof(reassemble_lost));
  gettimeofday(&tv, 0);
  pwp->time0 = tv.tv_sec;
  pwp->fragtable = (struct hostfrags **)calloc(n, sizeof(struct hostfrags *));
  if (!pwp->fragtable)
    gw_pp_params.no_mem("ip_frag_init");
  pwp->hash_size = n;
}

void ip_frag_exit(void *par)
{
  worker_params_t *pwp = (worker_params_t *)par;
  if (pwp->fragtable)
  {
    free(pwp->fragtable);
    pwp->fragtable = NULL;
  }
  /* FIXME: do we need to free anything else? */
}
