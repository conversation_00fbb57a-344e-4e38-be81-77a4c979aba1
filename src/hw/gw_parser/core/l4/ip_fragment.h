/*
  
*/

#ifndef _PROTO_PARSER_IP_FRAGMENT_H
#define _PROTO_PARSER_IP_FRAGMENT_H

#define IPF_NOTF 1
#define IPF_NEW 2
#define IPF_ISF 3


#define IP_PAESER_REASSEMBLE_TIMEOUT "timeout"
#define IP_PAESER_REASSEMBLE_OVER_CACHE "overfull"
#define IP_PAESER_REASSEMBLE_NO_MEMORY "no momery"
#define IP_PAESER_REASSEMBLE_FAILED "failed"
typedef struct {
    uint64_t timeout;
    uint64_t over_cache;
    uint64_t no_memory;
    uint64_t failed;
}ip_reassemble_lost_packets;

extern volatile ip_reassemble_lost_packets reassemble_lost;


void ip_frag_init(void *, int);
void ip_frag_exit(void *);
int ip_defrag_stub(void *, struct ip *, struct ip **);
int ip6_defrag_stub(void *, struct ip6_hdr *, struct ip6_hdr **);

#endif /* _PROTO_PARSER_IP_FRAGMENT_H */
