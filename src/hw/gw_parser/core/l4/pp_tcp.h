/*
*/

#ifndef _PROTO_PARSER_TCP_H
#define _PROTO_PARSER_TCP_H

#include <sys/time.h>

#ifdef __cplusplus
extern "C" {
#endif

struct skbuff
{
  struct skbuff *next;
  struct skbuff *prev;

  void *data;
  u_int len;
  u_int size;//接收到数据真正大小
//  u_int urg_ptr;

  char fin;
//  char urg;
  u_int seq;
  u_int ack;
};

typedef struct
{
  uint64_t total;
  uint64_t port_hit;
  uint64_t invalid;
  uint64_t timeout;
  uint64_t cache_overfull;
  uint64_t session_overfull;
  uint64_t no_memory;
  uint64_t ts_check;
  uint64_t not_regist;
  uint64_t reset;
  uint64_t session_close;
  uint64_t duplicate;
  uint64_t close_http;
  uint64_t close_http_http;
}tcp_parser_lost_packets;

typedef struct
{
  uint64_t total;
  uint64_t only_client;
  uint64_t only_server;
  uint64_t normal;
}tcp_single_stream_info;

#define TCP_PARSER_TOTAL "tcp parser"
#define TCP_PARSER_PORT "port hit"
#define TCP_PARSER_INVALID "invalid"
#define TCP_PARSER_TIMEOUT "timeout"
#define TCP_PARSER_CACHE_OVERFUL "cache overfull"
#define TCP_PARSER_SESSION_OVERFUL "session overfull"
#define TCP_PARSER_NO_MEMORY "no memory"
#define TCP_PARSER_TS_CHECK "ts check"
#define TCP_PARSER_NOT_REGIST "not regist"
#define TCP_PARSER_RESET "reset"
#define TCP_PARSER_SESSION_CLOSE "close"
#define TCP_PARSER_DUPLICATE "duplicate"
#define TCP_MEM_INFF_BUFF_TOTAL "tcp buff total"
#define TCP_MEM_INFF_BUFF_USE "tcp buff use"
#define TCP_MEM_INFO_OUT_OF_ORDER "tcp out of order"
extern volatile tcp_parser_lost_packets tcp_lost;
extern uint8_t tcp_lost_count;
extern tcp_parser_lost_packets *tcp_lost_array[64];

extern volatile tcp_single_stream_info tcp_single_stream;


typedef struct
{
  uint64_t stream_total;
  uint64_t lost;
}tcp_stream_lost_bytes;

#define TCP_STREAM_DATA_TOTAL "tcp stream"
#define TCP_STREAM_LOST "lost"
extern volatile tcp_stream_lost_bytes stream_lost;

int tcp_init(void *, int);
void tcp_exit(void *);
void process_tcp(void *, void *, int);
void tcp_check_timeouts(void *, struct timeval *);

#ifdef __cplusplus
}
#endif

#endif /* _PROTO_PARSER_TCP_H */
