/*

*/

#include <sys/types.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "pp.h"
//#include "pp_tcp.h"
#include "util.h"

void gw_pp_no_mem(const char *func)
{
  fprintf(stderr, "Out of memory in %s.\n", func);
  exit(1);
}

char *
test_malloc(int x)
{
  char *ret = (char*)malloc(x);

  if (!ret)
    gw_pp_params.no_mem("test_malloc");

  return ret;
}

void register_callback(struct proc_node **procs, void(*x))
{
  struct proc_node *ipp;

  for (ipp = *procs; ipp; ipp = ipp->next)
    if (x == ipp->item)
      return;
  ipp = mknew(struct proc_node);
  ipp->item = (proc_func)x;
  ipp->next = *procs;
  *procs = ipp;
}

void unregister_callback(struct proc_node **procs, void(*x))
{
  struct proc_node *ipp;
  struct proc_node *ipp_prev = 0;

  for (ipp = *procs; ipp; ipp = ipp->next)
  {
    if (x == ipp->item)
    {
      if (ipp_prev)
        ipp_prev->next = ipp->next;
      else
        *procs = ipp->next;
      free(ipp);
      return;
    }
    ipp_prev = ipp;
  }
}

