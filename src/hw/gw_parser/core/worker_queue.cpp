/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <inttypes.h>
#include <unistd.h>
#include <sys/prctl.h>

#include "worker_queue.h"
#include "display_stats_define.h"
#include "gw_i_upload.h"
#include "utils.h"
#include "watch_dog.h"
#include "gw_stats.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "task_worker.h"
#include "pkt_queue.h"

static void adj_thread_pri_ni_inner(int new_policy, int new_nice)
{
  // 调整线程优先级
  int ret;
  struct sched_param param;
  int prio, policy;
  ret = pthread_getschedparam(pthread_self(), &prio, &param);
  if (ret == -1)
  {
    printf("pthread_getschedparam() error\n");
  }
  policy = new_policy;

  param.sched_priority = sched_get_priority_max(policy);

  pthread_setschedparam(pthread_self(), policy, &param);

  ret = nice(new_nice);
  //printf( "nice():%d\n", ret );
  if (ret == -1 && errno == EACCES)
  {
    printf("nice() setting error\n");
  }
}

static void adj_thread_pri_ni(void)
{
  int new_nice = -20;          // -20 0
  int new_policy = SCHED_FIFO; // SCHED_RR SCHED_OTHER
  adj_thread_pri_ni_inner(new_policy, new_nice);
}

class CTaskWorkerBase : CTaskWorker
{
public:
  static void free_data_callback(void *p)
  {
    TaskWorkerData *ptwd = (TaskWorkerData *)p;
    CTaskWorker *pThis = ptwd->p_tw;
    ASSERT(pThis != NULL);
    // size_t mem_size = ptwd->mem_size;
    // CWorkerQueue *pwq = pThis->get_wq();
    // volatile uint64_t &stats_queue_memory_size = pwq->get_queue_mem_size();

    pThis->free_data(ptwd);
  }
};

/**
 * CWorkerQueue implementation
 *
 * 多线程工作队列及任务队列
 */

CWorkerQueue::CWorkerQueue(void) : m_p_q(NULL)
                                 , m_stats_task_data(NULL)
                                 , m_stats_queue_memory_size(0)
                                 , m_stats_queue_max_memory_size(0)
                                 , m_conf_queue_max_num(0)
                                 , m_conf_queue_memory_max_size_bytes(0)
                                 , m_ptw(NULL)
                                 , m_comm(NULL)
                                 , m_quit_signal(0)
                                 , m_destroy_func(NULL)
                                 , m_watch_dog(NULL)
                                 , m_run_worker_rtn(NULL)
                                 , m_run_arg(NULL)
                                 , m_thread_stop_off(-1)
{
}

CWorkerQueue::~CWorkerQueue(void)
{
  delete m_stats_task_data;
}

void CWorkerQueue::init()
{
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;
  m_stats_queue_max_memory_size = 0;
  m_stats_queue_memory_size = 0;
}

void CWorkerQueue::fini()
{
  ASSERT(m_comm != NULL);
}

void CWorkerQueue::run()
{
  ASSERT(m_comm != NULL);
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CWorkerQueue::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

/**
 * 触发退出信号时处理
 */
void CWorkerQueue::set_quit_signal(void)
{
  m_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CWorkerQueue::wait_for_stop(void)
{
  for (size_t i = 0; i < m_vec_thread.size(); i++)
  {
    pthread_join(m_vec_thread[i], NULL);
  }
  m_vec_thread.clear();
}

/**
 * 设置守护模块对象。
 * @param CWatchDog*
 */
void CWorkerQueue::set_watchdog(CWatchDog *p_wd)
{
  m_watch_dog = p_wd;
}

/**
 * 创建队列。
 */
void CWorkerQueue::create_queue(void)
{
  m_p_q = queue_create(m_conf_queue_max_num, 0);
  m_stats_task_data = new StatsTaskData();
  m_stats_task_data->cb = sizeof(StatsTaskData);
  m_stats_task_data->q.cb = sizeof(stats_queue_t);
}

/**
 * 删除队列
 */
void CWorkerQueue::delete_queue(void)
{
  // GWLOG_TEST(m_comm, "memory size: %" PRIu64 "M; max memory size: %" PRIu64 "M\n",
  //            m_stats_queue_memory_size / (1024ULL * 1024ULL), m_stats_queue_max_memory_size / (1024ULL * 1024ULL));

  if (m_ptw != NULL)
  {
    queue_destroy_complete(m_p_q, &CTaskWorkerBase::free_data_callback);
  }
  else
  {
    queue_destroy_complete(m_p_q, m_destroy_func);
  }

  m_p_q = NULL;
  delete m_stats_task_data;
  m_stats_task_data = NULL;

  // GWLOG_TEST(m_comm, "memory size: %" PRIu64 "M; max memory size: %" PRIu64 "M\n",
  //            m_stats_queue_memory_size / (1024ULL * 1024ULL), m_stats_queue_max_memory_size / (1024ULL * 1024ULL));
  m_stats_queue_max_memory_size = 0;
  m_stats_queue_memory_size = 0;
}

/**
 * 创建工作线程
 * @param int num
 * @param WORKER_ROUTINE worker_rtn
 * @param void *arg
 */
int CWorkerQueue::create_thread(int num, WORKER_ROUTINE worker_rtn, void *arg)
{

  m_run_worker_rtn = worker_rtn;
  m_run_arg = arg;

  return adjust_worker_thread_num(num);
}

/**
 * 调整工作线程数量
 * @param int num
 */
int CWorkerQueue::adjust_worker_thread_num(int num)
{
  int err;
  pthread_t thread;

  if (m_vec_thread.size() > (unsigned)num)
  {
    while (m_vec_thread.size() > (unsigned)num)
    {
      wait_thread(0);
    }
  }

  // GWLOG_INFO(m_comm, "%s thread num %d size %d\n", get_queue_name(), num, m_vec_thread.size());
  int base = m_vec_thread.size();
  for (int k = 0; k < num - base; k++)
  {
    if (0 != (err = pthread_create(&thread, NULL, thread_routine, this)))
    {
      GWLOG_ERROR(m_comm, "%s thread %d create failed %s\n", get_queue_name(), base + k, strerror(err));
    }
    else
    {
      GWLOG_INFO(m_comm, "%s thread %d created successfully\n", get_queue_name(), base + k);
      m_vec_thread.push_back(thread);
    }
  }

  return m_vec_thread.size();
}

/**
 * 等工作线程完成。
 * @param int off
 */
void CWorkerQueue::wait_thread(int off)
{

  for (size_t i = 0; i < 10; i++)
  {
    if (m_thread_stop_off < 0)
    {
      break;
    }
    sleep(1);
  }

  m_thread_stop_off = off;

  for (size_t i = 0; i < 10; i++)
  {
    if (m_thread_stop_off >= 0)
    {
      break;
    }
    sleep(1);
  }
}

void CWorkerQueue::set_queue_destroy_callback(q_destroy_func_t func)
{
m_destroy_func = func;
}

void CWorkerQueue::update_report(void)
{
  if (m_watch_dog == NULL)
  {
    return;
  }

  m_watch_dog->report();
}

bool CWorkerQueue::queue_put_data(void *p, size_t mem_size)
{
  int q_st;
  size_t length;
  queue_root_t *p_q = (queue_root_t *)m_p_q;
  //uint32_t q_num = queue_elements(p_q);
  if (0) // TODO 5G http parser msg queue
  {
    goto fail;
  }

  if (m_ptw != NULL)
  {
    TaskWorkerData *ptwd = (TaskWorkerData *)p;
    ptwd->mem_size = mem_size;
    ptwd->p_tw = m_ptw;
  }

  //STATS_QUEUE_NUM(m_stats_task_data, q_num);

  if (unlikely(m_conf_queue_max_num <= 0))
  {
    goto fail;
  }

  SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->cnt, 1);

  //if (unlikely(q_num > m_conf_queue_max_num))
  //{
    // print log drop msg
    //SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->q.cnt_q_full, 1);
    //goto fail;
  //}

  //if (unlikely(q_num > 10 && m_stats_queue_memory_size >= m_conf_queue_memory_max_size_bytes))
  if (unlikely(m_stats_queue_memory_size >= m_conf_queue_memory_max_size_bytes))
  {
    // 至少要保证队列中有10个任务
    // 队列使用的内存超出限制
    // no mem
    SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->q.cnt_q_no_mem, 1);
    goto fail;
  }

  // 发送到解析队列中
  length = mem_size;

  SYNC_FETCH_AND_ADD_UINT64(&m_stats_queue_memory_size, length);
  m_stats_queue_max_memory_size = MAX(m_stats_queue_max_memory_size, m_stats_queue_memory_size);
  q_st = queue_put(p_q, p);
  if (likely(q_st == QUEUE_OK))
  {
    SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->q.cnt_q_succ, 1);
    return true;
  }
  else
  {
    SYNC_FETCH_AND_SUB_UINT64(&m_stats_queue_memory_size, (uint64_t)length);
    switch (q_st)
    {
    case QUEUE_FULL:
      SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->q.cnt_q_full, 1);
      STATS_QUEUE_NUM(m_stats_task_data, m_conf_queue_max_num);
      goto fail;
    default:
      SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->q.cnt_q_fail, 1);
      goto fail;
    }
  }

fail:
  return false;
}

void CWorkerQueue::set_queue_num_and_bytes(int num, uint64_t bytes)
{
  m_conf_queue_max_num = (unsigned)num;
  m_conf_queue_memory_max_size_bytes = bytes;
}

void CWorkerQueue::set_queue_name(const char *name)
{
  // GWLOG_TEST(m_comm, "name=%s\n", name);
  m_queue_name = name;
}

const char *CWorkerQueue::get_queue_name() const
{
  return m_queue_name.c_str();
}

volatile uint64_t &CWorkerQueue::get_queue_mem_size(void)
{
  return m_stats_queue_memory_size;
}

volatile uint64_t &CWorkerQueue::get_queue_max_mem_size(void)
{
  return m_stats_queue_max_memory_size;
}

void *CWorkerQueue::thread_routine(void *args_ptr)
{
  CWorkerQueue *pThis = (CWorkerQueue *)args_ptr;
  ASSERT(pThis != NULL);

  // set thread_name
  prctl(PR_SET_NAME, pThis->get_queue_name());
  // if (0 == strcasecmp(pThis->get_queue_name(), "http msg queue"))
  // {
  //   sleep(6);
  // }

  pThis->thread_routine_inner();
  return NULL;
}

void CWorkerQueue::thread_routine_inner()
{
  if (m_watch_dog != NULL)
  {
    m_watch_dog->begin();
  }

  // update_report();
  if (m_run_arg != NULL)
  {
    if (0 == strcasecmp(get_queue_name(), UPLOAD_MSG))
    {
        CUpload *pThis = (CUpload *)m_run_arg;
        std::string str_name = pThis->get_name();
        
        if (str_name.find("CKafkaUpload") != std::string::npos)
        {
          adj_thread_pri_ni();
        }
    }
    
  }

  if (m_ptw == NULL && m_run_worker_rtn == NULL)
  {
    while (!m_quit_signal)
    {
      update_report();
      sleep(1);
    }
  }
  else
  {
    void *p = NULL;
    int q_st = 0;
    queue_root_t *p_q = m_p_q;
    worker_routine_param_t wrp[1] = {};

    if (m_ptw != NULL)
    {
      m_ptw->init();
    }
    else
    {
      wrp->step = WQ_WR_BEGIN;
      (*m_run_worker_rtn)(m_run_arg, wrp, NULL);
    }

    wrp->step = WQ_WR_DATA;
    while (!m_quit_signal)
    {
      update_report();

      if (m_thread_stop_off >= 0)
      {
        if ((unsigned)m_thread_stop_off >= m_vec_thread.size())
        {
          m_thread_stop_off = -1;
        }
        else if (pthread_equal(m_vec_thread[m_thread_stop_off], pthread_self()))
        {
          int off = m_thread_stop_off;
          m_thread_stop_off = -1;
          usleep(100 * 1000);
          m_vec_thread.erase(m_vec_thread.begin() + off);
          break;
        }
      }

      p = NULL;
      if (likely(QUEUE_OK == (q_st = queue_get(p_q, &p)) && p != NULL))
      {
        size_t length = ((TaskWorkerData *)p)->mem_size;
         
        // HTTP解析数据
        //uint32_t q_num = queue_elements(p_q);
        //if (q_num == UINT_MAX)
        //{
          //GWLOG_ERROR(m_comm, "thread get num failed(p_q = %p)\n", p_q);
        //}
        //STATS_QUEUE_NUM(m_stats_task_data, q_num);
        int res;
        if (m_ptw != NULL)
        {
          res = m_ptw->deal_data((const TaskWorkerData *)p);
        }
        else
        {
          res = (*m_run_worker_rtn)(m_run_arg, wrp, p);
        }

        if (likely(0 == res))
        {
          SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->cnt_succ, 1);
        }
        else if (likely(res < 0))
        {
          SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->cnt_fail, 1);
        }

        
        if (m_ptw != NULL)
        {
          m_ptw->free_data((TaskWorkerData *)p);
          SYNC_FETCH_AND_SUB_UINT64(&m_stats_queue_memory_size, length);
        }
        else
        {
          (*m_destroy_func)(p);
        }
        
        
      }
      else
      {
        usleep(1000);
      }
    }

    if (m_ptw != NULL)
    {
      m_ptw->fini();
    }
    else
    {
      wrp->step = WQ_WR_END;
      (*m_run_worker_rtn)(m_run_arg, wrp, NULL);
    }
  }

  if (m_watch_dog != NULL)
  {
    m_watch_dog->end();
  }

  return;
}

void CWorkerQueue::status_count(int type, int num)
{
  switch (type)
  {
  case WQ_STATUS_SUCC:
    SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->cnt_succ, num);
    break;
  case WQ_STATUS_FAIL:
    SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->cnt_fail, num);
    break;
  case WQ_STATUS_FAIL2:
    SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->cnt_fail2, num);
    break;
  case WQ_STATUS_FAIL3:
    SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->cnt_fail3, num);
    break;
  case WQ_STATUS_FAIL4:
    SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->cnt_fail4, num);
    break;
  case WQ_QUEUE_FAIL:
    SYNC_FETCH_AND_ADD_UINT64(&m_stats_task_data->q.cnt_q_fail, num);
    break;
  default:
    break;
  }
}

StatsTaskData *CWorkerQueue::get_stats_task_data(void) const
{
  return m_stats_task_data;
}

void CWorkerQueue::set_task_worker(CTaskWorker *ptw)
{
  m_ptw = ptw;
}

void CWorkerQueue::set_stats_task_data(void) 
{
  queue_root_t *p_q = m_p_q;
  uint32_t q_num = queue_elements(p_q);
  STATS_QUEUE_NUM(m_stats_task_data, q_num);
}

void CWorkerQueue::flush_queue(void) 
{
  if (m_ptw != NULL)
  {
    queue_flush_complete(m_p_q, &CTaskWorkerBase::free_data_callback);
  }
  else
  {
    queue_flush_complete(m_p_q, m_destroy_func);
  }
}

uint32_t CWorkerQueue::queue_elements_num(void)
{
  return queue_elements(m_p_q);
}
