/*
*/

#if defined(_CC_CLANG_PP) || defined(_CC_GNU_PP) 
// for linux clang++ g++
#undef _GNU_SOURCE
#undef _SVID_SOURCE
#undef _POSIX_C_SOURCE
#undef _XOPEN_SOURCE
#undef _POSIX_SOURCE
#include <netinet/tcp.h>
#endif

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <syslog.h>

#include "pp.h"
#include "pp_tcp.h"
#include "util.h"
#include "ip_fragment.h"

#include "pp_checksum.h"
#include "pp_hash.h"
#include "utils.h"

int ip_options_compile(unsigned char *iph);

const char *pp_warnings[] = {
    "Murphy - you never should see this message !",
    "Oversized IP packet",
    "Invalid IP fragment list: fragment over size",
    "Overlapping IP fragments",
    "Invalid IP header",
    "Source routed IP frame",
    "Oversized IPV6 packet",
    "Invaild IPv6 header",
    "Overlapping IPV6 fragments",
    "Max number of TCP streams reached",
    "Invalid TCP header",
    "Too much data in TCP receive queue",
    "Invalid TCP flags"};

static struct proc_node *ip_frag_procs;
static struct proc_node *ip_procs;

struct proc_node *tcp_procs;

//static int pp_ip_filter(struct ip *x, int len);
//static void pp_syslog(int type, int errnum, struct ip *iph, void *data);
static void pp_syslog(int type, int errnum,  void *iph, void *data, int ip_type);

gw_pp_prm_t gw_pp_params = {
    168,                // sk_buff_size;
    16,                 // dev_addon; for DLT_EN10MB
    gw_pp_no_mem,       // no_mem
    /* pp_ip_filter, */ // ip_filter
    pp_syslog,          // syslog
    LOG_INFO,          // syslog_level
    10240,              // n_tcp_streams
    0,                  // one_loop_less
    0,                  // tcp_flow_timeout
    256,                // n_hosts
    0,                  // tcp_workarounds;
};

volatile ip_parser_lost_packets ip_lost;
uint8_t ip_lost_count;
pthread_mutex_t ip_lost_mutex = PTHREAD_MUTEX_INITIALIZER;
__thread ip_parser_lost_packets *ip_lost_per_thread = NULL;
ip_parser_lost_packets *ip_lost_array[64];

void ip_lost_register(ip_parser_lost_packets *ip_lost)
{
  pthread_mutex_lock(&ip_lost_mutex);
  ip_lost_array[ip_lost_count] = ip_lost;
  ip_lost_count++;
  pthread_mutex_unlock(&ip_lost_mutex);
}

static void pp_syslog(int type, int errnum, void *ih, void *data, int ip_type)
{
  char saddr[64], daddr[64];
  // char buf[1024];
  // struct host *this_host;
  // unsigned char flagsand = 255; //, flagsor = 0;
  // int i;
  struct ip* iph = NULL;
  struct ip6_hdr *ip6h = NULL;
  
  switch (type)
  {

  case PP_WARN_IP:
    iph = (struct ip*)ih;
    if (errnum != PP_WARN_IP_HDR)
    {
      strcpy(saddr, int_ntoa(iph->ip_src.s_addr));
      strcpy(daddr, int_ntoa(iph->ip_dst.s_addr));
      syslog(gw_pp_params.syslog_level,
             "%s, packet (apparently) from %s to %s\n",
             pp_warnings[errnum], saddr, daddr);
    }
    else
      syslog(gw_pp_params.syslog_level, "%s\n",
             pp_warnings[errnum]);
    break;

  case PP_WARN_IP6: 
      syslog(gw_pp_params.syslog_level, "%s\n", pp_warnings[errnum]);
      break;

  case PP_WARN_TCP:
    if (ip_type == 0)
    {
      iph = (struct ip*)ih;
      strcpy(saddr, int_ntoa(iph->ip_src.s_addr));
      strcpy(daddr, int_ntoa(iph->ip_dst.s_addr));
      if (errnum != PP_WARN_TCP_HDR)
        syslog(gw_pp_params.syslog_level,
             "%s,from %s:%hu to  %s:%hu\n", pp_warnings[errnum],
             saddr, ntohs(((struct tcphdr *)data)->th_sport), daddr,
             ntohs(((struct tcphdr *)data)->th_dport));
      else
        syslog(gw_pp_params.syslog_level, "%s,from %s to %s\n",
               pp_warnings[errnum], saddr, daddr);
      break;
    }
    else
    {
      ip6h = (struct ip6_hdr*)ih;
      sprintf(saddr, "%4x:%4x:%4x:%4x:%4x:%4x:%4x:%4x", ip6h->ip6_src.s6_addr16[0]
                                                      , ip6h->ip6_src.s6_addr16[1]
                                                      , ip6h->ip6_src.s6_addr16[2]
                                                      , ip6h->ip6_src.s6_addr16[3]
                                                      , ip6h->ip6_src.s6_addr16[4]
                                                      , ip6h->ip6_src.s6_addr16[5]
                                                      , ip6h->ip6_src.s6_addr16[6]
                                                      , ip6h->ip6_src.s6_addr16[7]);
      sprintf(daddr, "%4x:%4x:%4x:%4x:%4x:%4x:%4x:%4x", ip6h->ip6_dst.s6_addr16[0]
                                                      , ip6h->ip6_dst.s6_addr16[1]
                                                      , ip6h->ip6_dst.s6_addr16[2]
                                                      , ip6h->ip6_dst.s6_addr16[3]
                                                      , ip6h->ip6_dst.s6_addr16[4]
                                                      , ip6h->ip6_dst.s6_addr16[5]
                                                      , ip6h->ip6_dst.s6_addr16[6]
                                                      , ip6h->ip6_dst.s6_addr16[7]);

      if (errnum != PP_WARN_TCP_HDR)
        syslog(gw_pp_params.syslog_level,
             "%s,from %s:%hu to  %s:%hu\n", pp_warnings[errnum],
             saddr, ntohs(((struct tcphdr *)data)->th_sport), daddr,
             ntohs(((struct tcphdr *)data)->th_dport));
      else
        syslog(gw_pp_params.syslog_level, "%s,from %s to %s\n",
               pp_warnings[errnum], saddr, daddr);
      break;                                                                                       
    }
  default:
    syslog(gw_pp_params.syslog_level, "Unknown warning number ?\n");
  }
}

/* called either directly from pcap_hand() or from cap_queue_process_thread()
 * depending on the value of gw_pp_params.multiproc - mcree
 */
static void call_ip_frag_procs(void *par, void *data, bpf_u_int32 caplen)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct proc_node *i;
  for (i = ip_frag_procs; i; i = i->next)
    (i->item)(pwp, data, caplen);
}

/* wireless frame types, mostly from tcpdump (wam) */
#define FC_TYPE(fc) (((fc) >> 2) & 0x3)
#define FC_SUBTYPE(fc) (((fc) >> 4) & 0xF)
#define DATA_FRAME_IS_QOS(x) ((x)&0x08)
#define FC_WEP(fc) ((fc)&0x4000)
#define FC_TO_DS(fc) ((fc)&0x0100)
#define FC_FROM_DS(fc) ((fc)&0x0200)
#define T_MGMT 0x0 /* management */
#define T_CTRL 0x1 /* control */
#define T_DATA 0x2 /* data */
#define T_RESV 0x3 /* reserved */
#define EXTRACT_LE_16BITS(p)                                \
  ((unsigned short)*((const unsigned char *)(p) + 1) << 8 | \
   (unsigned short)*((const unsigned char *)(p) + 0))
#define EXTRACT_16BITS(p) ((unsigned short)ntohs(*(const unsigned short *)(p)))
#define LLC_FRAME_SIZE 8
#define LLC_OFFSET_TO_TYPE_FIELD 6
#define ETHERTYPE_IP 0x0800


/* Protocol numbers in PPP */
#define PPP_IP		0x0021	/* Raw IP */
#define PPP_IPV6	0x0057	/* Internet Protocol version 6 */


/*
    0                   1                   2                   3
    0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |C|R|K|S|s|Recur|A| Flags | Ver |         Protocol Type         |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |    Key (HW) Payload Length    |       Key (LW) Call ID        |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |                  Sequence Number (Optional)                   |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
   |               Acknowledgment Number (Optional)                |
   +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
*/

typedef struct st_gre_fixed_len_header_for_ppp{
  // first byte
  unsigned char recur :3;
  unsigned char s :1;     // 为0
  unsigned char S :1;     // 若置1，则包含 Sequence Number 
  unsigned char K :1;     // 为1
  unsigned char R :1;     // 为0
  unsigned char C :1;     // 为0

  // second byte
  unsigned char ver :3;
  unsigned char flags :4;
  unsigned char A :1;     // 若置1，则包含 Acknowledgment Number
  
  

  struct{
    unsigned char hb;
    unsigned char lb;
  } protocol_type;
  unsigned short payload_length;
  unsigned short call_id;
}st_gre_fixed_len_header_for_ppp;


/*
   0                   1                   2                   3
   0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
  |C|R|K|S|s|Recur|  Flags  | Ver |         Protocol Type         |
  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
  |      Checksum (optional)      |       Offset (optional)       |
  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
  |                         Key (optional)                        |
  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
  |                    Sequence Number (optional)                 |
  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
  |                         Routing (optional)
  +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
*/

typedef struct st_gre_fixed_len_header{
  // first byte
  unsigned char recur :3;
  unsigned char s :1;
  unsigned char S :1;         // 若置1，则包含 Sequence Number
  unsigned char K :1;         // 若置1，则包含 Key
  unsigned char R :1;         // 若置1，则包含 Routing
  unsigned char C :1;         // 若置1，则包含 Checksum，Offset

  // second byte
  unsigned char ver :3;
  unsigned char flags :5;

  struct{
    unsigned char hb;
    unsigned char lb;
  } protocol_type;
}st_gre_fixed_len_header;

static int parser_ppp_over_gre(char *p_data, unsigned u_data_len, unsigned *p_off, int *p_ip_type)
{
  unsigned short protocol_type = 0;

  struct{
    unsigned char hb;
    unsigned char lb;
  } *p_protocol_type = (void*) p_data + *p_off;

  if (p_protocol_type->hb & 0x01)
  {
    protocol_type = p_protocol_type->hb;
    *p_off += 1;
  }
  else if(p_protocol_type->lb & 0x01)
  {
    protocol_type = (p_protocol_type->hb << 8) | p_protocol_type->lb;
    *p_off += 2;
  }else{
    return -1;
  }
  
  if (protocol_type == PPP_IP) // IPv4
  {
    *p_ip_type = 0;
  }
  else if (protocol_type == PPP_IPV6) // IPv6
  {
    *p_ip_type = 1;
  }else
  {
    return -1;
  }
  return 0;
}

static int parser_gre_ver_0(char *p_data, unsigned u_data_len, unsigned *p_u_gre_len, int *p_ip_type)
{
  st_gre_fixed_len_header *p_header = (st_gre_fixed_len_header*)p_data;
  int i_ip_type = 0;
  /* 判断是否是IP协议 */
  switch((p_header->protocol_type.hb << 8) | p_header->protocol_type.lb)
  {
    
    case 0x0800:
      i_ip_type = 0;
      break;
    case 0x86dd:
      i_ip_type = 1;
      break;
    default:
      return -1;
  }

  unsigned u_gre_len = 4 + (p_header->C + p_header->K + p_header->S) * 4;

  if (p_header->R){
    int SRE_len = 0;
    do{
      SRE_len = *(p_data + u_gre_len + 3);
      u_gre_len += SRE_len;
    }while(SRE_len);
    u_gre_len += 4;
  }

  if (u_data_len < u_gre_len)
  {
    return -1;
  }

  *p_u_gre_len = u_gre_len;
  *p_ip_type = i_ip_type;
  return 0;
}

static int parser_gre_ver_1(char *p_data, unsigned u_data_len, unsigned *p_u_gre_len, int *p_ip_type)
{
  st_gre_fixed_len_header_for_ppp *p_header = (st_gre_fixed_len_header_for_ppp*) p_data;

  unsigned u_gre_len = 8 + (p_header->S + p_header->A) * 4; // 固定长度8，可选字段Sequence Number和Acknowledgment Number

  *p_u_gre_len = u_gre_len;

  if (p_header->protocol_type.hb == 0x88 && p_header->protocol_type.lb == 0x0b)
  {
    return parser_ppp_over_gre(p_data, u_data_len, p_u_gre_len, p_ip_type);
  }
  else if ((p_header->protocol_type.hb == 0x08 && p_header->protocol_type.lb == 0x00)
  ||(p_header->protocol_type.hb == 0x86 && p_header->protocol_type.lb == 0xdd))
  {
    return parser_gre_ver_0(p_data, u_data_len, p_u_gre_len, p_ip_type);
  }

  return -1;
}

static int parser_gre(char *p_data, unsigned u_data_len, unsigned *p_u_gre_len, int *p_ip_type)
{
  unsigned u_gre_min_len = 4;     /* gre协议头最小长度为4字节 */
  if (u_data_len < u_gre_min_len)
  {
    return -1;
  }

  st_gre_fixed_len_header *p_header = (st_gre_fixed_len_header*) p_data;
  if (p_header->ver == 0 && p_header->protocol_type.hb != 0x65)
  {
    return parser_gre_ver_0(p_data, u_data_len, p_u_gre_len, p_ip_type);
  }
  else if(p_header->ver == 1)
  { // ppp over gre
    return parser_gre_ver_1(p_data, u_data_len, p_u_gre_len, p_ip_type);
  }
  else if(p_header->protocol_type.hb == 0x65 && p_header->protocol_type.lb == 0x58)
  {
    // 完整以太网帧,如果使用该协议镜像流量则应该是用offset功能,否则请求和响应无法分配到一个会话中
    unsigned u_gre_len = 4 + (p_header->C + p_header->K + p_header->S) * 4;
    *p_u_gre_len = u_gre_len + 14; // 偏移一个二层头
    if (*p_u_gre_len > u_data_len)
    {
        return -1;
    }
    if (*(p_data + u_gre_len + 12) == 0x08 && *(p_data + u_gre_len + 13) == 0x00)
    {
        *p_ip_type = 0;
        return 0;
    }
    else if (*(p_data + u_gre_len + 12) == 0x86 && *(p_data + u_gre_len + 13) == 0xdd)
    {
        *p_ip_type = 1;
        return 0;
    }
    else
    {
        return -1;
    }

  }
  else
  {
    return -1;
  }
}

int get_in6_l4_protocol(char *p_buf, int i_payload_offset, uint8_t *p_u8_type, ip6_defrag_info_t *p_st_defrag_info)
{
  if (p_buf == NULL)
  {
    return -1;
  }

  struct ip6_hdr *p_st_ip6_hdr = (struct ip6_hdr*)p_buf;

  int len = 0;
  uint8_t u8_next_header_type = p_st_ip6_hdr->ip6_nxt;
  len += sizeof(struct ip6_hdr);
  if (i_payload_offset < len)
  {
    return -1;
  }
  int i_defrag_flag = 0;
  struct ip6_hbh *p_st_hbh = NULL;     /* Hop-by-Hop options header */
  struct ip6_rthdr *p_st_rthdt = NULL; /* routing header */
  struct ip6_frag *p_st_frag = NULL;   /* Fragment header */
  struct ip6_dest *p_st_dest = NULL;   /* destination options header */

  while (u8_next_header_type == IPPROTO_HOPOPTS || u8_next_header_type == IPPROTO_ROUTING || u8_next_header_type == IPPROTO_FRAGMENT || u8_next_header_type == IPPROTO_ESP || u8_next_header_type == IPPROTO_AH || u8_next_header_type == IPPROTO_DSTOPTS)
  {
    //printf ("next header type = %d\n", u8_next_header_type);
    switch(u8_next_header_type)
    {
      case IPPROTO_HOPOPTS:
        p_st_hbh = (struct ip6_hbh *)((char *)p_buf + len);
        u8_next_header_type = p_st_hbh->ip6h_nxt;
        len += (p_st_hbh->ip6h_len + 1) << 3;

        if (i_payload_offset < len)
        {
          return -1;
        }
        break;
      case IPPROTO_ROUTING:
        p_st_rthdt = (struct ip6_rthdr *)((char *)p_buf + len);
        u8_next_header_type = p_st_rthdt->ip6r_nxt;
        len += (p_st_rthdt->ip6r_len + 1) << 3;
        if (i_payload_offset < len)
        {
          return -1;
        }
        break;
      case IPPROTO_FRAGMENT:
        
        p_st_frag = (struct ip6_frag *)((char *)p_buf + len);
        u8_next_header_type = p_st_frag->ip6f_nxt;
        //printf ("len = %d, next header = %d\n", len, u8_next_header_type);
        len += 8; /* 固定长度8字节 */

        if (i_payload_offset < len)
        {
          return -1;
        }
        i_defrag_flag = 1;
        //printf ("kdalsdka\n");
        break;
      case IPPROTO_ESP:
        /* TODO */
        return -1;
        break;
      case IPPROTO_AH:
        /* TODO */
        return -1;
        break;
      case IPPROTO_DSTOPTS:
        p_st_dest = (struct ip6_dest*)((char*)p_buf + len);
        u8_next_header_type = p_st_dest->ip6d_nxt;
        len += (p_st_dest->ip6d_len + 1) << 3;

        if (i_payload_offset < len)
        {
          return -1;
        }
        break;
    }
    //printf ("next header = %d\n", u8_next_header_type);
  }

  //printf (" qe  qw\n");
  *p_u8_type = u8_next_header_type;
  if (i_defrag_flag == 1 && p_st_defrag_info != NULL)
  {
    if (ntohs(p_st_frag->ip6f_offlg) & IP6F_MORE_FRAG || ntohs(p_st_frag->ip6f_offlg) & IP6F_OFF_MASK)
    {
      p_st_defrag_info->i8_defrag_flag = 1;
      p_st_defrag_info->u16_defrag_offset = p_st_frag->ip6f_offlg;
      p_st_defrag_info->u_idenfy_id = p_st_frag->ip6f_ident;
      p_st_defrag_info->u_defrag_len = (unsigned)(i_payload_offset - len);
      //p_st_defrag_info->u8_l4_protocol = u8_next_header_type;
    }
  }
  return len;
}

static void calc_linkoffset(unsigned char *data, int *linkoffset)
{
  switch (*(unsigned short *)(data + 2))
  {
    case 0x0008:
      *linkoffset += 4;
      break;
    case 0xdd86:
      *linkoffset += 4;
      break;
    case 0x0081:
      *linkoffset +=4;
      calc_linkoffset(data + 4, linkoffset);
      break;
    default:
      break;
  }
}

/* 返回码 -1 非IP数据 0:ipv4 1: ipv6 */
int parser_data_link_layer(u_char *data, int *p_offset)
{
  if (!p_offset)
  {
    return -1;
  }
  //0800:ipv4 86dd:ipv6 8100:vlan/IEEE 802.1Q
  if (data[12] == 8 && data[13] == 0)
  {
    *p_offset = 14;

    struct ip *iph = (struct ip*)(data + 14);
  
    if (iph->ip_p == IPPROTO_IPIP)
    {
      *p_offset += (iph->ip_hl * 4);
    }

    return 0;
  }
  else if (data[12] == 0x86 && data[13] == 0xdd)
  {
    *p_offset = 14;
    return 1;
  }
  else if (data[12] == 0x81 && data[13] == 0x0)
  {
    *p_offset = 14;
    calc_linkoffset(data + 14, p_offset);
    unsigned short type = *(unsigned short *)(data + *p_offset - 2);
    if (type == 0x0008)
    {
      return 0;
    }
    else if (type == 0xdd86)
    {
      return 1;
    }
    else
    {
      return -1;
    }
  }
  
  return -1;
}

void pp_pcap_handler(u_char *par, struct pcap_pkthdr *hdr, u_char *data)
{
  worker_params_t *pwp = (worker_params_t *)par;
  u_char *data_aligned;
  int is_in6addr_packet = 0;
  int len = 0;
  int i_ret = 0;
  int i_offset = 0;        /* 保存IPv6数据头长度 */
  uint8_t u8_l4_type = 0;  /* 保存IPv6四层协议类型 */

  if (pwp == NULL)
  {
    return;
  }

  if (NULL == ip_lost_per_thread)
  {
    ip_lost_per_thread = (ip_parser_lost_packets*)malloc(sizeof(ip_parser_lost_packets));
    memset(ip_lost_per_thread, 0, sizeof(ip_parser_lost_packets));
    ip_lost_register(ip_lost_per_thread);
  }

  ip_lost_per_thread->total_packets += 1;
  /*
   * Check for savagely closed TCP connections. Might
   * happen only when gw_pp_params.tcp_workarounds is non-zero;
   * otherwise pp_tcp_timeouts is always NULL.
   */
  if (NULL != pwp->pp_tcp_timeouts)
    tcp_check_timeouts(pwp, &hdr->ts);

  pwp->pp_last_pcap_header = hdr;
  pwp->pp_last_pcap_data = data;
  pwp->i_gre_flag = 0;
  pwp->i_ip_type = 0;
  memset(&(pwp->st_ip6_defrag_info), 0, sizeof(ip6_defrag_info_t));

  switch (pwp->linktype)
  {
  case DLT_EN10MB:
    if (hdr->caplen < 14){
      __sync_fetch_and_add(&(ip_lost.invalid_ip_packets),1);
      return;
    }

    i_ret = parser_data_link_layer(data, &i_offset);
    if (i_ret == 0)
    {
      pwp->i_ip_type = 0;
      is_in6addr_packet = 0;
    }
    else if (i_ret == 1)
    {
      pwp->i_ip_type = 1;
      is_in6addr_packet = 1;
    }
    else
    {
      __sync_fetch_and_add(&(ip_lost.invalid_ip_packets),1);
      return;
    }

    pwp->pp_linkoffset = i_offset;
    break;
  default:;
  }
  if (hdr->caplen < pwp->pp_linkoffset)
  {
    __sync_fetch_and_add(&(ip_lost.invalid_ip_packets),1);
    return;
  }
  /*
* sure, memcpy costs. But many EXTRACT_{SHORT, LONG} macros cost, too. 
* Anyway, libpcap tries to ensure proper layer 3 alignment (look for
* handle->offset in pcap sources), so memcpy should not be called.
*/
  // #ifdef LBL_ALIGN
  //     if ((unsigned long) (data + pwp->pp_linkoffset) & 0x3) {
  //   data_aligned = alloca(hdr->caplen - pwp->pp_linkoffset + 4);
  //   data_aligned -= (unsigned long) data_aligned % 4;
  //   memcpy(data_aligned, data + pwp->pp_linkoffset, hdr->caplen - pwp->pp_linkoffset);
  //     } else
  // #endif

  if (i_offset > 14  && 0x81 == data[12] && 0x00 == data[13])
  {
    pwp->vlan_id = ((data[14] & 0xFF) << 8 | (data[15] & 0xFF)) & 0x0FFF;
  }
  else
  {
    pwp->vlan_id = UINT16_MAX;
  }

  data_aligned = data + pwp->pp_linkoffset;
  len = hdr->caplen - pwp->pp_linkoffset;

  if (is_in6addr_packet == 0)
  {
    struct ip *iph = (struct ip*)data_aligned;
  
    if (iph->ip_p == IPPROTO_GRE)
    {
      //printf ("caplen = %d, linkoffset = %d, ip struct len = %d\n", hdr->caplen, pwp->pp_linkoffset, sizeof(struct ip));
      if (hdr->caplen <  pwp->pp_linkoffset + sizeof(struct ip))
      {
        __sync_fetch_and_add(&(ip_lost.invalid_v4_gre_packets),1);
        return;
      }
      unsigned u_gre_len = 0;
      
      if (0 != parser_gre((char*)data_aligned + sizeof(struct ip), hdr->caplen - pwp->pp_linkoffset - sizeof(struct ip), &u_gre_len, &pwp->i_ip_type))
      {
        __sync_fetch_and_add(&(ip_lost.invalid_v4_gre_packets),1);
        return ;
      }


      pwp->i_gre_flag = 1;
      pwp->i_gre_ip_type = 0;
      pwp->gre_saddr = ntohl(iph->ip_src.s_addr);
      pwp->gre_daddr = ntohl(iph->ip_dst.s_addr);

      data_aligned = ((u_char*)data_aligned + sizeof(struct ip) + u_gre_len);
      len -= (sizeof(struct ip) + u_gre_len); 

      if (pwp->i_ip_type == 1)  /* ipv6 */
      {
        i_offset = get_in6_l4_protocol((char*)data_aligned, len, &u8_l4_type, &(pwp->st_ip6_defrag_info));
        if (i_offset < 0)
        {
          __sync_fetch_and_add(&(ip_lost.invalid_v6_in_v4_packets),1);
          return;
        }

        pwp->i_ipv6_header_len = i_offset;
        pwp->u8_l4_protocol = u8_l4_type;
      }
      else  /* ipv4 */
      {
        iph = (struct ip*)data_aligned;
        pwp->u8_l4_protocol = iph->ip_p;
      }
    }
    else  /* 非gre ipv4数据 */
    {
      pwp->u8_l4_protocol = iph->ip_p;
    }
  }
  else
  {
    struct ip6_hdr *p_st_ip6_hdr = (struct ip6_hdr*)data_aligned;
    i_offset = get_in6_l4_protocol((char*)data_aligned, len, &u8_l4_type, &(pwp->st_ip6_defrag_info));
    if (i_offset < 0)
    {
      __sync_fetch_and_add(&(ip_lost.invalid_v6_packets),1);
      return;
    }
    else if (u8_l4_type == IPPROTO_GRE)
    {
      unsigned u_gre_len = 0;
      // int i_actual_ip_type = 0;
      if (0 != parser_gre((char*)data_aligned + i_offset, hdr->caplen - pwp->pp_linkoffset - i_offset, &u_gre_len, &pwp->i_ip_type))
      {
        __sync_fetch_and_add(&(ip_lost.invalid_v6_gre_packets),1);
        return ;
      }

      pwp->i_gre_flag = 1;
      pwp->i_gre_ip_type = 1;
      memcpy(pwp->a_gre_in6_saddr, p_st_ip6_hdr->ip6_src.s6_addr32, sizeof(pwp->a_gre_in6_saddr));
      memcpy(pwp->a_gre_in6_daddr, p_st_ip6_hdr->ip6_dst.s6_addr32, sizeof(pwp->a_gre_in6_daddr));

      data_aligned = ((u_char*)data_aligned + i_offset + u_gre_len);
      len -= (i_offset + u_gre_len);

      if (pwp->i_ip_type == 1)
      {
        i_offset = get_in6_l4_protocol((char*)data_aligned, len, &u8_l4_type, &(pwp->st_ip6_defrag_info));
        if (i_offset < 0)
        {
          __sync_fetch_and_add(&(ip_lost.invalid_v6_in_v6_packets),1);
          return;
        }

        pwp->i_ipv6_header_len = i_offset;
        pwp->u8_l4_protocol = u8_l4_type;
      }
    }
    else if(u8_l4_type == IPPROTO_IPV6)
    {
      data_aligned = ((u_char*)data_aligned + i_offset);
      len -= (i_offset);
      
      i_offset = get_in6_l4_protocol((char*)data_aligned, len, &u8_l4_type, &(pwp->st_ip6_defrag_info));
      if (i_offset < 0)
      {
        __sync_fetch_and_add(&(ip_lost.invalid_v6_in_v6_packets),1);
        return;
      }

      pwp->i_ipv6_header_len = i_offset;
      pwp->u8_l4_protocol = u8_l4_type;
    }
    else
    {
      /* IPv6数据头长度(包括基本头部和扩展头部) */
      pwp->i_ipv6_header_len = i_offset;
      pwp->u8_l4_protocol = u8_l4_type;
    }
  }

  if (pwp->u8_l4_protocol != IPPROTO_TCP)
  {
    __sync_fetch_and_add(&(ip_lost.not_tcp_packets),1);
    return;
  }

  pwp->p_mac = NULL;
  if(pwp->bool_need_mac && (pwp->pp_linkoffset == 14 || pwp->pp_linkoffset == 18)){
    pwp->p_mac = (char*)data;
  }
  
  call_ip_frag_procs(pwp, data_aligned, len);
}

static void fastcall gen_ip_frag_proc(void *par, void *data, int len)
{
  worker_params_t *pwp = (worker_params_t *)par;
  struct proc_node *i;
  //struct ip *iph = (struct ip *)data;
  struct ip* iph = NULL;
  struct ip6_hdr *ip6h = NULL;
  void *buf = NULL;
  int need_free = 0;
  int skblen;
  void (*glibc_syslog_h_workaround)(int, int, void *, void *, int) = gw_pp_params.syslog;

  if (pwp->i_ip_type == 0)
  {
    iph = (struct ip*)data;
    if (len < (int)sizeof(struct ip) || iph->ip_hl < 5 || iph->ip_v != 4 ||
        ip_fast_csum((unsigned char *)iph, iph->ip_hl) != 0 ||
        len < ntohs(iph->ip_len) || ntohs(iph->ip_len) < iph->ip_hl << 2)
    {
      glibc_syslog_h_workaround(PP_WARN_IP, PP_WARN_IP_HDR, iph, 0, pwp->i_ip_type);
      __sync_fetch_and_add(&(ip_lost.invalid_ip_packets),1);
      return;
    }

    if (iph->ip_hl > 5 && ip_options_compile((unsigned char *)data))
    {
      glibc_syslog_h_workaround(PP_WARN_IP, PP_WARN_IP_SRR, iph, 0, pwp->i_ip_type);
      __sync_fetch_and_add(&(ip_lost.invalid_ip_packets),1);
      return;
    }

    switch (ip_defrag_stub(pwp, (struct ip *)data, &iph))
    {
    case IPF_ISF: /* 分片数据包 */
      __sync_fetch_and_add(&(ip_lost.ipv4_defrag_packets), 1);
      return;
    case IPF_NOTF:
      need_free = 0;
      iph = (struct ip *)data;
      break;
    case IPF_NEW:
      need_free = 1;
      break;
    default:;
    }
    skblen = ntohs(iph->ip_len) + 16;
    if (!need_free)
      skblen += gw_pp_params.dev_addon;
    skblen = (skblen + 15) & ~15;
    skblen += gw_pp_params.sk_buff_size;
    buf = iph;
  }
  else if (pwp->i_ip_type == 1)
  {
    ip6h = (struct ip6_hdr*)data;  
    if (len < (int)sizeof(struct ip6_hdr))
    {
      glibc_syslog_h_workaround(PP_WARN_IP6, PP_WARN_IP6_HDR, (void*)ip6h, 0, 1);
      __sync_fetch_and_add(&(ip_lost.invalid_v6_packets),1);
      return;
    }

    switch (ip6_defrag_stub(pwp, (struct ip6_hdr*)data, &ip6h))
    {
      case IPF_ISF: /* ipv6分片数据包 */
        __sync_fetch_and_add(&(ip_lost.ipv6_defrag_packets), 1);
        return;
      case IPF_NOTF:
        need_free = 0;
        ip6h = (struct ip6_hdr*)data;
        break;
      case IPF_NEW:
        need_free = 1;
        break;
      default:;
    }
    skblen = ntohs(ip6h->ip6_plen) + sizeof(struct ip6_hdr) + 16;
    //printf ("ipv6 payload len = %d\n", ntohs(ip6h->ip6_plen));
    //printf ("next header = %hhu\n", ip6h->ip6_nxt);
    if (!need_free)
      skblen += gw_pp_params.dev_addon;
    skblen = (skblen + 15) & ~15;
    skblen += gw_pp_params.sk_buff_size;
    buf = ip6h;
  }
  else
  {
      __sync_fetch_and_add(&(ip_lost.invalid_ip_packets),1);
    return ;
  }

  for (i = ip_procs; i; i = i->next)
  {
    (i->item)(pwp, (void *)buf, skblen);
  }

  if (need_free)
  {
    free(buf);
  }
}

static void fastcall gen_ip_proc(void *par, void *data, int skblen)
{
  worker_params_t *pwp = (worker_params_t *)par;
  uint8_t u8_l4_protocol = 0;
  u8_l4_protocol = pwp->u8_l4_protocol;
  switch(u8_l4_protocol)
  {
  case IPPROTO_TCP:
    process_tcp(pwp, data, skblen);
    break;
  
  default:
    __sync_fetch_and_add(&(ip_lost.not_tcp_packets),1);
    break;
  }
}

static void init_procs()
{
  ip_frag_procs = mknew(struct proc_node);
  ip_frag_procs->item = gen_ip_frag_proc;
  ip_frag_procs->next = NULL;
  ip_procs = mknew(struct proc_node);
  ip_procs->item = gen_ip_proc;
  ip_procs->next = NULL;
  tcp_procs = NULL;
}

int pp_init(worker_params_t *pwp, int size)
{
  int i;

  /* free resources that previous usages might have allocated */
  pp_exit(pwp, size);
  memset((void*)&ip_lost,0,sizeof(ip_lost));


  if (gw_pp_params.syslog == pp_syslog)
  {
    openlog("pp", 0, LOG_LOCAL0);
    setlogmask(LOG_UPTO(LOG_ERR));
  }

  init_hash();
  init_procs();
  for (i = 0; i < size; ++i)
  {
    tcp_init(&pwp[i], gw_pp_params.n_tcp_streams);
    ip_frag_init(&pwp[i], gw_pp_params.n_hosts);
  }

  return 1;
}

void pp_exit(worker_params_t *pwp, int size)
{
  int i;

  for (i = 0; i < size; ++i)
  {
    tcp_exit(&pwp[i]);
    ip_frag_exit(&pwp[i]);
  }

  free(ip_procs);
  ip_procs = NULL;
  free(ip_frag_procs);
  ip_frag_procs = NULL;
}

void swap(struct half_stream *hlf_a, struct half_stream *hlf_b)
{
  struct half_stream tmp;
  if ((hlf_a == NULL) || (hlf_b == NULL)) {
    return ;
  }
  memcpy(&tmp, hlf_a, sizeof(struct half_stream));
  memcpy(hlf_a, hlf_b, sizeof(struct half_stream));
  memcpy(hlf_b, &tmp, sizeof(struct half_stream));
  return ;
}
