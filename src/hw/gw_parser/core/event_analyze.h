#ifndef __GW_PARSER_CORE_EVENT_ANALYZE_H__
#define __GW_PARSER_CORE_EVENT_ANALYZE_H__


#include <inttypes.h>
#include <stdio.h>

#include "utils.h"
#include "utils_core.h"
#include "cap_data.h"
#include "worker_queue.h"

struct AnalyzeMsg{
    uint64_t time;
    uint32_t len;
    bool is_data;             //event or data
    bool is_request;          //request or response
    char content[100];
};

struct thread_local_upload_msg_data;


class CGwCommon;


class CEventAnalyze
{
public:
    CEventAnalyze();
    ~CEventAnalyze();
public:
    /**
     * 初始化事件分析类，包括：初始化参数，注册tcp callback，初始化工作队列。
     */
    virtual void init();

    virtual void fini();

    virtual void run();

    void put_event_msg(bool is_request,const char* content,int content_len);
    void put_upload_msg(UploadMsg *);
    /**
     * 设置全局公共类对象实例。
     * @param CGwCommon *comm
     */
    virtual void set_gw_common(CGwCommon *comm);

    /**
     * 加载配置参数（Json字符串，支持动态）。
     * @param const char *
     */
    virtual bool load_conf(const char *);

    /**
     * 触发退出信号时处理
     */
    virtual void set_quit_signal(void);

    /**
     * 等待运行结束
     */
    virtual void wait_for_stop(void);

    /**
     * TCP callback，
     * 在会话建立时，决定是否监控会话
     * 会话传输数据时，将数据信息加入队列（在已监控会话上触发）
     */
    static void fastcall pp_tcp_callback(void *userdata, struct tcp_stream *a_tcp, void **this_time_not_needed);


    static CEventAnalyze& instance();

    bool check_analyze_log(uint32_t source_ip,uint32_t dest_ip,uint16_t dest_port);
    uint32_t get_analyze_flag(){
        return analyze_flag;
    }
protected:
    /**
     * 检查是否对当前会话进行监控分析。
     * 根据会话的源IP，目的IP，目的端口进行判断
     * 参数：
     *      TCP会话
     * 返回值：
     */
    bool check_analyze_stream(struct tcp_stream *a_tcp);
    void put_msg(AnalyzeMsg *p_msg);
    void free_worker_queue(CWorkerQueue *p);
    bool check_timeout();


    static void free_analyze_msg(AnalyzeMsg *p);
    static int worker_routine_analyse_msg(void *args_ptr, worker_routine_param *pwrp, void *p);
    int worker_routine_analyse_msg_inner(thread_local_upload_msg_data *tlumd, void *p);


    static void free_upload_msg(UploadMsg *p); 
    static int worker_routine_upload_msg(void *args_ptr, worker_routine_param *pwrp, void *p);
    int worker_routine_upload_msg_inner(thread_local_upload_msg_data *tlumd, void *p);

    CGwCommon *m_comm;
    volatile int m_quit_signal;
    CWorkerQueue *m_p_wq_event_msg;
    CWorkerQueue* m_p_wq_upload_msg;


    char m_event_queue_name[32];
    char m_upload_queue_name[32];
    int m_queue_priority;

    int m_conf_event_msg_thread_num;                       // TODO
    int m_conf_event_msg_queue_max_num;                    // TODO
    uint64_t m_conf_event_msg_queue_memory_max_size_bytes; // TODO

    int m_conf_event_queue_buffering_max_messages;
    uint64_t m_conf_event_queue_buffering_max_kbytes;
    int m_conf_event_batch_num_messages;

    uint32_t source_ip;
    uint32_t dest_ip;
    uint16_t dest_port;
    uint32_t timeout;
    uint32_t analyze_flag;
    std::string m_str_stats_path;
    FILE* log_event_file;
    FILE* log_upload_file;

    static CEventAnalyze* m_event_analyze;
};




#endif //__GW_PARSER_CORE_EVENT_ANALYZE_H__