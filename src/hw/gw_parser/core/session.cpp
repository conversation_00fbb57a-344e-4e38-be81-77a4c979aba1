/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <string.h>

#include <set>

#include "session.h"

#include "utils.h"

#include "gw_i_parser.h"
#include "gw_common.h"
#include "gw_logger.h"

#include "tcp_parser.h"
#include "pp.h"

#define SFP_FUNC_GET_DATA 1
#define SFP_FUNC_DISCARD 2
#define SFP_FUNC_DISCARD_AND_UPDATE 3

typedef struct search_func_param
{
  int func; // =1 get_data; =2 discard; =3 discard_and_update;
  union {
    struct
    {
      const char *res;
      int dir;
      int *data_len;
      int *offset_out;
    } get_data;

    struct
    {
      bool res;
      int dir;
      int num;
    } discard;

    struct
    {
      bool res;
      int dir;
      int num;
    } discard_and_update;
  };

} search_func_param_t;

/**
 * CSession implementation
 *
 * 根据从当前会话中根据协议层次获取有效数据。
 */

CSession::CSession() : stat(HASH_DELETE)
                     , m_psm(NULL)
                     , m_con({0})
                     , m_last_update(0)
                     , m_reverse(false)
                     , m_tcp_parser(NULL)
                     , m_tcp_stream_data(NULL)
                     , m_proto_data({{{.parser = NULL}, 0}})
                     , m_comm(NULL)
                     , m_probe_cnt(1)
{
  // m_comm = NULL;
  // m_last_update = 0;
  // stat = 0;
  // memset(&m_proto_data, 0, sizeof(m_proto_data));
  // memset(&m_con, 0, sizeof(m_con));

  // m_psm = NULL;

  // m_tcp_parser = NULL;
  // m_tcp_stream_data = NULL;
}

CSession::~CSession()
{
  del_data();
  /*
  if (m_tcp_parser != NULL && m_tcp_stream_data != NULL)
  {
    m_tcp_parser->delete_stream_data(m_tcp_stream_data->a_tcp);
    delete m_tcp_stream_data;
  }
  */
}

void CSession::search_func(CParser *pp, search_func_param_t *psfp)
{
  const char *p = NULL;

  std::set<CParser *> pa;
  std::vector<CParser *> vp;
  //std::vector<CParser *> vp = m_comm->get_parser_data_dep(pp);
  m_comm->get_parser_data_dep(pp, vp);
  // GWLOG_TEST(m_comm, "vp.size()=%d\n", vp.size());
  for (size_t i = 0; i < vp.size(); i++)
  {
    
    if (vp[i] == NULL)
    {
      // CTcpParser*
      CTcpParser *tcp_parser = m_proto_data[SESSION_PROTO_TYPE_TCP].tcp_parser;
      StreamData *data = m_proto_data[SESSION_PROTO_TYPE_TCP].data;
      if (unlikely(tcp_parser == NULL || data == NULL))
      {
        continue;
      }
      if (unlikely(data == NULL))
      {
        return;
      }

      switch (psfp->func)
      {
      case SFP_FUNC_GET_DATA:
        psfp->get_data.res = tcp_parser->get_data(data->a_tcp, psfp->get_data.dir, psfp->get_data.data_len, psfp->get_data.offset_out);
        break;

      case SFP_FUNC_DISCARD:
        psfp->discard.res = tcp_parser->tcp_discard(data->a_tcp, psfp->discard.dir, psfp->discard.num);
        break;

      case SFP_FUNC_DISCARD_AND_UPDATE:
        psfp->discard_and_update.res = tcp_parser->tcp_discard_and_update(data->a_tcp, psfp->discard_and_update.dir, psfp->discard_and_update.num);
        break;
      }
      return;
    }
    else
    {
      pa.insert(vp[i]);
      // GWLOG_TEST(m_comm, "vp[%d]->get_name()=%s\n", i, vp[i]->get_name());
      int type = m_comm->get_parser_type(vp[i]);
      if (type < 0)
      {
        continue;
      }
      // CParser*
      CParser *parser = m_proto_data[type].parser;
      StreamData *data = m_proto_data[type].data;
      if (unlikely(parser == NULL))
      {
        continue;
      }

      if (unlikely(data == NULL))
      {
        return;
      }

      switch (psfp->func)
      {
      case SFP_FUNC_GET_DATA:
        p = parser->get_data(data, psfp->get_data.dir, psfp->get_data.data_len, psfp->get_data.offset_out);
        if (p != NULL)
        {
          if (*psfp->get_data.data_len == 0)
          {
            psfp->get_data.res = NULL;
            return;
          }
          psfp->get_data.res = p;
          return;
        }
        break;

      case SFP_FUNC_DISCARD:
        if (parser->discard(data, psfp->discard.dir, psfp->discard.num))
        {
          psfp->discard.res = true;
          return;
        }
        break;

      case SFP_FUNC_DISCARD_AND_UPDATE:
        if (parser->discard_and_update(data, psfp->discard_and_update.dir, psfp->discard_and_update.num))
        {
          psfp->discard_and_update.res = true;
          return;
        }
        break;

      }

      if (pa.find(parser) == pa.end())
      {
        // 未处理过
        vp.push_back(parser);
      }
    }
  }
}

/**
 * 获取当前有效数据
 * @param int *datalen
 */
const char *CSession::get_data(CParser *pp, int dir, int *data_len, int *offset_out)
{
  int offset_out_t = 0;
  *data_len = 0;
  if (offset_out != NULL)
  {
    *offset_out = 0;
  }
  else
  {
    offset_out = &offset_out_t;
  }

  search_func_param_t psfp[1] = {0};
  psfp->func = SFP_FUNC_GET_DATA;
  psfp->get_data.dir = dir;
  psfp->get_data.data_len = data_len;
  psfp->get_data.offset_out = offset_out;
  psfp->get_data.res = NULL;
  search_func(pp, psfp);

  return psfp->get_data.res;
}

/**
 * @param int num
 */
bool CSession::discard(CParser *pp, int dir, int num)
{
  search_func_param_t psfp[1] = {0};
  psfp->func = SFP_FUNC_DISCARD;
  psfp->discard.dir = dir;
  psfp->discard.num = num;
  psfp->discard.res = false;
  search_func(pp, psfp);

  return psfp->discard.res;
}

/**
 * @param int num
 */
bool CSession::discard_and_update(CParser *pp, int dir, int num)
{
  search_func_param_t psfp[1] = {0};
  psfp->func = SFP_FUNC_DISCARD_AND_UPDATE;
  psfp->discard_and_update.dir = dir;
  psfp->discard_and_update.num = num;
  psfp->discard_and_update.res = false;
  search_func(pp, psfp);

  return psfp->discard_and_update.res;
}

void CSession::set_header_complete()
{
  StreamData *data = m_proto_data[SESSION_PROTO_TYPE_TCP].data;
  if (unlikely(data == NULL))
  {
    return ;
  }
  data->a_tcp->header_complete = 1;
  return ;
}

void CSession::set_tcp_direction_confirmed()
{
  StreamData *data = m_proto_data[SESSION_PROTO_TYPE_TCP].data;
  if (unlikely(data == NULL))
  {
    return ;
  }
  data->a_tcp->direction_confirmed = 1;
  return ;
}


void CSession::init()
{
  ASSERT(m_comm != NULL);
  m_last_update = m_comm->gw_time();
  stat = HASH_DATA;
  memset(&m_proto_data, 0, sizeof(m_proto_data));
  memset(&m_con, 0, sizeof(m_con));
  m_map_parser_chain.clear();
}

void CSession::fini()
{
  ASSERT(m_comm != NULL);
  m_map_parser_chain.clear();
  del_data();
}

void CSession::del_data()
{
  CSession *p = this;
  if (p->stat != HASH_DATA)
  {
    return;
  }

  for (size_t j = 0; j < COUNTOF(p->m_proto_data); j++)
  {
    if (j == SESSION_PROTO_TYPE_TCP)
    {
      if (m_tcp_stream_data != NULL)
      {
        continue;
      }

      if (p->m_proto_data[j].data != NULL) 
      {
        /* 防止超时删除session，后在访问会出现coredump */
        if (p->m_proto_data[j].data->a_tcp != NULL)
        {
          ((CTcpParser*)p->m_proto_data[j].parser)->send_tcp_session_info(p->m_proto_data[j].data->a_tcp);
          p->m_proto_data[j].data->a_tcp->p_session = NULL;
        }
        delete p->m_proto_data[j].data;
        p->m_proto_data[j].data = NULL;
      }
      continue;
    }
    if (p->m_proto_data[j].parser == NULL)
    {
      continue;
    }
    if (p->m_proto_data[j].data == NULL)
    {
      p->m_proto_data[j].parser = NULL;
      continue;
    }
    p->m_proto_data[j].parser->del_session_stream(p->m_proto_data[j].data);
    p->m_proto_data[j].parser = NULL;
    p->m_proto_data[j].data = NULL;
  }

  //释放会话时，完全清空队列
  if (m_tcp_parser != NULL && m_tcp_stream_data != NULL)
  {
	  m_tcp_parser->delete_stream_data(m_tcp_stream_data->a_tcp);
	  delete m_tcp_stream_data;
    m_tcp_stream_data = NULL;
  }

  p->stat = HASH_DELETE;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CSession::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

void CSession::tcp_drop_data(CParser *pp, int reason)
{
  CTcpParser *tcp_parser = m_proto_data[SESSION_PROTO_TYPE_TCP].tcp_parser;
  StreamData *data = m_proto_data[SESSION_PROTO_TYPE_TCP].data;
  if (unlikely(tcp_parser == NULL || data == NULL))
  {
    GWLOG_DEBUG(m_comm, "tcp_parser=%p data=%p\n", tcp_parser, data);
    return;
  }

  tcp_parser->tcp_drop_data(data->a_tcp, reason);
}

/**
 * 从会话中获取解析对象。
 * @param CParser *parser
 */
StreamData *CSession::get_stream_data_from_parser(CParser *parser)
{
  int type = m_comm->get_parser_type(parser);
  if (type < 0 || m_proto_data[type].parser == NULL)
  {
    return NULL;
  }

  return m_proto_data[type].data;
}

/**
 * 从会话中获取解析对象。
 * @param int type
 */
StreamData *CSession::get_stream_data_from_type(int type)
{
  if (type < 0 || (size_t)type >= COUNTOF(m_proto_data))
  {
    return NULL;
  }
  return m_proto_data[type].data;
}


/**
 * 设置解析对像到会话中。
 * @param const ConnData*conn
 * @param CParser *parser
 * @param StreamData *data
 */
bool CSession::set_parser(CParser *parser, StreamData *data)
{
  int type = m_comm->get_parser_type(parser);
  if (type < 0)
  {
    return false;
  }

  m_proto_data[type].parser = parser;
  m_proto_data[type].data = data;
  return true;
}

/**
 * 根据type设置解析对像到会话中。
 * @param int type
 * @param CParser *parser
 * @param StreamData *data
 */
bool CSession::set_parser_by_type(int type, CParser *parser, StreamData *data)
{
  if (type < 0 || (size_t)type >= COUNTOF(m_proto_data))
  {
    return false;
  }
  m_proto_data[type].parser = parser;
  m_proto_data[type].data = data;
  return true;
}

void CSession::set_session_mgt(CSessionMgt *psm)
{
  m_psm = psm;
}

CSessionMgt *CSession::get_session_mgt(void) const
{
  return m_psm;
}

void CSession::update_time(void)
{
  m_last_update = m_comm->gw_time();
}

const ConnData *CSession::get_conn(void) const
{
  return &m_con;
}

double CSession::get_ts(void) const
{
  const CTcpParser *tcp_parser = m_proto_data[SESSION_PROTO_TYPE_TCP].tcp_parser;
  const StreamData *data = m_proto_data[SESSION_PROTO_TYPE_TCP].data;
  if (unlikely(tcp_parser == NULL || data == NULL))
  {
    GWLOG_DEBUG(m_comm, "tcp_parser=%p data=%p\n", tcp_parser, data);
    return 0;
  }

  return tcp_parser->get_ts(data->a_tcp);
}

int64_t CSession::get_ts_ms(void) const
{
  const CTcpParser *tcp_parser = m_proto_data[SESSION_PROTO_TYPE_TCP].tcp_parser;
  const StreamData *data = m_proto_data[SESSION_PROTO_TYPE_TCP].data;
  if (unlikely(tcp_parser == NULL || data == NULL))
  {
    GWLOG_DEBUG(m_comm, "tcp_parser=%p data=%p\n", tcp_parser, data);
    return 0;
  }

  return tcp_parser->get_ts_ms(data->a_tcp);
}

CSession *CSession::create(void) const
{
  CSession *p = new CSession();
  p->set_gw_common(m_comm);
  p->init();
  return p;
}

void CSession::release(void)
{
  fini();
  delete this;
}

void CSession::clone(CSession *p)
{
  m_comm = p->m_comm;
  m_con = p->m_con;
  m_last_update = p->m_last_update;
  stat = p->stat;

  for (size_t i = 0; i < COUNTOF(m_proto_data); i++)
  {
    if (i == SESSION_PROTO_TYPE_TCP)
    {
      if (p->m_tcp_parser != NULL && p->m_tcp_stream_data != NULL)
      {
        // 直接移动数据，减少内存分配与数据拷贝
        m_proto_data[i].tcp_parser = m_tcp_parser = p->m_tcp_parser;
        m_proto_data[i].data = m_tcp_stream_data = p->m_tcp_stream_data;
        p->m_tcp_parser = NULL;
        p->m_tcp_stream_data = NULL;
        p->m_proto_data[i].data = NULL;        /* clone之后 m_proto_data[0]与m_tcp_stream_data指向统一地址，若不置空，释放空间会导致double free错误 */
        p->m_proto_data[i].tcp_parser = NULL;
        continue;
      }
      // 复制数据内容。
      m_proto_data[i].tcp_parser = m_tcp_parser = p->m_proto_data[i].tcp_parser;
      m_proto_data[i].data = m_tcp_stream_data = new StreamData();
      // GWLOG_TEST(m_comm, "m_tcp_parser=%p m_tcp_stream_data=%p\n", m_tcp_parser, m_tcp_stream_data);
      m_tcp_stream_data->a_tcp = m_tcp_parser->clone_stream_data(p->m_proto_data[i].data->a_tcp);
      continue;
    }

    m_proto_data[i].parser = p->m_proto_data[i].parser;
    if (p->m_proto_data[i].parser == NULL || p->m_proto_data[i].data == NULL)
    {
      continue;
    }

    //m_proto_data[i].data = new StreamData();
    //m_proto_data[i].data = p->m_proto_data[i].parser->clone_stream_data(p->m_proto_data[i].data);
    // 直接移动数据，减少内存分配与数据拷贝
    m_proto_data[i].data = p->m_proto_data[i].data;
    p->m_proto_data[i].data = NULL;
  }
}

bool CSession::has_proto_type(int type)
{
  if (type < 0 || (size_t)type >= COUNTOF(m_proto_data))
  {
    return false;
  }

  if (unlikely(m_proto_data[type].parser == NULL || m_proto_data[type].data == NULL))
  {
    return false;
  }

  return m_proto_data[type].parser->is_parsed(m_proto_data[type].data);
  // return true;
}

bool CSession::parser_chain_get(const CParser *p_parent, CParser *&parser)
{
  if (m_map_parser_chain.find(p_parent) == m_map_parser_chain.end())
  {
    return false;
  }

  parser = m_map_parser_chain[p_parent];
  return true;
}

bool CSession::parser_chain_add(const CParser *p_parent, CParser *parser)
{
  ASSERT(p_parent != parser);
  if (p_parent == parser)
  {
    return false;
  }

  m_map_parser_chain[p_parent] = parser;
  return true;
}

bool CSession::reset_parser_chain(void)
{
  m_map_parser_chain.clear();
  return true;
}

void CSession::clone_tcp_stream(CSession *p)
{
  m_comm = p->m_comm;
  m_con = p->m_con;
  m_last_update = p->m_last_update;
  stat = p->stat;

  for (size_t i = 0; i < COUNTOF(m_proto_data); i++)
  {
    if (i == SESSION_PROTO_TYPE_TCP)
    {
      // 复制数据内容。
      m_proto_data[i].tcp_parser = m_tcp_parser = p->m_proto_data[i].tcp_parser;
      m_proto_data[i].data = m_tcp_stream_data = new StreamData();
      // GWLOG_TEST(m_comm, "m_tcp_parser=%p m_tcp_stream_data=%p\n", m_tcp_parser, m_tcp_stream_data);
      m_tcp_stream_data->a_tcp = m_tcp_parser->clone_stream_data(p->m_proto_data[i].data->a_tcp);
      continue;
    }

    m_proto_data[i].parser = p->m_proto_data[i].parser;
    if (p->m_proto_data[i].parser == NULL || p->m_proto_data[i].data == NULL)
    {
      continue;
    }

    //m_proto_data[i].data = new StreamData();
    //m_proto_data[i].data = p->m_proto_data[i].parser->clone_stream_data(p->m_proto_data[i].data);
    // 直接移动数据，减少内存分配与数据拷贝
    m_proto_data[i].data = p->m_proto_data[i].data;
  }
}