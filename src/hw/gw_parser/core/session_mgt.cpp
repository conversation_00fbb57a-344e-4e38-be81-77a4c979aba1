/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include "session_mgt.h"

#include "session.h"
#include "gw_logger.h"

#include "gw_i_parser.h"
#include "gw_config.h"

/**
 * CSessionMgt implementation
 *
 * 网络包数据协议解析的会话管理。
 */

CSessionMgt::CSessionMgt() : m_comm(NULL)
                           , m_session_max_num(100)
                           , m_disable_timeout_check(0)
                           , m_last_check_time(0)
                           , m_proto_session_data({{{.parser = NULL}, 0}})
{
  //memset(m_proto_session_data, 0, sizeof(m_proto_session_data));
}

CSessionMgt::~CSessionMgt()
{
  free_all_session();
}

/**
 * @param const ConnData *conn
 */
// CSession *CSessionMgt::get_session(const ConnData *conn)
// {
//   return find_session(conn);
// }

/**
 * 查找会话Session。
 * @param const ConnData *conn
 */
CSession *CSessionMgt::find_session(const ConnData *conn)
{
  auto iter = m_map_session.find(*conn);
  if (iter == m_map_session.end()) {
    return NULL;
  }
  return iter->second;
}

/**
 * 创建新会话。
 * @param const ConnData *conn
 */
CSession *CSessionMgt::new_session(const ConnData *conn)
{

  if (m_session_max_num > 0 && m_map_session.size() >= (unsigned)m_session_max_num )
  {
    return NULL;
  }

  CSession *p = new CSession();
  if (NULL == p)
  {
    return NULL;
  }

  m_map_session[*conn] = p;
  p->set_gw_common(m_comm);
  p->set_session_mgt(this);

  p->init();
  p->m_con = *conn;

  return p;
}

/**
 * 删除会话。
 * @param constConnData *conn
 */
void CSessionMgt::del_session(const ConnData *conn)
{
  auto iter = m_map_session.find(*conn);
  if (iter == m_map_session.end())
  {
    return;
  }

  CSession *p = iter->second;
  m_map_session.erase(iter);

  if (p == NULL) 
  {
    return ;
  }

  p->del_data();
  p->fini();
  delete p;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CSessionMgt::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

/**
 * 设置解析对象在会话管理中的数据。
 * @param CParser *parser
 * @param SessionMgtData* data
 */
bool CSessionMgt::set_parser_data(CParser *parser, SessionMgtData *data)
{
  int type = m_comm->get_parser_type(parser);
  if (type < 0 || type >= MAX_SESSION_MGT_PROTO_TYPE_NUM)
  {
    return false;
  }

  m_proto_session_data[type].parser = parser;
  m_proto_session_data[type].data = data;
  return true;
}

/**
 * 获取解析对象在会话管理中的数据。
 * @param CParser *parser
 */
SessionMgtData *CSessionMgt::get_session_data_from_parser(CParser *parser)
{
  int type = m_comm->get_parser_type(parser);
  if (type < 0 || type >= MAX_SESSION_MGT_PROTO_TYPE_NUM || m_proto_session_data[type].parser == NULL)
  {
    return NULL;
  }

  return m_proto_session_data[type].data;
}

/**
 * 设置Tcp解析对像到会话中。
 * @param const ConnData*conn
 * @param CTcpParser *parser
 * @param StreamData *data
 */
CSession* CSessionMgt::set_tcp_parser(const ConnData *conn, CTcpParser *parser, StreamData *data)
{
  CSession *p = new_session(conn);
  if (p == NULL)
  {
    return NULL;
  }

  int type = SESSION_PROTO_TYPE_TCP;

  p->m_proto_data[type].tcp_parser = parser;
  if (p->m_proto_data[type].data == NULL )
  {
    p->m_proto_data[type].data = new StreamData();
  }
  p->m_proto_data[type].data->a_tcp = data->a_tcp;

  return p;
}

/**
 * 检查状态（主要检查是否有超时连接）。
 * @param int timeout
 */
void CSessionMgt::check_state(int timeout)
{
  // TODO
  // 1 记录当前检查时间
  // 2 检查当前会话是否存在超时

  if (m_disable_timeout_check) 
  {
    return ;
  }

  time_t ts =  m_comm->gw_time();
  if (ts - m_last_check_time < 2) 
  {
    return ;
  }
  m_last_check_time = ts;
  int null_cnt = 0;
  int free_cnt = 0;
  auto iter = m_map_session.begin();
  for (; iter != m_map_session.end();) 
  {
      if (iter->second == NULL) 
      {
        ++ iter;
        null_cnt ++;
        continue;
      }
      time_t session_ts = iter->second->m_last_update;
      long diff = ts - session_ts;
      if (diff > timeout) 
      {
        delete iter->second;
        free_cnt += 1;
        m_map_session.erase(iter++);
      }
      else 
      {
        ++iter;
      }
    
  }
}

void CSessionMgt::init()
{
  ASSERT(m_comm != NULL);
}

void CSessionMgt::fini()
{
  ASSERT(m_comm != NULL);
}

/**
 * @param int num
 */
void CSessionMgt::init_session(int num)
{
  // free_all_session();
  m_session_max_num = num;
}

/**
 * 释放所有会话数据。
  */
void CSessionMgt::free_all_session(void)
{
  std::map<ConnData, CSession *>::iterator iter;
  for (iter = m_map_session.begin(); iter != m_map_session.end(); iter++)
  {
    CSession *p = iter->second;
    if (p == NULL) {
      continue;
    }
    p->del_data();
    p->fini();
    delete p;
  }

  m_map_session.clear();

  for (size_t j = 0; j < COUNTOF(m_proto_session_data); j++)
  {
    if (j == SESSION_MGT_PROTO_TYPE_TCP)
    {
      continue;
    }
    if (m_proto_session_data[j].parser == NULL)
    {
      continue;
    }
    if (m_proto_session_data[j].data == NULL)
    {
      m_proto_session_data[j].parser = NULL;
      continue;
    }

    m_proto_session_data[j].parser->del_session_param(m_proto_session_data[j].data);
    m_proto_session_data[j].parser = NULL;
    m_proto_session_data[j].data = NULL;
  }
}

// void CSessionMgt::free_session_data(const ConnData *conn)
// {
//   if (m_map_session.find(*conn) == m_map_session.end())
//   {
//     return;
//   }

//   CSession *p = m_map_session[*conn];
//   if (p == NULL) {
//     return ;
//   }

//   p->del_data();
// }

/**
 * 创建新对象
 */
CSessionMgt *CSessionMgt::create(void) const
{
  CSessionMgt *p = new CSessionMgt();
  p->set_gw_common(m_comm);
  p->init();
  return p;
}

/**
 * 释放对象
 */
void CSessionMgt::release(void)
{
  fini();
  delete this;
}

/**
 * 克隆对象到队列中使用
 * @param CSessionMgt *
 */
void CSessionMgt::clone(const CSessionMgt *p)
{
  this->m_comm = p->m_comm;

  for (size_t i = 0; i < COUNTOF(m_proto_session_data); i++)
  {
    if (i == SESSION_MGT_PROTO_TYPE_TCP)
    {
      continue;
    }

    m_proto_session_data[i].parser = p->m_proto_session_data[i].parser;
    m_proto_session_data[i].data = p->m_proto_session_data[i].data;
  }
}


void CSessionMgt::add_addr_port(uint32_t dst_addr, uint16_t dst_port, uint32_t src_addr, uint16_t src_port, uint32_t flow, int first_link)
{
  m_addr_port_collect.add_addr_port(dst_addr, src_addr, dst_port, flow, first_link);
}

int CSessionMgt::get_top_addr_port_items(struct AddrPortCount *rets, int num, int order_field) 
{
  return m_addr_port_collect.get_top_items(rets, num, order_field);
}


void CSessionMgt::clear_addr_port_items()
{
  m_addr_port_collect.clear();
}
