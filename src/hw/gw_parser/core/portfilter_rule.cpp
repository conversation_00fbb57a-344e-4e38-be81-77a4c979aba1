/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <ctype.h>

#include "gw_common.h"
#include "gw_logger.h"
#include "portfilter_rule.h"
#include "tcp_parser.h"
#include "gw_config.h"
#include "nacos_listen_conf.h"

#define PORT_NONE (0xffffffff)

CPortfilterRule::CPortfilterRule(void) : m_p_port_filter_rule(NULL)
                                       , m_p_port_white_rule(NULL)
                                       , m_comm(NULL)
                                       , m_tcpparser(NULL)
{
}

CPortfilterRule::~CPortfilterRule(void)
{
    fini();
}

void CPortfilterRule::init()
{
    ASSERT(m_comm != NULL);
    ASSERT(m_tcpparser != NULL);
}

void CPortfilterRule::fini()
{
    free_conf_port(m_p_port_filter_rule);
    m_p_port_filter_rule = NULL;

    free_conf_port(m_p_port_white_rule);
    m_p_port_white_rule = NULL;
}

/**
 * 命中过滤规则。
 * @param unsigned int data
 */
int CPortfilterRule::hit(unsigned data)
{
    return 0;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CPortfilterRule::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);

    m_comm = comm;
}

 /**
 *  设置TcpParser对象实例 
 *  @paramount CTcpParser *tcpparser
 */
void CPortfilterRule::set_tcp_parser(CTcpParser *tcpparser)
{
    ASSERT(tcpparser != NULL);
    m_tcpparser = tcpparser;
}

/**
 *  设置端口过滤 
 *  @param const char*
 */
void CPortfilterRule::set_port_filter(const char *p_port_filter_rule)
{
    int i_ret = 0;
    free_conf_port(m_p_port_filter_rule);

    m_p_port_filter_rule = (conf_port_filter_t *)malloc(sizeof(conf_port_filter_t));
    if (m_p_port_filter_rule == NULL)
    {
        GWLOG_ERROR(m_comm, "malloc port filter rule failed\n");
        return;
    }
    memset(m_p_port_filter_rule, 0, sizeof(conf_port_filter_t));

    i_ret = read_conf_port_filter_inner(p_port_filter_rule, m_p_port_filter_rule);
    if (i_ret != 0)
    {
        free_conf_port(m_p_port_filter_rule);
    }

    return;
}

 void CPortfilterRule::set_port_white(const char* p_port_white_rule)
 {
    int i_ret = 0;
    free_conf_port(m_p_port_white_rule);

    m_p_port_white_rule = (conf_port_filter_t *)malloc(sizeof(conf_port_filter_t));
    if (m_p_port_white_rule == NULL)
    {
        GWLOG_ERROR(m_comm, "malloc port filter rule failed\n");
        return;
    }
    memset(m_p_port_white_rule, 0, sizeof(conf_port_filter_t));

    i_ret = read_conf_port_filter_inner(p_port_white_rule, m_p_port_white_rule);
    if (i_ret != 0)
    {
        free_conf_port(m_p_port_white_rule);
    }

    return;
 }
 
/**
 *  命中端口过滤规则 
 *  @param unsigned short
 */
int CPortfilterRule::port_filter_hit(unsigned short port)
{
    // size_t i;
    int ret = 0; // 1 命中;  0 未命中
    conf_port_filter_t *conf_ptr = m_p_port_filter_rule;
    if (conf_ptr == NULL)
    {
        return 0;
    }

    //printf("%d %x %x\n", port >> 5, conf_ptr->port_bit[port >> 5], (1 << (port & 0x1f)));
    if (conf_ptr->port_bit[port >> 5] & (1 << (port & 0x1f)))
    {
        // 命中
        return 1;
    }

    return ret;
}

int CPortfilterRule::port_white_hit(unsigned short port)
{
    int ret = 0; // 1 命中; 0未命中
    conf_port_filter_t *conf_ptr = m_p_port_white_rule;
    if (conf_ptr == NULL)
    {
        return 0;
    }

    if (conf_ptr->port_bit[port >> 5] & (1 << (port & 0x1f)))
    {
        // 命中
        return 1;
    }

    return ret;
}

/**
 *  PORT参数更新 
 */
void CPortfilterRule::port_filter_for_mon(int is_for_nacos, const char *s)
{
    const char *ss = NULL;
    if (!is_for_nacos)
    {
        ASSERT(m_comm != NULL);
        ASSERT(m_tcpparser != NULL);
        CGwConfig *pgwc = m_comm->get_gw_config();
        ASSERT(pgwc != NULL);
        std::string str_port_filter = pgwc->read_conf_string("parser", "port_filter");
        ss = str_port_filter.c_str();
    }
    else
    {
        ss = s;
    }

    conf_port_filter_t *conf_ptr = NULL;
    conf_ptr = (conf_port_filter_t *)malloc(sizeof(conf_port_filter_t));

    if (conf_ptr == NULL)
    {
        return;
    }
    memset(conf_ptr, 0, sizeof(conf_port_filter_t));

    if (0 == read_conf_port_filter_inner(ss, conf_ptr))
    {
        std::lock_guard<std::mutex> lock(m_conf_mutex);
        conf_port_filter_t *old_conf_ptr = m_p_port_filter_rule;
        m_p_port_filter_rule = conf_ptr;

        // 等待旧配置不在使用
        if (m_tcpparser->wait_for_worker_use_conf())
        {
            return;
        }
        free_conf_port(old_conf_ptr);
        return;
    }
    free_conf_port(conf_ptr);
    return;
}

void CPortfilterRule::port_white_for_mon(int is_for_nacos, const char *s)
{
    const char *ss = NULL;
    if (!is_for_nacos)
    {
        ASSERT(m_comm != NULL);
        ASSERT(m_tcpparser != NULL);
        CGwConfig *pgwc = m_comm->get_gw_config();
        ASSERT(pgwc != NULL);
        std::string str_port_white = pgwc->read_conf_string("parser", "port_white");
        ss = str_port_white.c_str();
    }
    else
    {
        ss = s;
    }

    conf_port_filter_t *conf_ptr = NULL;
    conf_ptr = (conf_port_filter_t *)malloc(sizeof(conf_port_filter_t));

    if (conf_ptr == NULL)
    {
        return;
    }
    memset(conf_ptr, 0, sizeof(conf_port_filter_t));

    if (0 == read_conf_port_filter_inner(ss, conf_ptr))
    {
        std::lock_guard<std::mutex> lock(m_conf_mutex);
        conf_port_filter_t *old_conf_ptr = m_p_port_white_rule;
        m_p_port_white_rule = conf_ptr;

        // 等待旧配置不在使用
        if (m_tcpparser->wait_for_worker_use_conf())
        {
            return;
        }
        free_conf_port(old_conf_ptr);
        return;
    }
    free_conf_port(conf_ptr);
    return;
}

void CPortfilterRule::free_conf_port(conf_port_filter_t *p_port_filter)
{
    if (p_port_filter == NULL)
    {
        return;
    }

    SAFE_FREE(p_port_filter->conf_port_filter_port);
    free(p_port_filter);
}

int CPortfilterRule::read_conf_port_filter_inner(const char *s, conf_port_filter_t *conf_ptr)
{
    // "443,9000-10000"
    if (s == NULL)
    {
        return 1;
    }
    char *ss = (char *)s;
    while ((ss = strchr(ss, ',')) != NULL)
    {
        ss++;
        conf_ptr->conf_port_filter_num++;
    }
    conf_ptr->conf_port_filter_num++;
    conf_ptr->conf_port_filter_port = (port_filter_data_t *)malloc(conf_ptr->conf_port_filter_num * sizeof(port_filter_data_t));
    if (conf_ptr->conf_port_filter_port == NULL)
    {
        conf_ptr->conf_port_filter_num = 0;
        return 1;
    }
    memset(conf_ptr->conf_port_filter_port, 0, conf_ptr->conf_port_filter_num * sizeof(port_filter_data_t));

    
    int k = 0;
    const char *p = NULL;
    const char *p1 = s;
    const char *p2 = NULL;
    while (p1)
    {
        char buf[256] = {0};
        p = strchr(p1, ',');
        p2 = (p == NULL) ? (p1 + strlen(p1)) : p++;
        size_t length = p2 - p1;
        if (length >= sizeof(buf) || length == 0)
        {
            p1 = p;
            continue;
        }
    
        memcpy(buf, p1, length);
        p1 = p;
        buf[length] = '\0';

        unsigned int port1 = PORT_NONE;
        unsigned int port2 = PORT_NONE;
        // 连接符分隔
        const char *pp1 = strchr(buf, '-');
        if (pp1)
        {
            // 90000-10000
            port1 = get_port_num(buf, pp1 - buf);
            port2 = get_port_num(pp1 + 1, strlen(pp1 + 1));
            if (port1 == PORT_NONE || port2 == PORT_NONE)
            {
                continue;
            }
        }
        else
        {
            // 443
            port1 = get_port_num(buf, length);
            if (port1 == PORT_NONE)
            {
                continue;
            }
            port2 = port1;
        }
        if (port1 > port2)
        {
            continue;
        }

        // add port list
        //printf("%d %d %d\n", k, port1, port2);
        if (k < conf_ptr->conf_port_filter_num)
        {
            port_filter_data_t *pda = &conf_ptr->conf_port_filter_port[k++];
            pda->port_min = port1;
            pda->port_max = port2;
        }
    }

    conf_ptr->conf_port_filter_last = k;
    for (; k < conf_ptr->conf_port_filter_num; k++)
    {
        port_filter_data_t *pda = &conf_ptr->conf_port_filter_port[k];
        pda->port_min = PORT_NONE;
        pda->port_max = PORT_NONE;
    }

    // 按 port_min 进行升序排
    qsort(conf_ptr->conf_port_filter_port, conf_ptr->conf_port_filter_last, sizeof(conf_ptr->conf_port_filter_port[0]), compare_port_filter_data);

    // 生成端口位图
    for (k = 0; k < conf_ptr->conf_port_filter_last; k++)
    {
        port_filter_data_t *pda = &conf_ptr->conf_port_filter_port[k];
        for (unsigned int i = pda->port_min; i <= pda->port_max; i++)
        {
            unsigned int port = (i);
            conf_ptr->port_bit[port >> 5] |= (1 << (port & 0x1f));
        }
    }

    return 0;
}

int CPortfilterRule::get_port_num(const char *s, size_t length)
{
    
    char buf[64] = {0};
    if (length >= sizeof(buf))
    {
        return PORT_NONE;
    }

    for (size_t i = 0; i < length; i++)
    {
        if (!isdigit(s[i]))
        {
            return PORT_NONE;
        }
    }
    memcpy(buf, s, length);
    return  atoi(buf);
}

int CPortfilterRule::compare_port_filter_data(const void *a, const void *b)
{
    port_filter_data_t *p1 = (port_filter_data_t *)a;
    port_filter_data_t *p2 = (port_filter_data_t *)b;

    if (p1->port_min < p2->port_min)
    {
        return -1;
    }
    if (p1->port_min > p2->port_min)
    {
        return 1;
    }
    return 0;
}