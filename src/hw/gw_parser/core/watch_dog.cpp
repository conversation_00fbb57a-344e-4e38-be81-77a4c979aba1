/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <semaphore.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include "gw_common.h"
#include "watch_dog.h"
#include "gw_logger.h"
#include "utils.h"

#define MAX_THREAD_CNT    1024        /* 定义最大线程数量 */
#define VERSION_NUM       2018
#define IPCKEY            0x2000000   /* 自定义共享内存KEY */
#define DELAY_TIME        5           /* 休眠时间为5秒 */

#define SEM_WRITE         "sem_write" /* 有名信号量，用于写，与守护进程相同 */
#define SEM_READ          "sem_read"  /* 有名信号量，用于读，与守护进程相同 */
#define MODE_FLAG         S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH
#define PATH_PROC_SYSV_SHM    "/proc/sysvipc/shm"
#define BUF_LEN           255

typedef struct
{
    uint64_t u64_increase;               /* 用于确定线程是否live */
    pthread_info_t st_pthread_info;
}pthread_status_t;

typedef struct pthread_watch_info
{
    uint32_t u32_version;                            /* 版本号（与守护进程保持相同，可以用来确保信号源的正确性以及版本对应） */
    pid_t process_pid;                               /* 被监控进程的进程ID */
    uint32_t u32_pthread_num;                        /* 线程的数量 */
    pthread_status_t a_st_pthread_status[MAX_THREAD_CNT];
}pthread_watch_info_t;

/**
 * CWatchDog implementation
 *
 * 线程守护模块。需要各线程在实际中使用。
 */

CWatchDog::CWatchDog(void) : m_comm(NULL)
                           , m_quit_signal(0)
                           , m_u32_thread_cnt(0)
                           , m_i_watch_thread_state(0)
                           , m_p_st_watch_info(NULL)
{
}

CWatchDog::~CWatchDog(void)
{
}

/**
 * 线程循环中周期调用，报告状态。
 */
void CWatchDog::report()
{
}

void CWatchDog::init()
{
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;
  m_p_st_watch_info = new pthread_watch_info_t;
}

void CWatchDog::fini()
{
  ASSERT(m_comm != NULL);
  if (m_p_st_watch_info)
  {
    delete m_p_st_watch_info;
  }
}

void CWatchDog::run()
{
  ASSERT(m_comm != NULL);
  int i_ret = 0;

  if (0 != (i_ret = pthread_create(&m_pthread_key, NULL, (void *(*)(void *))pthread_watch_module, this)))
  {
      GWLOG_ERROR(m_comm, "routine watch thread create failed(%s)\n", strerror(i_ret));
  }
  else
  {
      GWLOG_INFO(m_comm, "routine watch thread create successfully\n");
      m_i_watch_thread_state = 1;
  }
}

/**
 * 触发退出信号时处理
 */
void CWatchDog::set_quit_signal(void)
{
  m_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CWatchDog::wait_for_stop(void)
{
  if (m_i_watch_thread_state == 1)
  {
    pthread_join(m_pthread_key, NULL);
  }

  m_i_watch_thread_state = 0;
  return;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CWatchDog::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

 /**
 * 线程初始调用一次开始守护，分配序号。
 */
int CWatchDog::watch_module_conf_pthread_info(const pthread_info_t *p_st_pthread_info, uint32_t  *p_u32_index)
{
  if (NULL == p_st_pthread_info || NULL == p_u32_index)
  {
      printf("param is null");
      return -1;
  }

  /* 线程数加1，并获取数据的索引 */
  *p_u32_index = __sync_fetch_and_add(&m_u32_thread_cnt, 1);
  /* 判断线程数是否超出MAX_THREAD_CNT */
  if (*p_u32_index >= MAX_THREAD_CNT)
  {
      printf("array out of bouds(%d)\n", *p_u32_index);
      __sync_fetch_and_sub(&m_u32_thread_cnt, 1);
      return -1;
  }
  m_p_st_watch_info->a_st_pthread_status[*p_u32_index].u64_increase++;
  memcpy(&(m_p_st_watch_info->a_st_pthread_status[*p_u32_index].st_pthread_info)
        , p_st_pthread_info
        , sizeof(pthread_info_t));

  return 0;
}

/**
 * 各个线程报告状态
 */
int CWatchDog::watch_modole_report_pthread_info(uint32_t u32_index)
{
  if (u32_index >= m_u32_thread_cnt)
  {
      printf("paran is invailed(index = %u), (thread cnt = %u)\n", u32_index, m_u32_thread_cnt);
      return -1;
  }

  m_p_st_watch_info->a_st_pthread_status[u32_index].u64_increase++;

  return 0;
}

/* 参考ipcs源码，在/proc/sysvipc/shm文件下是有IPCKEY */
static int is_create_shm(void)
{
    int i_shmkey = 0;
    FILE *fp = NULL;
    fp = fopen(PATH_PROC_SYSV_SHM, "r");
    if (fp == NULL)
    {
        printf("open file(%s) failed\n", PATH_PROC_SYSV_SHM);
        return -1;
    }  

    while (fgetc(fp) != '\n');		/* skip header */
    while (feof(fp) == 0)
    {
        fscanf(fp, "%d \n", &i_shmkey);  /* 取出shmkey */
        //printf("i_shmkey = %d\n", i_shmkey);
        if (i_shmkey == IPCKEY)       
        {
            /* 创建了IPCKEY的共享内存 */
            fclose(fp);
            //printf("already create shm\n");
            return 0;
        }
    } 
    fclose(fp);
    //printf("not create shm\n");
    return -1;
}

/* 检测到共享内存并能够正确的绑定到自己的进程空间中，返回0 */
/* 其他情况返回-1 */
static int check_shm(int *p_i_shmid, pthread_watch_info_t **pp_st_watch_info)
{
    //printf("check shm\n");
    int i_shmid = 0;
    pthread_watch_info_t *p_st_watch_info = NULL;

    int i_ret = is_create_shm();
    if (i_ret != 0)
    {
        return i_ret;
    }
   
    /* 获取共享内存的ID */
    i_shmid = shmget(IPCKEY, sizeof(pthread_watch_info_t), MODE_FLAG);     
    if (i_shmid < 0)
    {
        printf("shmget shmid failed(%d), errno = %d\n", i_shmid, errno);
        return -1;
    }

    /* 绑定内存空间 */
    p_st_watch_info = (pthread_watch_info_t*)shmat(i_shmid, NULL, 0);
    if(p_st_watch_info == (void*)-1)
    {
        printf("shmat failed\n");
        return -1;
    }

    *p_i_shmid = i_shmid;
    *pp_st_watch_info = p_st_watch_info;
    return 0;
}

/* 创建一个有名信号量，linux系统会在/dev/shm/目录下生成sem. xxx,其中“xxx”是程序创建的有名信号名名字 */
static int is_sem_file_change(const char *p_sem_name, uint64_t *p_u64_sem_file_ctime)
{
    int i_ret = 0;
    char a_buf[BUF_LEN];
    memset(a_buf, 0, sizeof(a_buf));
    sprintf(a_buf, "/dev/shm/sem.%s", p_sem_name);
    //printf("a_buf = %s\n", a_buf);
    struct stat st_stat;
    memset(&st_stat, 0, sizeof(struct stat));
    /* 判断文件名是否存在 */
    i_ret = stat(a_buf, &st_stat); 
    if (i_ret != 0)
    {
        //printf("sem(%s) is not exist\n", p_sem_name);
        return i_ret;
    }
    //printf("sem(%s) is exist \n", p_sem_name);
    if (p_u64_sem_file_ctime != NULL)
    {
        uint64_t u64_sem_file_ctim = (st_stat.st_ctim.tv_sec * 1000000000) + st_stat.st_ctim.tv_nsec;
        if (*p_u64_sem_file_ctime != u64_sem_file_ctim)
        {
            *p_u64_sem_file_ctime = u64_sem_file_ctim;
            //printf("sem file ctime = %llu\n", *p_u64_sem_file_ctime);
        }
        else
        {
            /* 线程开始判断是否创建信号量，是不会走到这个分支的 */
            return -1;
        }
    }
        
    return 0;
}

/* 检测有名信号量，返回0，否则返回-1 */
static int check_sem(sem_t **pp_sem_param
                   , const char *p_sem_name
                   , uint64_t *p_u64_sem_file_ctime)
{
    sem_t *p_sem = NULL;
    int i_ret = 0;
    i_ret = is_sem_file_change(p_sem_name, p_u64_sem_file_ctime);
    if (i_ret != 0)
    {
        return i_ret;
    } 

    /* 获取已经创建的读写信号量 */
    p_sem = sem_open(p_sem_name, O_CREAT);
    if (p_sem == SEM_FAILED)
    {
        return -1;
    } 
    *pp_sem_param = p_sem;
    return 0;
}

int CWatchDog::pthread_watch_module(void *arg_ptr)
{
  CWatchDog *pThis = (CWatchDog *)arg_ptr;
  pThis->routine_watch();
  return 0;
}

int CWatchDog::routine_watch()
{
    int i_ret = 0;
    int i_check_shm_succ_flag = 0;
    int i_check_sem_r_succ_flag = 0;
    int i_check_sem_w_succ_flag = 0;
    int i_shmid = 0;
    pthread_watch_info_t *p_st_watch_info = NULL;

    sem_t *p_sem_write = NULL;
    sem_t *p_sem_read = NULL;
    uint64_t u64_sem_write_file_ctime = 0;   /* 有名写信号量文件的状态改变时间 */
    struct timespec st_time_out;

    /* 检测是否已创建了共享内存，直到监测到 */
    while(!m_quit_signal)
    {
        if (!i_check_shm_succ_flag)
        {
            i_ret = check_shm(&i_shmid, &p_st_watch_info);
            if(i_ret == 0)
            {
                //printf("shm success\n");
                i_check_shm_succ_flag = 1;
            }
        }

        if (!i_check_sem_r_succ_flag)
        {
            i_ret = check_sem(&p_sem_read, SEM_READ, NULL);
            if (i_ret == 0)
            {
                //printf("sem read succ\n");
                i_check_sem_r_succ_flag = 1;
            }
        }

        if (!i_check_sem_w_succ_flag)
        {
            i_ret = check_sem(&p_sem_write, SEM_WRITE, &u64_sem_write_file_ctime);
            if (i_ret == 0)
            {
                //printf("sem write succ\n");
                i_check_sem_w_succ_flag = 1;
            }
        }        

        if (i_check_sem_r_succ_flag && i_check_sem_w_succ_flag && i_check_shm_succ_flag)
        {
            break;
        }
        sleep(DELAY_TIME);
    }
    //printf("test\n");
    //printf("p_sem_write = %p\n", p_sem_write);
    /* 此处获取写信号量的值，如果是0，则置为1 */
    /* why：因为gw_parser进程可能异常退出，写信号量没有没有来得及sem_post，这样会导致两个进程一只在等待信号量 */
    if (p_sem_write != NULL)
    {
        int i_sem_value = 0;
        i_ret = sem_getvalue(p_sem_write, &i_sem_value);
        if (i_ret != 0)
        {
            /* 失败怎么处理 */
            printf("get sem value failed(%d, errno = %d)\n", i_ret, errno);
        }
        //printf("write sem value = %d\n", i_sem_value);
        if (i_sem_value == 0)
        {
            sem_post(p_sem_write);
        }
    }

    /* 循环写共享内存 */
    while (!m_quit_signal)
    {
        sleep(DELAY_TIME);
        st_time_out.tv_sec = DELAY_TIME;
        st_time_out.tv_nsec = 0;
        //printf("p_sem_write = %p\n", p_sem_write);
        i_ret = sem_timedwait(p_sem_write, &st_time_out);
        if (i_ret != 0)
        {
            if (errno == ETIMEDOUT)
            {
                //printf("sem timedwait time out\n");
                struct shmid_ds st_shmid_ds;
                shmctl(i_shmid, IPC_STAT, &st_shmid_ds);
                if (st_shmid_ds.shm_perm.__key != IPCKEY)
                {
                    shmdt(p_st_watch_info);
                }
                /* 判断读写信号量文件的ctmie是否改变，出现这样的情况的一种情况是守护进程异常退出，重新起来的时候，会创建新的信号量 */
                i_ret = is_sem_file_change(SEM_WRITE, &u64_sem_write_file_ctime);
                if (i_ret == 0)
                {
                    //printf("write sem file change\n");
                    sem_close(p_sem_read);
                    sem_close(p_sem_write);
                    p_sem_write = sem_open(SEM_WRITE, O_CREAT);
                    p_sem_read = sem_open(SEM_READ, O_CREAT);

                    check_shm(&i_shmid, &p_st_watch_info);
                }
            }
            continue;
        }
        
        //printf("version = %d\n", m_st_watch_info.u32_version);
        m_p_st_watch_info->u32_pthread_num = m_u32_thread_cnt;
        memcpy(p_st_watch_info, m_p_st_watch_info, sizeof(pthread_watch_info_t));
        /* test */
        //printf("pthread num = %u\n", m_st_watch_info.u32_pthread_num);

        sem_post(p_sem_read);
    }

    if (p_st_watch_info != NULL)
    {
        shmdt(p_st_watch_info);
    }

    if (p_sem_read != NULL)
    {
        sem_close(p_sem_read);
    }

    if (p_sem_write != NULL)
    {
        sem_close(p_sem_write);
    }

    // pthread_exit(NULL);
    return 0;
}


void CWatchDog::begin()
{

}

void CWatchDog::end()
{
    
}