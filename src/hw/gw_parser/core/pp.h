/*
 */

#ifndef __PROTO_PARSER_PP_H__
#define __PROTO_PARSER_PP_H__

#include <sys/types.h>
#include <netinet/in_systm.h>
#include <netinet/in.h>
#include <netinet/ip.h>
#include <netinet/ip6.h>
#include <netinet/tcp.h>
#include <pcap.h>

#if (__APPLE__ && __MACH__)

#ifndef s6_addr16
# define s6_addr16		__u6_addr.__u6_addr16
#endif

#ifndef s6_addr32
# define s6_addr32		__u6_addr.__u6_addr32
#endif

#ifdef HAVE_TCP_STATES
#undef HAVE_TCP_STATES
#endif

#endif // (__APPLE__ && __MACH__)

#ifdef __cplusplus
extern "C" {
#endif

typedef struct
{
    int sk_buff_size;
    int dev_addon;
    void (*no_mem)(const char *);
    //  int (*ip_filter)();
    void (*syslog)(int, int, void *, void *, int);
    int syslog_level;
    int n_tcp_streams;
    int one_loop_less;
    int tcp_flow_timeout;
    int n_hosts;
    int tcp_workarounds;
} gw_pp_prm_t;

extern gw_pp_prm_t gw_pp_params;

enum
{
    PP_WARN_IP = 1,
    PP_WARN_IP6,
    PP_WARN_TCP,
    PP_WARN_UDP,
    PP_WARN_SCAN
};

enum
{
    PP_WARN_UNDEFINED = 0,
    PP_WARN_IP_OVERSIZED,
    PP_WARN_IP_INVLIST,
    PP_WARN_IP_OVERLAP,
    PP_WARN_IP_HDR,
    PP_WARN_IP_SRR,
    PP_WARN_IP6_OVERSIZED,
    PP_WARN_IP6_HDR,
    PP_WARN_IP6_OVERLAP,
    PP_WARN_TCP_TOOMUCH,
    PP_WARN_TCP_HDR,
    PP_WARN_TCP_BIGQUEUE,
    PP_WARN_TCP_BADFLAGS
};

enum
{
    TCP_CONNECT_INVALID = 0,
    TCP_CONNECT_NORMAL,
    TCP_CONNECT_CACHE,
    TCP_CONNECT_PACKET_LOST,
    TCP_CONNECT_DISCARDING_DATA_FROM_SERVER,
    TCP_CONNECT_DISCARDING_DATA_FROM_CLIENT,
    TCP_CONNECT_FIRST_PACKET_LOST
};

enum
{
    TCP_DROP_INVALID = 0,
    TCP_DROP_BY_HTTP_TRUNK_FAILED,
    TCP_DROP_BY_HTTP_TRUNK_TOO_LARGE,
    TCP_DROP_BY_HTTP_PARSED_FAILED,
    TCP_DROP_BY_HTTP_PAYLOAD_TOO_LARGE,
    TCP_DROP_BY_CACHE_TOO_LARGE,
    TCP_DROP_BY_PORT_FILTER,
    TCP_DROP_BY_URG,
    
    
    
    
    TCP_DROP_BY_PACKET_LOST = 2000
};

#define PP_JUST_EST             1
#define PP_DATA                 2
#define PP_CLOSE                3
#define PP_RESET                4
#define PP_TIMED_OUT            5
#define PP_JUST_EST_FROM_CACHE  6
#define PP_DISCARDING_DATA      7
#define PP_EXITING              8 /* exiting; last chance to get data */
#define PP_SESSION_OVERFULL     9

#define PP_DO_CHKSUM 0
#define PP_DONT_CHKSUM 1

#define TCP_STREAM_STREAM_ONLY_CLIENT 1
#define TCP_STREAM_STREAM_ONLY_SERVER 2

#define TCP_SINGLE_STREAM_TRIGGER_CONDITION_COUNT 1
#define TCP_SINGLE_STREAM_TRIGGER_CONDITION_CLOSE 2

struct tuple4
{
    int i_ip_type; // 0 ipv4 1 ipv6
    u_short source;
    u_short dest;
    union {
        u_int saddr;      // ipv4源地址
        u_int a_saddr[4]; // ipv6源地址
    };
    union {
        u_int daddr;      // ipv4 目的地址
        u_int a_daddr[4]; // ipv6 目的地址
    };
};

struct half_stream
{
    char *data;
    int offset;
    int count;
    int count_new;
    int stream_cache_size;
    int data_len;//真实的数据长度
    
    int complete; //当前请求响应流是否完整
    int lost_len;//累计丢失的字节数
    int first_lost_offset;//第一次丢包的位置
    int missing_info_len;//插入丢包信息的长度
    int fin_state;
    
    u_int ack;
//    u_int current_packet_ack;
    u_int seq;
    u_int first_seq; // 当前请求或响应第一个包的seq
    u_int stream_ack; // 当前请求或响应的ack
    u_int excepted_first_seq; //真正意义上的第一个seq
    // int urg_count;
    // u_char urgdata;
    // u_char count_new_urg;
    // u_char urg_seen;
    // u_int urg_ptr;
    // u_char ts_on;
    // u_char ts_on_2;
    // u_int curr_ts;
    struct skbuff *head;//很多的内核变量命名应当修改
    struct skbuff *tail;
    struct skbuff *node; //存储新数据流到达的包，使处理保持一致性
};

struct tcp_timeout
{
    struct tcp_stream *a_tcp;
    struct timeval timeout;
    struct tcp_timeout *next;
    struct tcp_timeout *prev;
};

struct pp_chksum_ctl
{
    u_int netaddr;
    u_int mask;
    u_int action;
    u_int reserved;
};

typedef struct
{
    int8_t i8_defrag_flag; /* IPv6分段标识, 0:不分段 1:分段 */
    //u_int8_t u8_l4_protocol;     /* 四层协议 */
    u_int16_t u16_defrag_offset; /* 整个IPv6数据的位置 */
    unsigned u_defrag_len;       /* 分段数据包的长度 */
    unsigned u_idenfy_id;        /* 分段标识ID */
} ip6_defrag_info_t;


typedef struct 
{
    uint64_t total_packets;
    uint64_t invalid_ip_packets;
    uint64_t invalid_v4_gre_packets;
    uint64_t invalid_v6_in_v4_packets;
    uint64_t invalid_v6_packets;
    uint64_t invalid_v6_gre_packets;
    uint64_t invalid_v6_in_v6_packets;
    uint64_t not_tcp_packets;
    uint64_t ipv4_defrag_packets;     /* ipv4的分片的个数 */
    uint64_t ipv6_defrag_packets;     /* ipvt的分片的个数 */
}ip_parser_lost_packets;
#define IP_PARSER_TOTAL "ip parser"
#define IP_PARSER_INVALID "invalid ip"
#define IP_PARSER_INVALID_V4_GRE "invalid v4 gre"
#define IP_PARSER_INVALID_V6_IN_V4 "invalid v6 in v4"
#define IP_PARSER_INVALID_V6 "invalid v6"
#define IP_PARSER_INVALID_V6_GRE "invalid v6 gre"
#define IP_PARSER_INVALID_V6_IN_V6 "invalid v6 in v6"
#define IP_PARSER_NOT_TCP "not tcp"
#define IPV4_DEFRAG "ipv4 defrag"
#define IPV6_DEFRAG "ipv6_defrag"

extern volatile ip_parser_lost_packets ip_lost;
extern uint8_t ip_lost_count;
extern ip_parser_lost_packets *ip_lost_array[64];

extern void pp_register_chksum_ctl(struct pp_chksum_ctl *, int);
int get_in6_l4_protocol(char *p_buf, int i_payload_offset, uint8_t *p_u8_type, ip6_defrag_info_t *p_st_defrag_info);
int parser_data_link_layer(u_char *data, int *p_offset);
//void calc_linkoffset(unsigned char *data, int *linkoffset);
void pp_pcap_handler(u_char *, struct pcap_pkthdr *, u_char *);

typedef struct
{
    // for userdata
    void *userdata; // = NULL;
    void *session;  // = NULL;
    
    // for pcap
    int linktype;                            // = DLT_EN10MB;
    u_int pp_linkoffset;                     // = 0;
    struct pcap_pkthdr *pp_last_pcap_header; // = NULL;
    u_char *pp_last_pcap_data;               // = NULL;
    struct tcp_timeout *pp_tcp_timeouts;     // = NULL;
    struct tcp_timeout *pp_tcp_timeouts_tail;     // = NULL;
    int i_ip_type; // 0:IPv4 1:IPv6
    
    //for gre
    int i_gre_flag;              // 0:不是gre协议， 1:gre协议
    int i_gre_ip_type;           // gre协议新IP头的类型(IPv4或IPv6), 0:IPv4 1:IPv6
    u_int gre_saddr;             // 0
    u_int gre_daddr;             //
    uint32_t a_gre_in6_saddr[4]; // {0};  IPv6  gre源地址
    uint32_t a_gre_in6_daddr[4]; // {0};  IPv6  gre目的地址
    
    // for ipv6
    int i_ipv6_header_len;                // 0;    IPv6头部长度(包括附加头)
    u_int8_t u8_l4_protocol;              // 0;    IPv6上层协议
    ip6_defrag_info_t st_ip6_defrag_info; //       IPv6分段信息
    
    // for tcp
    struct tcp_stream **tcp_stream_table;
    struct tcp_stream *streams_pool;
    int tcp_num; // = 0;
    int tcp_stream_table_size;
    int tcp_run_max_stream;
    int max_stream;
    struct tcp_stream *tcp_latest; // = 0;
    struct tcp_stream *tcp_oldest; // = 0;
    struct tcp_stream *http_latest;
    struct tcp_stream *http_oldest;
    struct tcp_stream *free_streams;
    struct ip *ugly_iphdr;
    struct ip6_hdr *ugly_ip6hdr;
    
    // for ip fragment
    struct hostfrags **fragtable;
    struct hostfrags *this_host;
    int numpack; // = 0;
    int hash_size;
    int timenow;
    unsigned int time0;
    struct timer_list *timer_head; // = 0;
    struct timer_list *timer_tail; // = 0;
    
    const char *p_mac;
    int bool_need_mac;
    
    const char *pcap_filename;
    
    uint16_t vlan_id;

    uint32_t http_num;
    uint32_t http_max_num;
} worker_params_t;

struct tcp_stream
{
    struct tuple4 addr;
    unsigned char client_mac[6];
    unsigned char server_mac[6];
    char pp_state;
    struct lurker_node *listeners;
    struct half_stream client;
    struct half_stream server;
    struct tcp_stream *next_node;
    struct tcp_stream *prev_node;
    int hash_index;
    struct tcp_stream *next_time;
    struct tcp_stream *prev_time;
    int read;
    struct tcp_stream *next_http;
    struct tcp_stream *prev_http;
    int total;
    struct tcp_stream *next_free;
    // void *user;
    long ts;
    long ts_us;
    char reverse;
    char direction_confirmed;
    char header_complete;
    int drop_data_reason;
    int8_t b_analyse;
    struct tcp_timeout *timeout;
    char pcap_filename[256];
    void* p_session;
    
    // use to tcp-single-stream
    uint64_t packet_num;
    uint8_t is_finded;
    
    uint16_t vlan_id;
    
    char dir;
    
    worker_params_t* pwp;
    uint8_t is_http;// 是否为http连接 =0 未决; =1 http连接; =2 非http连接;
    uint8_t probe_num; // TODO 探测5次以上则认为是非应用数据流,不在进行缓存,有必要开放配置
};

void pp_tcp_discard(struct tcp_stream *a_tcp, int num);
void pp_tcp_discard_force(struct tcp_stream *a_tcp, int num);
void pp_tcp_discard_force_update(struct tcp_stream *a_tcp, struct half_stream *rcv, int num);
void pp_register_tcp(void(*x));
void pp_unregister_tcp(void(*x));

void pp_register_port_filter_hit(void(*x));

void pp_register_port_white_hit(void(*x));

int pp_init(worker_params_t *, int);
void pp_exit(worker_params_t *, int);

void swap(struct half_stream *hlf_a, struct half_stream *hlf_b);


// typedef struct
// {
//   volatile uint64_t cnt;                                      // 数据总量
//   volatile uint64_t cnt_parsed;                               // 解析数量
//   volatile uint64_t cnt_drop_by_http_trunk_failed;            // 因为trunk失败导致的丢弃
//   volatile uint64_t cnt_drop_by_http_trunk_too_large;         // 因为trunk过长导致的丢弃
//   volatile uint64_t cnt_drop_by_http_payload_too_large;       // 因为内容过长导致的丢弃
//   volatile uint64_t cnt_drop_by_http_parsed_failed;           // 因为解析失败导致的丢弃
//   volatile uint64_t cnt_drop_by_packet_lost;                  // 因为丢包导致的丢弃
//   volatile uint64_t cnt_drop_by_cache_too_large;              // 因为缓存过大导致的丢弃
//   volatile uint64_t cnt_drop_by_port_filter;                  // 因为端口过滤导致的丢弃
//   volatile uint64_t cnt_drop_by_urg;                          // urg数据不处理
//   volatile uint64_t cnt_drop_by_other;                        // 其他原因导致的丢弃

// } stats_app_cnt_t;

#ifdef __cplusplus
}
#endif

#endif /* __PROTO_PARSER_PP_H__ */
