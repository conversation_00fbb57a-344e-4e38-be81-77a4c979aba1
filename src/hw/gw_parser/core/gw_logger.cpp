/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <string.h>
#include <stdio.h>

#include <string>
#include <algorithm>

#include "gw_logger.h"
#include "utils.h"

#include "gw_common.h"
#include "worker_queue.h"
#include "gw_stats.h"
#include "display_stats_define.h"

typedef struct log_msg
{
  size_t mem_size; // 占用内存空间

  void *logger; // 日志对象实例

  uint64_t time_ms; // 日志输出时间戳

  size_t length; // 日志数据长度
  char *s;       // 日志数据指针

} log_msg_t;

static const char *get_loglevel_name(int level)
{
  switch (level)
  {
  case GWLOGGER_LEVEL_FATAL:
    return "FATAL";
    break;
  case GWLOGGER_LEVEL_TEST:
    return "TEST";
    break;
  case GWLOGGER_LEVEL_ERROR:
    return "ERROR";
    break;
  case GWLOGGER_LEVEL_WARN:
    return "WARN";
    break;
  case GWLOGGER_LEVEL_NOTICE:
    return "NOTICE";
    break;
  case GWLOGGER_LEVEL_INFO:
    return "INFO";
    break;
  case GWLOGGER_LEVEL_DEBUG:
    return "DEBUG";
    break;
  case GWLOGGER_LEVEL_TRACE:
    return "TRACE";
    break;

  default:
    break;
  }

  return "";
}

inline static int get_loglevel_int(const char *level)
{
  std::string s = level;
  std::transform(s.begin(), s.end(), s.begin(), ::toupper);

  if (unlikely(0))
  {
  }
  else if (s == "OFF")
  {
    return GWLOGGER_LEVEL_OFF;
  }
  else if (s == "TST" || s == "TEST")
  {
    return GWLOGGER_LEVEL_TEST;
  }
  else if (s == "FAT" || s == "FATAL")
  {
    return GWLOGGER_LEVEL_FATAL;
  }
  else if (s == "ERR" || s == "ERROR")
  {
    return GWLOGGER_LEVEL_ERROR;
  }
  else if (s == "WAR" || s == "WARN")
  {
    return GWLOGGER_LEVEL_WARN;
  }
  else if (s == "INF" || s == "INFO")
  {
    return GWLOGGER_LEVEL_INFO;
  }
  else if (s == "NOT" || s == "NOTICE")
  {
    return GWLOGGER_LEVEL_NOTICE;
  }
  else if (s == "DBG" || s == "DEBUG")
  {
    return GWLOGGER_LEVEL_DEBUG;
  }
  else if (s == "TRA" || s == "TRACE")
  {
    return GWLOGGER_LEVEL_TRACE;
  }
  else if (s == "ALL")
  {
    return GWLOGGER_LEVEL_ALL;
  }

  return GWLOGGER_LEVEL_UNKNOWN;
}

static inline void free_log_msg_inner(log_msg_t *p)
{
  delete[] p->s;
}

/**
 * CGwLogger implementation
 *
 * 日志输出类
 */

CGwLogger::CGwLogger(void) :  m_comm(NULL)
                            , m_quit_signal(0)
                            , m_level(GWLOGGER_LEVEL_WARN)
                            , m_mode(0)
                            , m_wq(NULL)
{
  SPIN_IMPL_INIT(m_msg_lock);
  spin_init(&(m_msg_lock), PTHREAD_PROCESS_SHARED);
}

CGwLogger::~CGwLogger(void)
{
  free_worker_queue(m_wq);
  log_flush();
  spin_destroy(&(m_msg_lock));
}

/**
 * 日志输出（异步到队列中）
 * @param int level
 * @param const char *format
 */
void CGwLogger::log(int level, const char *format, ...)
{
  char log_format[1024] = {0};
  va_list arg_ptr;

  if (likely(level < m_level))
  {
    // 当前日志输出等级低于日志输出信息等组不进行显示
    return;
  }

  snprintf(log_format, COUNTOF(log_format) - 1, "[%s] %s", get_loglevel_name(level), format);

  if (unlikely(!(m_mode == 0)))
  {
    va_start(arg_ptr, format);
    vfprintf(stderr, log_format, arg_ptr);
    va_end(arg_ptr);
    return ;
  }

  va_start(arg_ptr, format);

  log_msg_t *plm = new log_msg_t;
  char log_info[8192] = {0};
  vsnprintf(log_info, COUNTOF(log_info) - 1, log_format, arg_ptr);

  size_t length = strlen(log_info);

  plm->s = new char[length + 1];
  strcpy(plm->s, log_info);
  plm->length = length;
  plm->mem_size = sizeof(log_msg_t) + length;

  plm->time_ms = m_comm->gw_real_time_ms();
  plm->logger = this;

  if (likely(m_wq != NULL))
  {
    if (m_vec_log_msg.size() > 0)
    {
      log_msg_lock();
      // 缓存日志消息
      unsigned int k = m_vec_log_msg.size();

      for (size_t i = 0; i < m_vec_log_msg.size(); i++)
      {
        if (!m_wq->queue_put_data(m_vec_log_msg[i], m_vec_log_msg[i]->mem_size))
        {
          k = i;
          break;
        }
      }

      if (k != m_vec_log_msg.size())
      {
        m_vec_log_msg.erase(m_vec_log_msg.begin(), m_vec_log_msg.begin() + k);
        log_msg_unlock();
        goto next;
      }

      m_vec_log_msg.clear();
      log_msg_unlock();
    }

    if (m_wq->queue_put_data(plm, plm->mem_size))
    {
      goto end;
    }
  }

next:
  if (m_vec_log_msg.size() < 1000)
  {
    log_msg_lock();
    m_vec_log_msg.push_back(plm);
    log_msg_unlock();
    goto end;
  }
  else
  {
    log_flush();
    free_log_msg_inner(plm);
    delete plm;
  }

  // fprintf(stderr, "*****\n");
  va_start(arg_ptr, format);
  vfprintf(stderr, log_format, arg_ptr);

end:
  va_end(arg_ptr);

  return;
}

void CGwLogger::log_flush(void)
{
  log_msg_lock();
  for (size_t i = 0; i < m_vec_log_msg.size(); i++)
  {
    log_msg_t *plm = m_vec_log_msg[i];

    worker_routine_log_msg_inner(plm);

    free_log_msg_inner(plm);
    delete plm;
  }
  m_vec_log_msg.clear();
  log_msg_unlock();
}

/**
 * 日志输出（同步）
 * @param int level
 * @param const char *format
 */
void CGwLogger::log_sync(int level, const char *format, ...)
{
  char log_format[1024] = {0};
  va_list arg_ptr;

  if (level < m_level)
  {
    // 当前日志输出等级高于日志输出信息等组不进行显示
    return;
  }

  snprintf(log_format, COUNTOF(log_format) - 1, "[%s] %s", get_loglevel_name(level), format);

  va_start(arg_ptr, format);
  vfprintf(stderr, log_format, arg_ptr);
  va_end(arg_ptr);

  return;
}

void CGwLogger::init()
{
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;

  CWorkerQueue *pwq = m_comm->create_worker_queue();
  if (pwq != NULL)
  {
    pwq->set_gw_common(m_comm);
    pwq->set_watchdog(m_comm->get_watchdog());

    pwq->set_queue_num_and_bytes(1000, 100 * 1024LL * 1024LL);
    pwq->set_queue_name(LOG_QUEUE);
    pwq->set_queue_destroy_callback((q_destroy_func_t)free_log_msg);
    pwq->init();
    pwq->create_queue();
    pwq->create_thread(1, worker_routine_log_msg, this);

    if (m_comm->get_verbose())
    {
      //m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq->get_stats_task_data());
      m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq);
      m_comm->get_gw_stats()->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());
    }
  }

  m_wq = pwq;
}

void CGwLogger::fini()
{
  ASSERT(m_comm != NULL);

  CWorkerQueue *pwq = m_wq;
  m_wq = NULL;
  free_worker_queue(pwq);

  log_flush();
}

void CGwLogger::run()
{
  ASSERT(m_comm != NULL);

  if (m_wq != NULL)
  {
    m_wq->run();
  }
}

int CGwLogger::get_mode(void) const
{
  return m_mode;
}

void CGwLogger::set_mode(int mode)
{
  m_mode = mode;
}

int CGwLogger::get_level(void) const
{
  return m_level;
}

void CGwLogger::set_level(int level)
{
  if (level >= GWLOGGER_LEVEL_ALL && level <= GWLOGGER_LEVEL_OFF)
  {
    m_level = level;
  }
}

void CGwLogger::set_level_name(const char *level)
{
  set_level(get_loglevel_int(level));
}

/**
 * 触发退出信号时处理
 * @param void
 */
void CGwLogger::set_quit_signal(void)
{
  m_quit_signal = 1;
  if (m_wq != NULL)
  {
    m_wq->set_quit_signal();
  }
}

/**
 * 等待运行结束
 */
void CGwLogger::wait_for_stop(void)
{
  if (m_wq != NULL)
  {
    m_wq->wait_for_stop();
  }
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CGwLogger::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

void CGwLogger::free_worker_queue(CWorkerQueue *p)
{
  if (p == NULL)
  {
    return;
  }
  p->set_quit_signal();
  p->wait_for_stop();

  p->delete_queue();

  p->fini();

  if (m_comm != NULL)
  {
    m_comm->destory_worker_queue(p);
  }
}

void CGwLogger::free_log_msg(log_msg_t *p)
{
  size_t mem_size = p->mem_size;
  CGwLogger *pThis = (CGwLogger *)p->logger;
  ASSERT(pThis != NULL);
  CWorkerQueue *pwq = pThis->m_wq;
  // volatile uint64_t &stats_queue_memory_size = pwq->get_queue_mem_size();
  volatile uint64_t *p_stats_queue_memory_size = NULL;
  if (pwq != NULL)
  {
    p_stats_queue_memory_size = &pwq->get_queue_mem_size();
  }

  free_log_msg_inner(p);

  delete p;

  // __sync_fetch_and_sub(&stats_queue_memory_size, mem_size);
  if (p_stats_queue_memory_size != NULL)
  {
    SYNC_FETCH_AND_SUB_UINT64(p_stats_queue_memory_size, mem_size);
  }
}

int CGwLogger::worker_routine_log_msg(void *args_ptr, worker_routine_param_t *pwrp, void *p)
{
  CGwLogger *pThis = (CGwLogger *)args_ptr;
  ASSERT(pThis != NULL);

  switch (pwrp->step)
  {
  case WQ_WR_DATA:
    return pThis->worker_routine_log_msg_inner(p);
    break;

  default:
    break;
  }

  return 0;
}

int CGwLogger::worker_routine_log_msg_inner(void *p)
{
  log_msg_t *plm = (log_msg_t *)p;
  time_t now_tm = plm->time_ms;
  struct tm tm_gm_tm = {0};
  now_tm  /= 1000;
  now_tm += 8 * 3600;
  char buf[256] = {0};
  strftime(buf, sizeof(buf) - 1, "[%Y-%m-%d %H:%M:%S] ", gmtime_r(&now_tm, &tm_gm_tm));

  fputs(buf, stderr);
  fputs(plm->s, stderr);

  return 0;
}

void CGwLogger::log_msg_lock(void)
{
  spin_lock(&m_msg_lock);
}

void CGwLogger::log_msg_unlock(void)
{
  spin_unlock(&m_msg_lock);
}
