/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <errno.h>
#include <unistd.h>
#include <sys/select.h>
#include <string.h>

#include "interactive_service.h"
#include "gw_i_source.h"
#include "gw_i_parser.h"
#include "gw_config.h"
#include "utils.h"
#include "gw_common.h"
#include "tcp_parser.h"
#include "gw_logger.h"
#include "gw_stats.h"
#include "get_file_type.h"
#include "cpp_utils.h"

const static char* g_p_help = "help";
const static char* g_p_source_file = "source_file";
const static char* g_p_source_dpdk = "source_dpdk";
const static char* g_p_source_nic = "source_nic";

const static char* g_p_parser_http = "parser_http";
const static char* g_p_parser_ftp = "parser_ftp";
const static char* g_p_parser_mail = "parser_mail";

const static char* g_p_forward_info = "forward_info";
const static char* g_p_drop_info = "drop_info";
const static char* g_p_tcp_stream_info = "tcp_stream_info";
const static char* g_p_queue_info = "queue_info";
const static char* g_p_task_info = "task_info";
const static char* g_p_qps_info = "qps_info";
const static char* g_p_gene_file_info = "gene_file_info";
const static char* g_p_mem_info = "queue_mem_info";

// const static char* g_p_start_risk = "start_risk";
// const static char* g_p_stop_risk = "stop_risk";
// const static char* g_p_cache_clean = "cache_clean";
const static char* g_p_gwhw_status = "gwhw_status";
// const static char* g_p_task_status = "task_status";
const static char* g_p_drop_http_file_event = "drop_http_file_event";
const static char* g_p_save_http_file_event = "save_http_file_event";
const static char* g_p_add_control_strategy = "add_control_strategy";
const static char* g_p_del_control_strategy = "del_control_strategy";

/* error info */
const char* file_module_error = "file source module not load\n";
const char* dpdk_module_error = "dpdk source module not load\n";
const char* nic_module_error = "nic source module not load\n";

const char* http_module_error = "http parser module not load\n";
const char* ftp_module_error = "ftp parser module not load\n";
const char* mail_module_error = "mail parser module not load\n";

const char* forward_info_error = "get forward info error\n";
const char* drop_info_error = "get drop info error\n";
const char* tcp_stream_info_error = "get tcp stream info error\n";
const char* queue_info_error = "get queue info error\n";
const char* task_info_error = "get task info error\n";
const char* qps_info_error = "get qps info error\n";
const char* queue_mem_info_error = "get queue mem info error\n";
const char* cache_clean_done = "cache clean done\n";

const char* g_p_risk_return_template = "{\n   \"success\":\"%s\",\n   \"errCode\":%d,\n   \"msg\":\"%s\"\n}\n";
const char* g_p_risk_return_template1 = "{\n   \"success\":\"%s\",\n   \"errCode\":%d,\n   \"msg\":\"%d\"\n}\n";

struct err_info
{
  const char *success_flag;
  int errnum;
  const char* err_str;
}; 

extern "C" bool is_hit_control_strategy(const char* src_ip, const char* dst_ip);

struct control_strategy_info_t
{
  std::string control_name;
  std::string control_type;
  std::vector<std::string> interrupt_ip;
};

std::map<std::string, control_strategy_info_t> control_strategy;

const err_info err_data[] = 
{
      {"true", 200, NULL},
      {"false", -1, "dpdk task already start"},
      {"false", -2, "agent task already start"},
      {"false", -3, "history task already start"},
      {"false", -4, "unknow target type"},
      {"false", -5, "dpdk source module not load"},
      {"false", -6, "file source module not load"},
      {"false", -7, "task not start"},
      {"false", -8, "task is running"},
      {"false", -9, "missing operand"},
      {"false", -10, "pfring task already start"},
      {"false", -11, "pfring source module not load"},
      {"false", -12, "local target type is error"},
      {"false", -13, "history path is error"},
  };

const char* invaild_command = "invaild command\n";

class CInteractiveServiceLogHelp
{
  CGwCommon *m_comm;
  const char* m_command;
  const char* m_rsp_log;
  static const char* log_format;
public:
  CInteractiveServiceLogHelp();
  CInteractiveServiceLogHelp(CGwCommon *_comm, const char* m_command, const char* m_rsp_log);
  CInteractiveServiceLogHelp& operator= (CInteractiveServiceLogHelp& o) = delete;
  CInteractiveServiceLogHelp& operator= (CInteractiveServiceLogHelp&& o);
  ~CInteractiveServiceLogHelp();
};

const char* CInteractiveServiceLogHelp::log_format = "[Interactive Service Log] command: %s\nrsp_log: %s";

CInteractiveServiceLogHelp::CInteractiveServiceLogHelp() : m_comm(NULL)
                                                          , m_command(NULL)
                                                          , m_rsp_log(NULL)
{
}

CInteractiveServiceLogHelp::CInteractiveServiceLogHelp(CGwCommon *_comm, const char* _command, const char* _rsp_log) : m_comm(_comm)
                                                                                                                      , m_command(_command)
                                                                                                                      , m_rsp_log(_rsp_log)
{
}

CInteractiveServiceLogHelp& CInteractiveServiceLogHelp::operator= (CInteractiveServiceLogHelp&& o) 
{
  m_comm    = o.m_comm,    o.m_comm    = NULL;
  m_command = o.m_command, o.m_command = NULL;
  m_rsp_log = o.m_rsp_log, o.m_rsp_log = NULL;
  return *this;
}

CInteractiveServiceLogHelp::~CInteractiveServiceLogHelp() 
{
  if (m_comm && m_command && m_rsp_log) 
  {
    GWLOG_INFO(m_comm, "command: %s\nrsp_log: %s", m_command, m_rsp_log);
  }
  else if (m_command && m_rsp_log) 
  {
    fprintf(stderr, log_format, m_command, m_rsp_log);
  }
}


/**
 * CInteractiveService implementation
 *
 * 交互式服务，封装内部状态与配置。支持Telnet协议或Web服务。
 */

CInteractiveService::CInteractiveService(void) : m_comm(nullptr)
                                               , m_tcp_parser(nullptr)
                                               , m_quit_signal(0)
                                               , m_u16_port(23)  /* 默认使用telnet服务默认端口号 */
                                               , m_recv_command_pthread_stats(0)
                                               , m_timeout_sec(30)
                                               , m_target_type(0)
                                               , m_save_history(0)
                                               , m_command_log_mode(0)
                                               , m_gwhw_mode(0)
                                               , m_source_file(nullptr)
                                               , m_source_dpdk(nullptr)
                                               , m_source_nic(nullptr)
                                               , m_http_parser(nullptr)
                                               , m_ftp_parser(nullptr)
                                               , m_mail_parser(nullptr)
                                               
{
}

CInteractiveService::~CInteractiveService(void)
{
}

void CInteractiveService::init()
{
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;

  load_conf(NULL);
}

void CInteractiveService::fini()
{
  ASSERT(m_comm != NULL);
}

void CInteractiveService::run()
{
  ASSERT(m_comm != NULL);

  int i_ret = pthread_create(&m_pthread_recv_command, NULL, (void *(*)(void *))recv_command_run, this);
  if (i_ret != 0)
  {
    GWLOG_ERROR(m_comm, "create recv command thread failed: %s\n", strerror(i_ret));
  }
  else
  {
    m_recv_command_pthread_stats = 1;
    GWLOG_INFO(m_comm, "create recv command thread successfully\n");
  }
}

/**
 * 触发退出信号时处理
 */
void CInteractiveService::set_quit_signal(void)
{
  m_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CInteractiveService::wait_for_stop(void)
{
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CInteractiveService::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

void CInteractiveService::set_tcp_parser(CTcpParser *tcpparser)
{
  assert(tcpparser != nullptr);

  m_tcp_parser = tcpparser; 
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CInteractiveService::load_conf(const char *)
{
    /* 交互式端口号 */
  CGwConfig *pgwc = m_comm->get_gw_config();
  m_u16_port = pgwc->read_conf_int("parser", "interacting_port", m_u16_port);
  m_command_log_mode = pgwc->read_conf_int("parser", "inter_active_server_command_log_mode", m_command_log_mode);
  m_gwhw_mode = pgwc->read_conf_int("parser", "gwhw_mode", m_gwhw_mode);
  return true;
}

/* 设置source 对象 */
void CInteractiveService::set_source_obj(CSource *csource)
{
  if (csource == nullptr)
  {
    return;
  }

  const std::string str_source_name = csource->get_name();
  if (str_source_name.find("CDpdkSource") != std::string::npos)
  {
    m_source_dpdk = csource;
  }
  else if (str_source_name.find("CFileSource") != std::string::npos)
  {
    m_source_file = csource;
  }
  else if (str_source_name.find("CNicSource") != std::string::npos)
  {
    m_source_nic = csource;
  }
  else if (str_source_name.find("CPcapSource") != std::string::npos)
  {
    m_source_nic = csource;
  }

  return;
}

void CInteractiveService::set_parser_obj(CParser *cparser)
{
  if (cparser == nullptr)
  {
    return;
  }

  const std::string str_parser_name = cparser->get_name();
  if (str_parser_name.find("CHttpParser") != std::string::npos)
  {
    m_http_parser = cparser;
  }
  else if (str_parser_name.find("CFtpParser") != std::string::npos)
  {
    m_ftp_parser = cparser;
  }
  else if (str_parser_name.find("CMailParser") != std::string::npos)
  {
    m_mail_parser = cparser;
  }

  return;
}

int CInteractiveService::recv_command_run(void *arg)
{
  CInteractiveService *pThis = (CInteractiveService *)arg;
  ASSERT(pThis != NULL);

  pThis->recv_command();
  return 0;
}

int CInteractiveService::recv_command()
{
  int fd = 0;
  int i_ret = 0;
  struct sockaddr_in addr;
  if (m_gwhw_mode == 0)
  {
    // change_to_default();
    GWLOG_INFO(m_comm, "mode: default\n");
  }
  else
  {
    GWLOG_INFO(m_comm, "mode: task\n");
  }

  fd = socket(AF_INET, SOCK_STREAM, 0);
  if (fd == -1)
  {
    GWLOG_ERROR(m_comm, "socket error: %s\n", strerror(errno));
    m_recv_command_pthread_stats = 0;
    return i_ret;
  }

  int optval = 1;
  setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval));

  addr.sin_family = AF_INET;
  addr.sin_addr.s_addr = htonl(INADDR_LOOPBACK);
  addr.sin_port = htons(m_u16_port);

  i_ret = bind(fd, (const struct sockaddr*)&addr, sizeof(addr));
  if (i_ret == -1)
  {
    GWLOG_ERROR(m_comm, "bind error: %s\n", strerror(errno));
    m_recv_command_pthread_stats = 0;
    return i_ret;
  }

  i_ret = listen(fd, 10);
  if (i_ret == -1)
  {
    GWLOG_ERROR(m_comm, "listen error: %s\n", strerror(errno));
    m_recv_command_pthread_stats = 0;
    return i_ret;
  }

  int conn_fd = -1; /* 连接fd */
  int client_set[FD_SETSIZE];
  int maxi = 0;
  for (int i = 0; i < FD_SETSIZE; ++i)
  {
    client_set[i] = -1;
  }

  struct sockaddr client_addr;
  int client_addrlen = sizeof(client_addr);

  int ready_fd = 0;
  int maxfd = fd;
  struct timeval timeout;
  fd_set allset;
  fd_set rset;
  FD_ZERO(&rset);
  FD_ZERO(&allset);
  FD_SET(fd, &allset);

  while (!m_quit_signal)
  {
    rset = allset;
    
    timeout.tv_sec = m_timeout_sec;
    timeout.tv_usec = 0;

    ready_fd = select(maxfd + 1, &rset, NULL, NULL, &timeout);
    if (ready_fd == -1) /* 出现错误 */
    {
      if (errno == EINTR) /* A signal was delivered before the time limit expired and before any of the selected events occurred */
      {
        continue;
      }
      else
      {
        GWLOG_ERROR(m_comm, "select error: %s\n", strerror(errno));
        break;
      }
    }

    if (ready_fd == 0) /* 超时 */
    {
      continue;
    }

    /* new connect */
    if (FD_ISSET(fd, &rset))
    {
      conn_fd = accept(fd, &client_addr, (socklen_t*)&client_addrlen);
      if (conn_fd == -1)
      {
        GWLOG_ERROR(m_comm, "accept failed: %s\n", strerror(errno));
        break;
      }

      for (int i = 0; i < FD_SETSIZE; ++i)
      {
        if (client_set[i] < 0)
        {
          client_set[i] = conn_fd;
          if (i > maxi)
          {
            maxi = i;
          }

          break;
        }
      }

      FD_SET(conn_fd, &allset);
      if (conn_fd > maxfd)
      {
        maxfd = conn_fd;
      }
      if (--ready_fd <= 0)
      {
        continue;
      }
    }
    
    for (int i = 0; i <= maxi; i++)
    {
      conn_fd = client_set[i];
      if (conn_fd == -1)
      {
        continue;
      }

      if (!FD_ISSET(conn_fd, &rset))
      {
        continue;
      }
      
      //dup2(conn_fd, STDERR_FILENO);
      //dup2(conn_fd, STDOUT_FILENO);
      char recvbuf[1024] = {0};
      int ret = recv(conn_fd, recvbuf, sizeof(recvbuf) - 1, 0);
      if (ret < 0) /* error */
      {
        GWLOG_ERROR(m_comm, "recv data failed: %s\n", strerror(errno));
        continue;
      }
      else if (ret == 0) /* close */
      {
        FD_CLR(conn_fd, &allset);
        client_set[i] = -1;
        close(conn_fd);
      }
      else
      {
        char log_buf[LOG_BUF_LEN] = {0};
        if (recvbuf[ret - 1] == '\0')
        {
          if (ret > 2 && recvbuf[ret - 2] == '\n')
          {
            recvbuf[ret - 2] = '\0';
          }
          parser_command(recvbuf, log_buf, LOG_BUF_LEN);
        }
        else
        {
          if (ret > 2 && (recvbuf[ret - 1] == '\n' && recvbuf[ret - 2] == '\r'))
          {
            recvbuf[ret - 2] = '\0';
          }
          else
          {
            recvbuf[ret] = '\0';
          }
          parser_command(recvbuf, log_buf, LOG_BUF_LEN);
        }

        send(conn_fd, log_buf, strlen(log_buf), 0);
      }
    }
  }

  close(fd);

  return 0;
}

void CInteractiveService::parser_command(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  memset(log_buf, 0, log_buf_len);
  CInteractiveServiceLogHelp loghelp;
  if ( m_command_log_mode )
  {
    loghelp = CInteractiveServiceLogHelp( (m_command_log_mode == 1 ? NULL : m_comm), 
                                          command_buf, log_buf);
  }
  
  if (get_help_info(command_buf, log_buf, log_buf_len))
  {
    return;
  }
  /* source 日志信息 */
  if (get_source_log_buf(command_buf, log_buf, log_buf_len))
  {
    return;
  }

  /* parser 日志信息 */
  if (get_parser_log_buf(command_buf, log_buf, log_buf_len))
  {
    return;
  }

  /* tcp 日志信息 */
  if (get_tcp_info(command_buf, log_buf, log_buf_len))
  {
    return ;
  }

  if (get_stats_info(command_buf, log_buf, log_buf_len))
  {
    return;
  }

  if (get_gene_file_info(command_buf, log_buf, log_buf_len))
  {
    return;
  }

  if (drop_http_file_event(command_buf, log_buf, log_buf_len))
  {
    return;
  }

  if (save_http_file_event(command_buf, log_buf, log_buf_len))
  {
    return;
  }

  if (add_control_strategy(command_buf, log_buf, log_buf_len))
  {
    return;
  }

  if (del_control_strategy(command_buf, log_buf, log_buf_len))
  {
    return;
  }

  if (1 == m_gwhw_mode) 
  {
    // if (do_cache_clean(command_buf, log_buf, log_buf_len)) 
    // {
    //   return;
    // }

    // if (start_risk_parser(command_buf, log_buf, log_buf_len))
    // {
    //   return;
    // }

    // if (stop_risk_parser(command_buf, log_buf, log_buf_len))
    // {
    //   return;
    // }

    // if (get_task_status(command_buf, log_buf, log_buf_len))
    // {
    //   return;
    // }

    // if (get_gwhw_status(command_buf, log_buf, log_buf_len))
    // {
    //   return;
    // }
  }

  memcpy(log_buf, invaild_command, MIN(log_buf_len - 1, strlen(invaild_command)));

}

int CInteractiveService::get_help_info(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  size_t command_len = strlen(command_buf);
  if (command_len == strlen(g_p_help) && strcmp(command_buf, g_p_help) == 0)
  {
    sprintf (log_buf + strlen(log_buf), "usage:\n");
    sprintf (log_buf + strlen(log_buf), "source_file:  get agent source log info\n");
    sprintf (log_buf + strlen(log_buf), "source_dpdk:  get dpdk source log info\n");
    sprintf (log_buf + strlen(log_buf), "source_nic:   get nic source log info\n");
    sprintf (log_buf + strlen(log_buf), "parser_http:  get http parser log info\n");
    sprintf (log_buf + strlen(log_buf), "parser_ftp:   get ftp parser log info\n");
    sprintf (log_buf + strlen(log_buf), "parser_mail:  get mail parser log info\n");
    sprintf (log_buf + strlen(log_buf), "forward_info: get forward log info\n");
    sprintf (log_buf + strlen(log_buf), "drop_info:    get drop log info\n");
    sprintf (log_buf + strlen(log_buf), "tcp_stream_info: get tcp stream log info\n");
    sprintf (log_buf + strlen(log_buf), "queue_info:   get queue log info\n");
    sprintf (log_buf + strlen(log_buf), "task_info:    get task log info\n");
    sprintf (log_buf + strlen(log_buf), "qps_info:     get qps log info\n");
    sprintf (log_buf + strlen(log_buf), "gene_file_info: get gene file info\n");
    sprintf (log_buf + strlen(log_buf), "queue_mem_info: get queue menory info\n");
    sprintf (log_buf + strlen(log_buf), "cache_clean:  clean the cache (ip-queue, tcp-queue, parser-queue, upload-queue)");
    return 1;
  }

  return 0; 
}

int CInteractiveService::get_source_log_buf(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  size_t command_len = strlen(command_buf);
  /* 获取 agent 模式信息 */
  if (command_len == strlen(g_p_source_file) && strcmp(command_buf, g_p_source_file) == 0)
  {
    if (m_source_file == nullptr)
    {
      memcpy(log_buf, file_module_error, MIN(log_buf_len - 1, strlen(file_module_error)));
    }
    else
    {
      m_source_file->get_log_buf(log_buf, log_buf_len);
    }

    return 1;
  } /* 获取dpdk模式信息 */
  else if (command_len == strlen(g_p_source_dpdk) && strcmp(command_buf, g_p_source_dpdk) == 0)
  {
    if (m_source_dpdk == nullptr)
    {
      memcpy(log_buf, dpdk_module_error, MIN(log_buf_len - 1, strlen(dpdk_module_error)));
    }
    else
    {
      m_source_dpdk->get_log_buf(log_buf, log_buf_len);
    }

    return 1;
  } /* 获取 网卡模式信息 */
  else if (command_len == strlen(g_p_source_nic) && strcmp(command_buf, g_p_source_nic) == 0)
  {
    if (m_source_nic == nullptr)
    {
      memcpy(log_buf, nic_module_error, MIN(log_buf_len - 1, strlen(nic_module_error)));
    }
    else
    {
      m_source_nic->get_log_buf(log_buf, log_buf_len);
    }

    return 1;
  }

  return 0;
}

int CInteractiveService::get_parser_log_buf(const char *command_buf, char *log_buf, size_t log_buf_len)
{
  size_t command_len = strlen(command_buf);
  if (command_len == strlen(g_p_parser_http) && strcmp(command_buf, g_p_parser_http) == 0)
  {
    if (m_http_parser == nullptr)
    {
      memcpy (log_buf, http_module_error, MIN(log_buf_len - 1, strlen(http_module_error)));
    }
    else
    {
      m_http_parser->get_log_buf(log_buf, log_buf_len);
    }
    
    return 1;
  }
  else if (command_len == strlen(g_p_parser_ftp) && strcmp(command_buf, g_p_parser_ftp) == 0)
  {
    if (m_ftp_parser == nullptr)
    {
      memcpy(log_buf, ftp_module_error, MIN(log_buf_len - 1, strlen(ftp_module_error)));
    }
    else
    {
      m_ftp_parser->get_log_buf(log_buf, log_buf_len);
    }
    
    return 1;
  }
  else if (command_len == strlen(g_p_parser_mail) && strcmp(command_buf, g_p_parser_mail) == 0)
  {
    if (m_mail_parser == nullptr)
    {
      memcpy(log_buf, mail_module_error, MIN(log_buf_len - 1, strlen(mail_module_error)));
    }
    else
    {
      m_mail_parser->get_log_buf(log_buf, log_buf_len);
    }

    return 1;
  }

  return 0;
}

int CInteractiveService::get_tcp_info(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  size_t command_len = strlen(command_buf);
  /* 转发信息 */
  if (command_len == strlen(g_p_forward_info) && strcmp(command_buf, g_p_forward_info) == 0)
  {
    if (m_tcp_parser == nullptr)
    {
      memcpy(log_buf, forward_info_error, MIN(log_buf_len - 1, strlen(forward_info_error)));
    }
    else
    {
      m_tcp_parser->get_forward_info(log_buf, log_buf_len);
    }

    return 1;
  }
  else if (command_len == strlen(g_p_drop_info) && strcmp(command_buf, g_p_drop_info) == 0)
  {
    if (m_tcp_parser == nullptr)
    {
      memcpy(log_buf, drop_info_error, MIN(log_buf_len - 1, strlen(drop_info_error)));
    }
    else
    {
      m_tcp_parser->get_drop_info(log_buf, log_buf_len);
    }
    return 1;
  }
  else if (command_len == strlen(g_p_tcp_stream_info) && strcmp(command_buf, g_p_tcp_stream_info) == 0)
  {
    if (m_tcp_parser == nullptr)
    {
      memcpy(log_buf, g_p_tcp_stream_info, MIN(log_buf_len - 1, strlen(g_p_tcp_stream_info)));
    }
    else
    {
      m_tcp_parser->get_tcp_stream_info(log_buf, log_buf_len);
    }

    return 1;
  }

  return 0;
}

int CInteractiveService::get_stats_info(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  size_t command_len = strlen(command_buf);
  CGwStats *p_stats = m_comm->get_gw_stats();

  if (command_len == strlen(g_p_queue_info) && strcmp(command_buf, g_p_queue_info) == 0)
  {
    if (p_stats == nullptr)
    {
      memcpy(log_buf, queue_info_error, MIN(log_buf_len - 1, strlen(queue_info_error)));  
    }
    else
    {
      p_stats->get_queue_info(log_buf, log_buf_len);
    }

    return 1;
  }
  else if (command_len == strlen(g_p_task_info) && strcmp(command_buf, g_p_task_info) == 0)
  {
    if (p_stats == nullptr)
    {
      memcpy(log_buf, task_info_error, MIN(log_buf_len - 1, strlen(task_info_error)));  
    }
    else
    {
      p_stats->get_task_info(log_buf, log_buf_len);
    }
    
    return 1;
  }
  else if (command_len == strlen(g_p_qps_info) && strcmp(command_buf, g_p_qps_info) == 0)
  {
    if (p_stats == nullptr)
    {
      memcpy(log_buf, qps_info_error, MIN(log_buf_len - 1, strlen(qps_info_error)));  
    }
    else
    {
      p_stats->get_qps_info(log_buf, log_buf_len);
    }

    return 1;
  }
  else if (command_len == strlen(g_p_mem_info) && strcmp(command_buf, g_p_mem_info) == 0)
  {
    if (p_stats == nullptr)
    {
      memcpy(log_buf, queue_mem_info_error, MIN(log_buf_len - 1, strlen(queue_mem_info_error))); 
    }
    else
    {
      p_stats->get_mem_info(log_buf, log_buf_len);
    }

    return 1;
  }

  return 0;
}

int CInteractiveService::get_gene_file_info(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  size_t command_len = strlen(command_buf);
  if (command_len == strlen(g_p_gene_file_info) && strcmp(command_buf, g_p_gene_file_info) == 0)
  {
    get_gene_file_log(log_buf, log_buf_len);
    return 1;
  }

  return 0; 
}


int CInteractiveService::get_gwhw_status(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  int i_ret = 0;
  if (strlen(command_buf) == strlen(g_p_gwhw_status) && strcmp(command_buf, g_p_gwhw_status) == 0)
  {

    i_ret = m_comm->gwhw_parser_status();

    sprintf(log_buf, g_p_risk_return_template1, err_data[0].success_flag, err_data[0].errnum, i_ret);

    return 1;
  }
  return 0;
}

int CInteractiveService::drop_http_file_event(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  if (strcmp(command_buf, g_p_drop_http_file_event) == 0)
  {
    GWLOG_INFO(m_comm, "drop http file event\n");
    m_http_parser->set_drop_file_event(true);
    sprintf(log_buf, g_p_risk_return_template, err_data[0].success_flag, err_data[0].errnum, err_data[0].err_str);
    return 1;
  }

  return 0;
}

int CInteractiveService::save_http_file_event(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  if (strcmp(command_buf, g_p_save_http_file_event) == 0)
  {
    GWLOG_INFO(m_comm, "save http file event\n");
    m_http_parser->set_drop_file_event(false);
    sprintf(log_buf, g_p_risk_return_template, err_data[0].success_flag, err_data[0].errnum, err_data[0].err_str);
    return 1;
  }

  return 0;
}

int CInteractiveService::add_control_strategy(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  std::vector<std::string> result;
  split_string(command_buf, "|", result, false);

  if (result.empty() || result[0].size() != strlen(g_p_add_control_strategy) || strcmp(g_p_add_control_strategy, result[0].c_str()) != 0)
  {
    sprintf(log_buf, g_p_risk_return_template, err_data[9].success_flag, err_data[9].errnum, err_data[9].err_str);
    return 0;
  }

  GWLOG_INFO(m_comm, "command=%s\n", command_buf);

  //"add_control_strategy|" + content["id"] + content["controlName"] + "|" + content["controlType"] + "|" + json.dumps(content["controlContents"])
  if (result.size() < 5)
  {
    sprintf(log_buf, g_p_risk_return_template, "false", -1, "parameter too less");
    return 1;
  }

  GWLOG_INFO(m_comm, "cmd=%s\n", result[0].c_str());
  GWLOG_INFO(m_comm, "id=%s\n", result[1].c_str());
  GWLOG_INFO(m_comm, "control name=%s\n", result[2].c_str());
  GWLOG_INFO(m_comm, "control type=%s\n", result[3].c_str());
  GWLOG_INFO(m_comm, "control contents=%s\n", result[4].c_str());

  std::map<std::string, control_strategy_info_t>::iterator it = control_strategy.find(result[1]);

  if (control_strategy.find(result[1]) == control_strategy.end())
  {
    control_strategy_info_t control_strategy_info;
    control_strategy_info.control_name = result[2];
    control_strategy_info.control_type = result[3];

    it = control_strategy.insert({result[1], control_strategy_info}).first;
  }

  CGwConfig *p_config = new CGwConfig();
  p_config->set_gw_common(m_comm);
  p_config->load_string(result[4].c_str());
  int array_size = 0;
  array_size = p_config->get_array_size(NULL, NULL, NULL);

  const cJSON *p_json = p_config->get_json_conf();
  for (int i = 0; i < array_size; i++)
  {
    cJSON *json_array = cJSON_GetArrayItem(p_json, i);
    // char *p_data = cJSON_Print(json_array);
    char* p_data = json_array->valuestring;

    GWLOG_INFO(m_comm, "control content data=%s\n", p_data);
    it->second.interrupt_ip.push_back(p_data);
    cJSON_free_safe(p_data);
  }

  sprintf(log_buf, g_p_risk_return_template, err_data[0].success_flag, err_data[0].errnum, err_data[0].err_str);
  return 1;
}

int CInteractiveService::del_control_strategy(const char* command_buf, char *log_buf, size_t log_buf_len)
{
  std::vector<std::string> result;
  split_string(command_buf, "|", result, false);

  if (result.empty() || result[0].size() != strlen(g_p_del_control_strategy) || strcmp(g_p_del_control_strategy, result[0].c_str()) != 0)
  {
    sprintf(log_buf, g_p_risk_return_template, err_data[9].success_flag, err_data[9].errnum, err_data[9].err_str);
    return 0;
  }

  GWLOG_INFO(m_comm, "command=%s\n", command_buf);

  //"del_control_strategy|" + content["id"]
  if (result.size() < 2)
  {
    sprintf(log_buf, g_p_risk_return_template, "false", -1, "parameter too less");
    return 1;
  }

  GWLOG_INFO(m_comm, "cmd=%s\n", result[0].c_str());
  GWLOG_INFO(m_comm, "id=%s\n", result[1].c_str());

  if (control_strategy.find(result[1]) == control_strategy.end())
  {
    sprintf(log_buf, g_p_risk_return_template, "false", -1, "control strategy not exist");
    return 1;
  }

  control_strategy.erase(result[1]);

  sprintf(log_buf, g_p_risk_return_template, err_data[0].success_flag, err_data[0].errnum, err_data[0].err_str);
  return 1;
}

bool is_hit_control_strategy(const char* src_ip, const char* dst_ip)
{
  for (const std::pair<std::string, control_strategy_info_t>& control_strategy_it : control_strategy)
  {
    for (const std::string& ip_it : control_strategy_it.second.interrupt_ip)
    {
      if (strcmp(ip_it.c_str(), src_ip) == 0 || strcmp(ip_it.c_str(), dst_ip) == 0)
      {
        fprintf(stderr, "%s hit control strategy\n", ip_it.c_str());
        return true;
      }
    }
  }

  return false;
}