#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "accoutfilter_rule.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "cJSON.h"
#include "gw_config.h"
#include "tcp_parser.h"
#include "utils.h"

CAccoutfilterRule::CAccoutfilterRule() : m_comm(NULL)
                                       , m_tcpparser(NULL)
                                       , m_accoutfilter_info(NULL)
{

}

CAccoutfilterRule::~CAccoutfilterRule()
{
    fini();
}

/**
 * 命中过滤规则。
 * @param unsigned int data
 */
int CAccoutfilterRule::hit(unsigned data)
{
    return 0;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CAccoutfilterRule::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);
    m_comm = comm;
}

 /**
 *  设置TcpParser对象实例 
 *  @paramount CTcpParser *tcpparser
 */
void CAccoutfilterRule::set_tcp_parser(CTcpParser *tcpparser)
{
    ASSERT(tcpparser != NULL);
    m_tcpparser = tcpparser;
}

void CAccoutfilterRule::init()
{
    ASSERT(m_comm != NULL);
    ASSERT(m_tcpparser != NULL);
}

void CAccoutfilterRule::fini()
{
    free_accout_info(m_accoutfilter_info);
    m_accoutfilter_info = NULL;
}

void CAccoutfilterRule::set_accout_filter(const char* p_accout_filename)
{
    ASSERT(m_comm != NULL);
    const char *p_conf_name = NULL;
    CGwConfig *pgwc = m_comm->get_gw_config();
    ASSERT(pgwc != NULL);
    p_conf_name = pgwc->get_config_path();

    pgwc->set_config_path(p_accout_filename);
    if (!pgwc->load())
    {
      GWLOG_ERROR(m_comm, " %s load error!\n", p_accout_filename);
    }

    const cJSON *cjson_rule_conf = NULL;
    cjson_rule_conf = pgwc->get_json_conf();

    m_accoutfilter_info = read_accout_rule_info(cjson_rule_conf);

    pgwc->set_config_path(p_conf_name);
    pgwc->load();
}

rule_user_info_t* CAccoutfilterRule::get_accout_info() const
{
    return m_accoutfilter_info;
}

rule_user_info_t* CAccoutfilterRule::read_accout_rule_info(const cJSON *cjson_rule_conf)
{
    user_info_rule_t *p = NULL;
    rule_user_info_t *rule_user_info_ptr = NULL;
    int i;
    int k;

    if (!cJSON_IsObject(cjson_rule_conf))
    {
        return NULL;
    }

    rule_user_info_ptr = (rule_user_info_t*)malloc(sizeof(rule_user_info_t));
    if (rule_user_info_ptr == NULL)
    {
        return NULL;
    }
    memset(rule_user_info_ptr, 0, sizeof(rule_user_info_t));

    for (i = 0; i < cJSON_GetArraySize(cjson_rule_conf); ++i)
    {
        cJSON *rule_conf_host = cJSON_GetArrayItem(cjson_rule_conf, i);
        for (k = 0; k < cJSON_GetArraySize(rule_conf_host); ++k)
        {
            cJSON *rule_conf_item = cJSON_GetArrayItem(rule_conf_host, k);
            if (cJSON_IsObject(rule_conf_item))
            {
                cJSON *r_url = cJSON_GetObjectItem(rule_conf_item, "r_url");
                if (cJSON_IsString(r_url))
                {
                //printf("r_url:%s\n", r_url->valuestring);
                ++rule_user_info_ptr->rule_user_info_num;
                }
            }
        }
    }

    p = rule_user_info_ptr->rule_user_info = (user_info_rule_t*)malloc(sizeof(user_info_rule_t) * rule_user_info_ptr->rule_user_info_num);
    if (rule_user_info_ptr->rule_user_info == NULL)
    {
        free(rule_user_info_ptr);
        return NULL;
    }

    for (i = 0; i < cJSON_GetArraySize(cjson_rule_conf); ++i)
    {
        cJSON *rule_conf_host = cJSON_GetArrayItem(cjson_rule_conf, i);
        for (k = 0; k < cJSON_GetArraySize(rule_conf_host); ++k)
            {
            cJSON *rule_conf_item = cJSON_GetArrayItem(rule_conf_host, k);
            if (cJSON_IsObject(rule_conf_item))
            {
                cJSON *r_url = cJSON_GetObjectItem(rule_conf_item, "r_url");
                cJSON *r_keys = cJSON_GetObjectItem(rule_conf_item, "r_keys");
                cJSON *r_keys_name = cJSON_GetObjectItem(rule_conf_item, "r_keys_name");
                cJSON *r_keys_uid = cJSON_GetObjectItem(rule_conf_item, "r_keys_uid");

                if (cJSON_IsString(r_url))
                {
                    // // 加载 用户访问接口规则配置文件
                    p->url = rule_init_url_strings(r_url);
                    p->keys = rule_init_conf_strings(r_keys);
                    p->keys_name = rule_init_conf_strings(r_keys_name);
                    p->keys_uid = rule_init_conf_strings(r_keys_uid);
                    p++;
                    //printf("r_url:%s\n", r_url->valuestring);
                }
            }
        }
    }

    return rule_user_info_ptr;
}

char *CAccoutfilterRule::rule_init_url_strings(cJSON *r)
{
    char *p = NULL;
    if (!cJSON_IsString(r))
    {
        return NULL;
    }

    p = (char*)malloc(strlen(r->valuestring) + 1);
    if (p == NULL)
    {
        GWLOG_ERROR(m_comm, "no mem for init url strings\n");
        return NULL;
    }
    memset(p, 0, strlen(r->valuestring) + 1);

    strcpy(p, r->valuestring);

    return p;
}

char *CAccoutfilterRule::rule_init_conf_strings(cJSON *r)
{
    int i;
    int size;
    int str_len = 0;
    cJSON *rr;
    char *p;
    char *pp;

    if (!cJSON_IsArray(r))
    {
        return NULL;
    }

    size = cJSON_GetArraySize(r);
    for (i = 0; i < size; ++i)
    {
        rr = cJSON_GetArrayItem(r, i);
        if (cJSON_IsString(rr))
        {
            str_len += strlen(rr->valuestring) + 1;
        }
    }

    pp = p = (char *)malloc(sizeof(char) * (str_len + 1));
    if (p == NULL)
    {
        // no mem
        GWLOG_ERROR(m_comm, "no mem for init conf strings\n");
        return NULL;
    }

    for (i = 0; i < size; ++i)
    {
        rr = cJSON_GetArrayItem(r, i);
        if (cJSON_IsString(rr))
        {
            strcpy(p, rr->valuestring);
            p += strlen(rr->valuestring) + 1;
        }
    }

    *p = '\0';

    return pp;
}

void CAccoutfilterRule::free_accout_info(rule_user_info_t *rule_accout_info_ptr)
{
    int i;

    if (rule_accout_info_ptr == NULL)
    {
        return;
    }

    if (rule_accout_info_ptr->rule_user_info == NULL)
    {
        free(rule_accout_info_ptr);
        return;
    }

    for (i = 0; i < rule_accout_info_ptr->rule_user_info_num; ++i)
    {
        user_info_rule_t *p = &rule_accout_info_ptr->rule_user_info[i];
        if (p != NULL)
        {
            if (p->url)
            {
                free(p->url);
            }

            if (p->keys)
            {
                free(p->keys);
            }

            if (p->keys_name)
            {
                free(p->keys_name);
            }

            if (p->keys_uid)
            {
                free(p->keys_uid);
            }
        }
    }
    free(rule_accout_info_ptr->rule_user_info);
    free(rule_accout_info_ptr);
}

void CAccoutfilterRule::rule_user_extract(const char *s, size_t length, rule_key_t *rk_list)
{
  size_t k;
  const char *kn;
  const char *s1;
  const char *s2;
  const char *ss;
  const char *h1;
  const char *h2;
  const char *v1;
  const char *v2;
  rule_key_t *prk;

  if (s == NULL || length == 0)
  {
    return;
  }

  for (k = length, s1 = s; k--; s++)
  {
    if (!(*s == ';' || k == 0))
    {
      continue;
    }

    //  aaa=bbb;
    //  ^      ^
    // s1      s
    if (*s != ';')
    {
      s++;
    }
    s2 = s;
    if (s1 >= s2 || (ss = my_strchr(s1, s2 - s1, '=')) == NULL)
    {
      s1 = s + 1;
      continue;
    }

    //  aaaaa = bbbbb;
    //  ^     ^ ^    ^
    //  h1   h2 v1   v2
    h1 = s1;
    h2 = ss;
    v1 = ss + 1;
    v2 = s2;
    // 去除首末的空格
    while (h1 < h2 && isspace((unsigned char)*h1))
      h1++;
    while (h1 < h2 && isspace((unsigned char)*(h2 - 1)))
      h2--;
    while (v1 < v2 && isspace((unsigned char)*v1))
      v1++;
    while (v1 < v2 && isspace((unsigned char)*(v2 - 1)))
      v2--;

    //printf("rule_user_extract [%d] %.*s = [%d] %.*s\n", h2 - h1, h2 - h1, h1, v2 - v1, v2 - v1, v1);

    s1 = s + 1;
    if (h1 >= h2)
    {
      // key 为空
      continue;
    }

    // TODO 配置项量过大时   需要将数组方式 改为 HASH方式
    for (prk = rk_list; prk->out && prk->r_k; ++prk)
    {
      for (kn = prk->r_k; *kn; kn += (strlen(kn) + 1))
      {
        if ((unsigned)(h2 - h1) == strlen(kn) && 0 == strncasecmp(h1, kn, strlen(kn)))
        {
          cJSON_AddString3ToObject(prk->out, h1, h2 - h1, v1, v2 - v1);
          break;
        }
      }
    }
  }
}

/**
 *  动态加载账号转发规则 
 *  @param const char*
 */
void CAccoutfilterRule::read_conf_user_info_for_mon(const char *filename)
{
    rule_user_info_t *old_conf_ptr = m_accoutfilter_info;
    ASSERT(m_comm != NULL);
    ASSERT(m_tcpparser != NULL);
    CGwConfig *pgwc = m_comm->get_gw_config();
    ASSERT(pgwc != NULL);
    pgwc->set_config_path(filename);
    if (!pgwc->load())
    {
      GWLOG_ERROR(m_comm, " %s mon accout conf load error!\n", filename);
      return;
    }

    const cJSON *cjson_forward_conf = NULL;
    cjson_forward_conf = pgwc->get_json_conf();
    // 从配置文件读取新参数
    rule_user_info_t *rule_accout_ptr = read_accout_rule_info(cjson_forward_conf);
    m_accoutfilter_info = rule_accout_ptr;

    // 等当前工作线程结束，使用到新的全局配置
    if (m_tcpparser->wait_for_worker_use_conf())
    {
        return;
    }

    free_accout_info(old_conf_ptr);
}
