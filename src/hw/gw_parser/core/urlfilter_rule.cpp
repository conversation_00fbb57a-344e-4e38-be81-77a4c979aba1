#include <stdio.h>
#include <string.h>
#include <stdlib.h>

#include "urlfilter_rule.h"
#include "gw_common.h"
#include "gw_config.h"
#include "utils.h"
#include "gw_logger.h"
#include "tcp_parser.h"

#define DEFAULT_SHOLD (100000L)
#define DEFAULT_PERCENT (1.0)
#define DEFAULT_UNKNOWN_RULE_MAX_NUM (200L)

CUrlfilterRule::CUrlfilterRule(void) : m_comm(NULL)
                                     , m_tcpparser(NULL)
                                     , m_p_url_filter(NULL)
                                     , m_i_init(0)
                                     , m_i_unknown_url_max_num(0)
{
}

CUrlfilterRule::~CUrlfilterRule(void)
{
    
}

/**
 * 命中过滤规则。
 * @param unsigned int data
 */
int CUrlfilterRule::hit(unsigned data)
{
    return 0;
}

/**
 * 命中URL转发规则
 * @param const char *url
 */
int CUrlfilterRule::forward(const char *url)
{
    return 0;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CUrlfilterRule::set_gw_common(CGwCommon *comm)
{
    ASSERT(comm != NULL);
    m_comm = comm;
}

/**
 *  设置TcpParser对象实例 
 *  @paramount CTcpParser *tcpparser
 */
void CUrlfilterRule::set_tcp_parser(CTcpParser *tcpparser)
{
    ASSERT(tcpparser != NULL);
    m_tcpparser = tcpparser;
}

/**
 *    设置url过滤规则 
 *    @param const char*
 */
void CUrlfilterRule::set_url_filter(const char *p_urlfilter_name)
{   
    ASSERT(m_comm != NULL);
    if (m_i_init == 0)
    {
        GWLOG_WARN(m_comm, "url filter not init\n");
        return;
    }

    const char *p_conf_name = NULL;
    CGwConfig *pgwc = m_comm->get_gw_config();
    ASSERT(pgwc != NULL);
    p_conf_name = pgwc->get_config_path();

    pgwc->set_config_path(p_urlfilter_name);
    if (!pgwc->load())
    {
      GWLOG_ERROR(m_comm, " %s load error!\n", p_urlfilter_name);
    }

    const cJSON *cjson_forward_conf = NULL;
    cjson_forward_conf = pgwc->get_json_conf();

    m_p_url_filter = read_urlfilter_forward(cjson_forward_conf);
    init_forward_table_inner();

    pgwc->set_config_path(p_conf_name);
    pgwc->load();
    return;
}

int CUrlfilterRule::check_forward(char *full_url, char *sub_url, int full_check, int *forward_reason)
{
  (void)full_check;

  int forward = 0; //0 不转发 1 转发
  *forward_reason = DONT_MATCH_FORWARD_RULE;
  rule_forward_t *rfp = m_p_url_filter;

  /* 初始化转发表 */
  //init_forward_table();

  /* 没有配置转发规则时，所有URL均会被转发 */
  if (NULL == rfp || NULL == rfp->rule_forward_info)
  {
    forward = 1;
    *forward_reason = NO_FORWARD_RULE;
    return forward;
  }

  pthread_mutex_lock(&m_lock);

  int match_rule = 0;
  int i = 0;
  int urllen = 0;
  forward_table *forward_item = NULL;
  for (i = 0; i < rfp->known_forward_table_cnt; i++)
  {
    urllen = strlen(rfp->known_forward_table[i].url);
    if (0 == strncasecmp(full_url, rfp->known_forward_table[i].url, urllen))
    {
      match_rule = 1;
      forward_item = &rfp->known_forward_table[i];
      *forward_reason = MATCH_FORWARD_RULE;
      break;
    }
  }

  if (NULL == forward_item)
  {
    for (i = 0; i < rfp->unknown_forward_table_cnt; i++)
    {
      urllen = strlen(rfp->unknown_forward_table[i].url);
      if (0 == strncasecmp(sub_url, rfp->unknown_forward_table[i].url, urllen))
      {
        forward_item = &rfp->unknown_forward_table[i];
        break;
      }
    }

    if ((NULL == forward_item) && (rfp->unknown_forward_table_cnt < m_i_unknown_url_max_num))
    {
      forward_item = &rfp->unknown_forward_table[rfp->unknown_forward_table_cnt];
      rfp->unknown_forward_table_cnt++;
      strncpy(rfp->unknown_forward_table[i].url, sub_url, strlen(sub_url));
      rfp->unknown_forward_table[i].threshold = rfp->rule_forward_unknown_info->threshold;
      rfp->unknown_forward_table[i].percent = rfp->rule_forward_unknown_info->percent;
    }
    else if (rfp->unknown_forward_table_cnt >= m_i_unknown_url_max_num)
    {
      /* 是否需要扩容 */
    }
  }
  pthread_mutex_unlock(&m_lock);

  if (NULL == forward_item)
  {
    forward = 0;
    return forward;
  }

  /* 继续判断，是否已经触发了抽样阈值 */
  if (1 == match_rule)
  {
    forward_item->counter++;

    if (1 == forward_item->counter)
    {
      forward = 1;
    }
    else if (forward_item->counter < (unsigned)forward_item->threshold)
    {
      forward = 1;
    }
    else if (forward_item->counter == (unsigned)forward_item->threshold)
    {
      forward_item->sampling = 1;
      forward_item->last_sampling = forward_item->counter;
      forward = 1;
    }
    else if (forward_item->counter > (unsigned)forward_item->threshold)
    {
      double sampling_rate = forward_item->threshold * forward_item->percent * 0.01;
      if (0 == sampling_rate)
      {
        forward = 0;
      }
      else
      {
        int offset = forward_item->threshold / sampling_rate;
        if (offset < 1)
        {
          offset = 1;
        }

        if (forward_item->counter - forward_item->last_sampling == (unsigned)offset)
        {
          forward = 1;
          forward_item->last_sampling = forward_item->counter;
        }
        else
        {
          forward = 0;
        }
      }
    }
  }
  else
  {
    forward_item->counter++;

    if (1 == forward_item->counter)
    {
      /* 未配置的规则，第一条都放行 */
      forward = 1;
      if (forward_item->counter == (unsigned)forward_item->threshold)
      {
        forward_item->sampling = 1;
        forward_item->last_sampling = forward_item->counter;
      }
    }
    else if (forward_item->counter < (unsigned)forward_item->threshold)
    {
      forward = 0;
    }
    else if (forward_item->counter == (unsigned)forward_item->threshold)
    {
      forward_item->sampling = 1;
      forward_item->last_sampling = forward_item->counter;
      forward = 1;
    }
    else if (forward_item->counter > (unsigned)forward_item->threshold)
    {
      double sampling_rate = forward_item->threshold * forward_item->percent * 0.01;
      if (0 == sampling_rate)
      {
        forward = 0;
      }
      else
      {
        int offset = forward_item->threshold / sampling_rate;
        if (offset < 1)
        {
          offset = 1;
        }

        if (forward_item->counter - forward_item->last_sampling == (unsigned)offset)
        {
          forward = 1;
          forward_item->last_sampling = forward_item->counter;
        }
        else
        {
          forward = 0;
        }
      }
    }
  }

#ifdef FORWARD_DEBUG
  printf("counter:%d, forward:%d\n", forward_item->counter, forward);
#endif

  return forward;
}

/**
 *  动态加载URL转发规则 
 *  @param const char*
 */
void CUrlfilterRule::read_conf_forward_for_mon(const char* filename)
{
    rule_forward_t *old_conf_ptr = m_p_url_filter;
    ASSERT(m_comm != NULL);
    ASSERT(m_tcpparser != NULL);
    CGwConfig *pgwc = m_comm->get_gw_config();
    ASSERT(pgwc != NULL);
    pgwc->set_config_path(filename);
    if (!pgwc->load())
    {
      GWLOG_ERROR(m_comm, " %s mon forward conf load error!\n", filename);
      return;
    }

    const cJSON *cjson_forward_conf = NULL;
    cjson_forward_conf = pgwc->get_json_conf();
    // 从配置文件读取新参数
    rule_forward_t *rule_forward_ptr = read_urlfilter_forward(cjson_forward_conf);
    m_p_url_filter = rule_forward_ptr;

    // 等当前工作线程结束，使用到新的全局配置
    if (m_tcpparser->wait_for_worker_use_conf())
    {
        return;
    }

    free_urlfilter_forward(old_conf_ptr);
}

void CUrlfilterRule::init()
{
    ASSERT(m_comm != NULL);
    ASSERT(m_tcpparser != NULL);
    int i_ret = 0;
    if (m_i_init == 0)
    {
        CGwConfig *pgwc = m_comm->get_gw_config();
        ASSERT(pgwc != NULL);
        m_i_unknown_url_max_num = pgwc->read_conf_int("parser", "unknown_rule_forward_max_num", DEFAULT_UNKNOWN_RULE_MAX_NUM);
        
        i_ret = pthread_mutex_init(&(m_lock), NULL);
        if (i_ret != 0)
        {
            GWLOG_ERROR(m_comm, "url filter init lock failed(%s)\n", strerror(i_ret));
            return;
        }

        m_i_init = 1;
    }
}

void CUrlfilterRule::fini()
{
    if (m_i_init == 0)
    {
        return;
    }

    if (m_p_url_filter != NULL)
    {

    }

    pthread_mutex_destroy(&m_lock);
    m_i_init = 0;

    return;
}

rule_forward_t* CUrlfilterRule::read_urlfilter_forward(const cJSON *cjson_forward_conf)
{
    int i = 0;
    int k = 0;
    forward_info_rule_t *p = NULL;
    if (!cJSON_IsObject(cjson_forward_conf))
    {
        return NULL;
    }

    rule_forward_t *rule_forward_ptr = NULL;
    rule_forward_ptr = (rule_forward_t *)malloc(sizeof(rule_forward_t));
    if (rule_forward_ptr == NULL)
    {
        return NULL;
    }
    memset(rule_forward_ptr, 0, sizeof(rule_forward_t));

    for (i = 0; i < cJSON_GetArraySize(cjson_forward_conf); ++i)
    {
        cJSON *rule_conf_host = cJSON_GetArrayItem(cjson_forward_conf, i);
        for (k = 0; k < cJSON_GetArraySize(rule_conf_host); ++k)
        {
            cJSON *rule_conf_item = cJSON_GetArrayItem(rule_conf_host, k);
            if (cJSON_IsObject(rule_conf_item))
            {
                cJSON *r_url = cJSON_GetObjectItem(rule_conf_item, "url");
                if (cJSON_IsString(r_url))
                {
                    ++rule_forward_ptr->rule_forward_info_num;
                }
            }
        }
    }

    if (rule_forward_ptr->rule_forward_info_num > 0)
    {
        rule_forward_ptr->forward = 1;
    }
    else
    {
        free(rule_forward_ptr);
        return NULL;
    }

    p = rule_forward_ptr->rule_forward_info = (forward_info_rule_t*)malloc(sizeof(forward_info_rule_t) * rule_forward_ptr->rule_forward_info_num);
    if (rule_forward_ptr->rule_forward_info == NULL)
    {
        free(rule_forward_ptr);
        return NULL;
    }
    memset(p, 0, sizeof(*rule_forward_ptr->rule_forward_info) * rule_forward_ptr->rule_forward_info_num);

    for (i = 0; i < cJSON_GetArraySize(cjson_forward_conf); ++i)
    {
        cJSON *rule_conf_host = cJSON_GetArrayItem(cjson_forward_conf, i);
        if ((NULL != rule_conf_host) && (NULL != rule_conf_host->string))
        {
            if (0 == strncmp(rule_conf_host->string, "unknown", strlen(rule_conf_host->string)))
            {
                continue;
            }
        }
        else
        {
            continue;
        }

        for (k = 0; k < cJSON_GetArraySize(rule_conf_host); ++k)
        {
            cJSON *rule_conf_item = cJSON_GetArrayItem(rule_conf_host, k);
            if (cJSON_IsObject(rule_conf_item))
            {
                cJSON *url = cJSON_GetObjectItem(rule_conf_item, "url");
                if ((cJSON_IsString(url)) && (NULL != url->valuestring))
                {
                    p->url = (char *)malloc(sizeof(char) * (strlen(url->valuestring) + 1));
                    if (p->url == NULL)
                    {
                        GWLOG_WARN(m_comm, "init forward rule mem failed\n");
                    }
                    else
                    {
                        strcpy(p->url, url->valuestring);
                    }
                }
                else
                {
                    p->url = NULL;
                    continue;
                }

                cJSON *state = cJSON_GetObjectItem(rule_conf_item, "state");
                if (NULL != state)
                {
                    p->state = state->valueint;
                }
                else
                {
                    p->state = 0;
                }

                cJSON *threshold = cJSON_GetObjectItem(rule_conf_item, "threshold");
                if (NULL != threshold)
                {
                    p->threshold = threshold->valueint;
                }
                else
                {
                    p->threshold = DEFAULT_SHOLD;
                }

                cJSON *percent = cJSON_GetObjectItem(rule_conf_item, "percent");
                if (NULL != percent)
                {
                    p->percent = percent->valuedouble;
                }
                else
                {
                    p->percent = DEFAULT_PERCENT;
                }

                p++;
            }
        }
    }

    p = rule_forward_ptr->rule_forward_unknown_info = (forward_info_rule_t*)malloc(sizeof(forward_info_rule_t) * 1);
    if (rule_forward_ptr->rule_forward_unknown_info == NULL)
    {
        free_urlfilter_forward(rule_forward_ptr);
        return NULL;
    }

    memset(p, 0, sizeof(forward_info_rule_t));

    for (i = 0; i < cJSON_GetArraySize(cjson_forward_conf); ++i)
    {
        cJSON *rule_conf_host = cJSON_GetArrayItem(cjson_forward_conf, i);

        if ((NULL != rule_conf_host) && (NULL != rule_conf_host->string))
        {
            if (0 != strncmp(rule_conf_host->string, "unknown", strlen(rule_conf_host->string)))
            {
                continue;
            }
        }
        else
        {
            continue;
        }

        for (k = 0; k < cJSON_GetArraySize(rule_conf_host); ++k)
        {
            cJSON *rule_conf_item = cJSON_GetArrayItem(rule_conf_host, k);
            if (cJSON_IsObject(rule_conf_item))
            {
                cJSON *threshold = cJSON_GetObjectItem(rule_conf_item, "threshold");
                if (NULL != threshold)
                {
                    p->threshold = threshold->valueint;
                }
                else
                {
                    p->threshold = DEFAULT_SHOLD;
                }

                cJSON *percent = cJSON_GetObjectItem(rule_conf_item, "percent");
                if (NULL != percent)
                {
                    p->percent = percent->valuedouble;
                }
                else
                {
                    p->percent = DEFAULT_PERCENT;
                }
            }
            break;
        }
        break;
    }

    return rule_forward_ptr;
}

void CUrlfilterRule::free_urlfilter_forward(rule_forward_t *p_url_filter)
{
    if (p_url_filter == NULL)
    {
        return;
    }

    int i = 0;

    if (p_url_filter->rule_forward_info)
    {
        for (i = 0; i < p_url_filter->rule_forward_info_num; ++i)
        {
            if (p_url_filter->rule_forward_info[i].url)
            {
                free(p_url_filter->rule_forward_info[i].url);
            }
        }
        
        free(p_url_filter->rule_forward_info);
    }

    if (p_url_filter->rule_forward_unknown_info)
    {
        free(p_url_filter->rule_forward_unknown_info);
    }

    if (m_p_url_filter->known_forward_table)
    {
        free(m_p_url_filter->known_forward_table);
    }

    if (m_p_url_filter->unknown_forward_table)
    {
        free(m_p_url_filter->unknown_forward_table);
    }

    free(p_url_filter);
}

 void CUrlfilterRule::init_forward_table_inner()
 {
    if (m_p_url_filter == NULL)
    {
        return;
    }
    
    if (NULL == m_p_url_filter->rule_forward_info)
    {
        return;
    }

    /* 初始化已知转发规则 */
    m_p_url_filter->known_forward_table = (forward_table*)malloc(sizeof(forward_table) * m_p_url_filter->rule_forward_info_num);
    if (m_p_url_filter->known_forward_table == NULL)
    {
        GWLOG_ERROR(m_comm, "no mem for forward table\n");
        return;
    }

    memset(m_p_url_filter->known_forward_table, 0, sizeof(forward_table) * m_p_url_filter->rule_forward_info_num);
    int i = 0;
    for (i = 0; i < m_p_url_filter->rule_forward_info_num; i++)
    {
        if (m_p_url_filter->rule_forward_info[i].url)
        {
            strncpy(m_p_url_filter->known_forward_table[i].url, m_p_url_filter->rule_forward_info[i].url, URL_LEN);
        }
        m_p_url_filter->known_forward_table[i].threshold = m_p_url_filter->rule_forward_info[i].threshold;
        m_p_url_filter->known_forward_table[i].percent = m_p_url_filter->rule_forward_info[i].percent;
    }
    m_p_url_filter->known_forward_table_cnt = m_p_url_filter->rule_forward_info_num;

    m_p_url_filter->unknown_forward_table = (forward_table*)malloc(sizeof(forward_table) * m_i_unknown_url_max_num);
    if (m_p_url_filter->unknown_forward_table == NULL)
    {
        GWLOG_ERROR(m_comm, "no mem for unknown forward table\n");
        return;
    }
    memset(m_p_url_filter->unknown_forward_table, 0, sizeof(forward_table) * m_i_unknown_url_max_num);
    m_p_url_filter->unknown_forward_table_cnt = 0;
 }
