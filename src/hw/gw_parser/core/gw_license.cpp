/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>

#include "gw_license.h"

#include "utils.h"
#include "gw_ver.h"
#include "gw_logger.h"
#include "gw_common.h"
#include "gw_config.h"
#include "gw_stats.h"

/**
 * CGwLicense implementation
 *
 * 授权管理
 */

#define CHECK_LIMIT_SPEED_DELAY (1)
#define CHECK_EXPIRED_DELAY (60)

#define DPDK_MODULE "dpdk"
#define ETH_NUM "eth_num"
#define DPDK_LIMIT_RATE "recv_rate"
#define PCAP_MODULE "pcap"
#define PCAP_ENABLE "enable"
#define PCAP_LIMIT_RATE "recv_rate"
#define SSL_MODULE "ssl"
#define SSL_ENABLE "enable"
#define BUSSINESS_MODULE "business"
#define HTTP_GZIP_DEEP "http_gzip"

// 编译开关，是否禁用license模块
#ifndef _DISABLE_LICENSE_
  #define _DISABLE_LICENSE_ 0
#endif

  
//#if !_DISABLE_LICENSE_
CGwLicense::CGwLicense() : m_comm(NULL)
                         , m_quit_signal(0)
                         , m_i_verify_samll_version(0)      /* 默认不认证小版本 */
                         , m_u_product_expired(0)           /* 默认产品不超时 */
                         , m_u_pcap_enable(1)               /* 默认开启agent模式 */
                         , m_u_pcap_expired(0)              /* 默认agent模式不超时 */
                         , m_u_ssl_enable(1)                /* 默认开启ssl功能 */
                         , m_u_eth_num(0)
                         , m_u_limit_rate(0)
                         , m_u_magic(0)
                         , m_u_gzip_deep(0)                 /* 默认不限制解压深度 */
                         , m_u_is_limit_speed(0)            /* 默认不限速 */
                         , m_i_source_flag(0)
                         , m_check_expired_thread_stats(0)
                         , m_check_rate_thread_stats(0)
                         
{
}

CGwLicense::~CGwLicense()
{
  fini();
}

void CGwLicense::init()
{
  GWLOG_INFO(m_comm, "GwLicense init()\r\n");
  ASSERT(m_comm != NULL);
  m_quit_signal = 0;

  load_conf(NULL);
  #if !_DISABLE_LICENSE_
  init_licutils();
  #endif
}

void CGwLicense::fini()
{
  ASSERT(m_comm != NULL);
  #if !_DISABLE_LICENSE_
  release_lic_info();
  #endif
  m_vec_source_name.clear();
}

void CGwLicense::run()
{
  #if !_DISABLE_LICENSE_
  int err = 0;
  ASSERT(m_comm != NULL);
  for (const auto& source_name : m_vec_source_name)
  {
    if (source_name.find("Dpdk") != std::string::npos)
    {
      m_i_source_flag |= SOURCE_FLAG_DPDK;
    }
    else if (source_name.find("File") != std::string::npos)
    {
      m_i_source_flag |= SOURCE_FLAG_AGENT;
    }
    else if (source_name.find("Nic") != std::string::npos)
    {
      m_i_source_flag |= SOURCE_FLAG_NIC;
    }
    else if (source_name.find("Pcap") != std::string::npos)
    {
      m_i_source_flag |= SOURCE_FLAG_PCAP;
    }
    else
    {
      GWLOG_WARN(m_comm, "invaild source module(%s)\n", source_name.c_str());
    }
  }

  if (0 != (err = pthread_create(&m_thread_check_expired, NULL, (void *(*)(void *))report_check_expired_run, this)))
  {
    GWLOG_ERROR(m_comm, "check expired thread create failed(%s)\n", strerror(err));
  }
  else
  {
    m_check_expired_thread_stats = 1;
    GWLOG_INFO(m_comm, "check expired thread create successfully\n");
  }

  if (0 != (err = pthread_create(&m_thread_check_rate, NULL, (void *(*)(void*))report_check_rate_run, this)))
  {
    GWLOG_ERROR(m_comm, "check rate thread create faield(%s)\n", strerror(err));
  }
  else
  {
    m_check_rate_thread_stats = 1;
    GWLOG_INFO(m_comm, "check rate thread create successfully\n");
  }
  #endif
  return;
}

/**
 * 验证网卡源数量
 * @param int num
 */
int CGwLicense::verify_source_num(int num)
{
  return 0;
}

/**
 * 验证模块功能开关
 * @param const char*
 */
int CGwLicense::verify_func(const char *)
{
  return 0;
}

/**
 * 验证产品有效时间。
 */
int CGwLicense::verify_product_exprie()
{
  return m_u_product_expired;
}

/**
 * 验证是否需要限流 
 */
int CGwLicense::verify_limit_rate()
{
  return m_u_is_limit_speed;
}

/*
获取限流发生时的魔数，用于按session丢包
*/
int CGwLicense::get_magic()
{
  return m_u_magic;
}

/**
 * 触发退出信号时处理
 */
void CGwLicense::set_quit_signal(void)
{
  m_quit_signal = 1;
}

/**
 * 等待运行结束
 */
void CGwLicense::wait_for_stop(void)
{
  #if !_DISABLE_LICENSE_
  if (m_check_expired_thread_stats == 1)
  {
    pthread_join(m_thread_check_expired, NULL);
    m_check_expired_thread_stats = 0;
  }
  
  if (m_check_rate_thread_stats == 1)
  {
    pthread_join(m_thread_check_rate, NULL);
    m_check_rate_thread_stats = 0;
  }
  #endif
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CGwLicense::set_gw_common(CGwCommon *comm)
{
  m_comm = comm;
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CGwLicense::load_conf(const char *)
{
  CGwConfig *p_gwconf = m_comm->get_gw_config();

  std::string str_lic_path = p_gwconf->read_conf_string("parser", "licutils_path");
  if (str_lic_path.size() > 0)
  {
    m_str_lic_path = str_lic_path;
  }
  else
  {
    m_str_lic_path = "/opt/licutils/license.lic";
  }

  m_i_verify_samll_version = p_gwconf->read_conf_int("parser", "verify_samll_version", 0);

  return true;
}

/**
 * 设置ip字节数在状态统计的名字，根据名字查找qps，限速使用 
 * @param const char *
 */
void CGwLicense::set_ip_bytes_stat_name(const char *name)
{
  m_str_ip_stat_name = name;
}

/**
 * 设置source源名称
 * @param const char*
 */
void CGwLicense::set_source_name(const char *name)
{
  ASSERT(name != NULL);
  m_vec_source_name.push_back(name);
}

/**
 *  获取网卡源数量
 */
unsigned CGwLicense::get_eth_num()
{
  return m_u_eth_num;
}

/**
 * 验证pcap源功能是否开启 
 */
unsigned CGwLicense::verify_pcap_func()
{
  return m_u_pcap_enable;
}

/**
 *  验证ssl功能是否开启 
 */
unsigned CGwLicense::verify_ssl_func()
{
  return m_u_ssl_enable;
}

/**
 *  获取gzip解压深度 
 */
unsigned CGwLicense::get_gzip_deep()
{
  return m_u_gzip_deep;
}

int CGwLicense::report_check_expired_run(void *arg_ptr)
{
  CGwLicense *pThis = (CGwLicense *)arg_ptr;
  ASSERT(pThis != NULL);

  return pThis->check_license_expired();
}

int CGwLicense::check_license_expired()
{
  #if !_DISABLE_LICENSE_
  int i_ret = 0;

  while (!m_quit_signal)
  {
    
    i_ret = check_expired(GW_PRODUCT_ID, NULL);
    
    if (i_ret != 0)
    {
      GWLOG_ERROR(m_comm, "product expired\n");
      m_u_product_expired = 1;
    }

    i_ret = check_expired(GW_PRODUCT_ID, PCAP_MODULE);
    if (i_ret != 0)
    {
      GWLOG_ERROR(m_comm, "pcap func expired\n");
      m_u_pcap_expired = 1;
    }

    sleep(CHECK_EXPIRED_DELAY);
  }
  #endif

  return 0;
}

int CGwLicense::report_check_rate_run(void *arg_ptr)
{
  CGwLicense *pThis = (CGwLicense *)arg_ptr;
  ASSERT(pThis != NULL);

  return pThis->check_license_rate();
}

int CGwLicense::check_license_rate()
{
  uint32_t u32_recv_rate = 0;
  ASSERT(m_comm != NULL);
  int delta = 0;
  while (!m_quit_signal)
  {
    u32_recv_rate = m_comm->get_gw_stats()->get_name_qps(m_str_ip_stat_name.c_str());

    delta = u32_recv_rate - m_u_limit_rate;
    if (delta > 0 && m_u_limit_rate > 0)
    {
      m_u_magic = delta * DROP_MOD / u32_recv_rate;
      m_u_is_limit_speed = 1;
    }
    else
    {
      m_u_is_limit_speed = 0;
    }
    
    if (m_u_is_limit_speed)
    {
      GWLOG_INFO(m_comm, "limit rate reached, u32_rate:%u, total limit rate:%u, source_flag = 0x%x, magic=%d\n", u32_recv_rate, m_u_limit_rate, m_i_source_flag, m_u_magic);
    }

    sleep(CHECK_LIMIT_SPEED_DELAY);
  }
  return 0;
}

void CGwLicense::creat_product_info(st_pro_info_t *p_st_pro_info)
{
  if (m_i_verify_samll_version == 1)
  {
    snprintf(p_st_pro_info->a_pro_version, sizeof(p_st_pro_info->a_pro_version) - 1, "%d.%d.%d", GW_VER_MAJOR, GW_VER_MINOR, GW_VER_REVISION);
  }
  else
  {
    snprintf(p_st_pro_info->a_pro_version, sizeof(p_st_pro_info->a_pro_version) - 1, "%d", GW_VER_MAJOR);
  }

  snprintf(p_st_pro_info->a_pro_id, sizeof(p_st_pro_info->a_pro_id) - 1, "%s", GW_PRODUCT_ID);
  snprintf(p_st_pro_info->a_pro_serialid, sizeof(p_st_pro_info->a_pro_serialid) - 1, "%s", GW_PRODUCT_SERIAL_ID);
}

/**
 * 初始化licese文件
 */
int CGwLicense::init_licutils(void)
{
  #if !_DISABLE_LICENSE_
  int i_ret = 0;
  st_pro_info_t st_product_info;
  memset(&st_product_info, 0, sizeof(st_pro_info_t));
  creat_product_info(&st_product_info);
  i_ret = init_license(m_str_lic_path.c_str(), &st_product_info);
  if (i_ret != 0)
  {
    GWLOG_ERROR(m_comm, "init license file(%s) failed(%d)\n", m_str_lic_path.c_str(), i_ret);
    /* 设置进程退出 */
    m_comm->set_gwparser_exit();
    return i_ret;
  }

  GWLOG_INFO(m_comm, "init license file successfully\n");
  parser_func_license();
  #endif

  return 0;
}

/**
 * 解析license功能模块信息 
 */
void CGwLicense::parser_func_license(void)
{
  #if !_DISABLE_LICENSE_
  int i_ret = 0;
  char a_eth_num[6] = {0};
  i_ret = get_funtion_limit_info(GW_PRODUCT_ID, DPDK_MODULE, ETH_NUM, a_eth_num, sizeof(a_eth_num) - 1);
  if (i_ret == 0)
  {
    m_u_eth_num = strtoul(a_eth_num, NULL, 10);
    GWLOG_INFO(m_comm, "eth num = %u\n", m_u_eth_num);
  }
  else
  {
    GWLOG_ERROR(m_comm, "get eth num limit info failed(%d)\n", i_ret);
  }


  i_ret = check_expired(GW_PRODUCT_ID, NULL);
  if (i_ret != 0)
  {
    GWLOG_ERROR(m_comm, "product expired\n");
    m_u_product_expired = 1;
  }

  char a_pcap_enable[6] = {0};
  i_ret = get_funtion_limit_info(GW_PRODUCT_ID, PCAP_MODULE, PCAP_ENABLE, a_pcap_enable, sizeof(a_pcap_enable) - 1);
  if (i_ret == 0)
  {
    m_u_pcap_enable = strtoul(a_pcap_enable, NULL, 10);
    GWLOG_INFO(m_comm, "pcap enable = %u\n", m_u_pcap_enable);
  }
  else
  {
    GWLOG_ERROR(m_comm, "get pcap enable info failed(%d)\n", i_ret);
  }

  char limit_rate[16] = {0};
  i_ret = get_product_base_info(GW_PRODUCT_ID, "flow_rate", limit_rate, sizeof(limit_rate) - 1);
  if (i_ret == 0)
  {
    m_u_limit_rate = strtoul(limit_rate, NULL, 10);
    GWLOG_INFO(m_comm, "limit rate = %u\n", m_u_limit_rate);
  }
  else
  {
    GWLOG_ERROR(m_comm, "get limit rate info failed(%d)\n", i_ret);
  }

  char a_ssl_enable[6] = {0};
  i_ret = get_funtion_limit_info(GW_PRODUCT_ID, SSL_MODULE, SSL_ENABLE, a_ssl_enable, sizeof(a_ssl_enable) - 1);
  if (i_ret == 0)
  {
    m_u_ssl_enable = strtoul(a_ssl_enable, NULL, 10);
    GWLOG_INFO(m_comm, "ssl enable = %u\n", m_u_ssl_enable);
  }
  else
  {
    GWLOG_ERROR(m_comm, "get ssl enable failed(%d)\n", i_ret);
  }

  char a_gzip_deep[6] = {0};
  i_ret = get_funtion_limit_info(GW_PRODUCT_ID, BUSSINESS_MODULE, HTTP_GZIP_DEEP, a_gzip_deep, sizeof(a_gzip_deep) - 1);
  if (i_ret == 0)
  {
    m_u_gzip_deep = strtoul(a_gzip_deep, NULL, 10);
    GWLOG_INFO(m_comm, "gzip deep = %u\n", m_u_gzip_deep);
  }
  else
  {
    GWLOG_ERROR(m_comm, "get http gzip failed(%d)\n", i_ret);
  }
  #endif
  return;
}
//#endif
