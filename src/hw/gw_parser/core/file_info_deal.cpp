
#include <string.h>
#include "cJSON.h"
#include "utils.h"
#include "file_info_deal.h"

#define RW_FLAG_R_OR_W(x) (!((x) >> 1))

upload_stats_t::upload_stats_t(upload_stats_t&& o) : str_file_path(std::move(o.str_file_path))
                                                    , str_md5_sum(std::move(o.str_md5_sum))
                                                    , str_sha256(std::move(o.str_sha256))
                                                    , ret(o.ret)
{

}

upload_stats_t& upload_stats_t::operator=(upload_stats_t&& o) 
{
    str_file_path = std::move(o.str_file_path);
    str_md5_sum = std::move(o.str_md5_sum);
    str_sha256 = std::move(o.str_sha256);
    ret = o.ret;
    return *this;
}

struct FileInfoExt
{
  // std::string file_name;
  // std::string file_type;
  // std::size_t file_len;
  // std::string file_warn;
  // std::string file_data;
  int upload_flag;
  std::string upload_dir;
  std::string file_md5;
  std::string file_sha256;
};


const char* json_file_info = "{\"file_name\":%s,"
                              "\"file_type\":%s,"
                              "\"file_len\":%d,"
                              "\"file_warn\":%s,"
                              "\"file_data\":%s,"
                              "\"is_incomplete\":%d,"
                              "\"rw_flag\":%d,"
                              "\"upload_flag\":%d,"
                              "\"upload_dir\":%s,"
                              "\"sha256\":%s,"
                              "\"bucket\":%s,"
                              "\"file_real_len\":%d}";

const static size_t json_format_str_len = strlen(json_file_info);

file_info_json::file_info_json() : file_name(0)
                                , file_type(0)
                                , file_warn(0)
                                , file_data(0)
                                , dir(0)
                                , md5sum(0)
                                , sha256(0)
                                , bucket(0)
                                , rw_flag(0)
                                , is_complete(0)
                                , file_len(0)
                                , upload_flag(0) 
                                , file_real_len(0) {
}

file_info_json::file_info_json(file_info_json&& fij) : file_name(fij.file_name)
                                                    , file_type(fij.file_type)
                                                    , file_warn(fij.file_warn)
                                                    , file_data(fij.file_data)
                                                    , dir(fij.dir)
                                                    , md5sum(fij.md5sum)
                                                    , sha256(fij.sha256)
                                                    , bucket(fij.bucket)
                                                    , rw_flag(fij.rw_flag)
                                                    , is_complete(fij.is_complete)
                                                    , file_len(fij.file_len)
                                                    , upload_flag(fij.upload_flag)
                                                    , file_real_len(fij.file_real_len) {
    fij.file_name = NULL;
    fij.file_type = NULL;
    fij.file_warn = NULL;
    fij.file_data = NULL;
    fij.dir = NULL;
    fij.md5sum = NULL;
    fij.sha256 = NULL;
    fij.bucket = NULL;
}
file_info_json& file_info_json::operator=(file_info_json&& o) 
{
    cJSON_free_safe(file_name);
    cJSON_free_safe(file_type);
    cJSON_free_safe(file_warn);
    cJSON_free_safe(file_data);
    cJSON_free_safe(dir);
    cJSON_free_safe(md5sum);
    cJSON_free_safe(sha256);
    cJSON_free_safe(bucket);

    file_name = o.file_name, o.file_name = NULL;
    file_type = o.file_type, o.file_type = NULL;
    file_warn = o.file_warn, o.file_warn = NULL;
    file_data = o.file_data, o.file_data = NULL;
    dir = o.dir, o.dir = NULL;
    md5sum = o.md5sum, o.md5sum = NULL;
    sha256 = o.sha256, o.sha256 = NULL;
    bucket = o.bucket, o.bucket = NULL;
    rw_flag = o.rw_flag;
    is_complete = o.is_complete;
    file_len = o.file_len;
    upload_flag = o.upload_flag;
    file_real_len = o.file_real_len;
    return *this;
}

file_info_json::~file_info_json()
{
    cJSON_free_safe(file_name);
    cJSON_free_safe(file_type);
    cJSON_free_safe(file_warn);
    cJSON_free_safe(file_data);
    cJSON_free_safe(dir);
    cJSON_free_safe(md5sum);
    cJSON_free_safe(sha256);
    cJSON_free_safe(bucket);
}

file_info_json file_info_format_encode(const char* file_name, size_t file_name_len, 
                            const char* file_type, size_t file_type_len, 
                            const char* file_warn, size_t file_warn_len, 
                            const char* file_data, size_t file_len, 
                            int is_incomplete,
                            int rw_flag, FileUpload* fileupload , int max_data_len)
{
  if (fileupload && RW_FLAG_R_OR_W(rw_flag) && file_data && file_len) 
  {
    upload_stats_t result = fileupload->upload_file(file_data, file_len, file_name ? file_name : "");
    if (result.ret == 0) 
    {
      file_info_json st_f_i_json ;
      if (file_name && file_name_len) 
      {
        st_f_i_json.file_name = cJSON_EscapeStringWithBufferSize(file_name, file_name_len, NULL);
      }
      if (file_type && file_type_len) 
      {
        st_f_i_json.file_type = cJSON_EscapeStringWithBufferSize(file_type, file_type_len, NULL);
      }
      if (file_warn && file_warn_len) 
      {
        st_f_i_json.file_warn = cJSON_EscapeStringWithBufferSize(file_warn, file_warn_len, NULL);
      }
      if (result.str_file_path.size()) 
      {
        st_f_i_json.dir = cJSON_EscapeStringWithBufferSize(result.str_file_path.c_str(), result.str_file_path.size(), NULL);
      }
      if (result.str_md5_sum.size()) {
        st_f_i_json.md5sum = cJSON_EscapeStringWithBufferSize(result.str_md5_sum.c_str(), result.str_md5_sum.size(), NULL);
      }
      if (result.str_sha256.size()) {
        st_f_i_json.sha256 = cJSON_EscapeStringWithBufferSize(result.str_sha256.c_str(), result.str_sha256.size(), NULL);
      }
      if (result.bucket_name.size()) {
        st_f_i_json.bucket = cJSON_EscapeStringWithBufferSize(result.bucket_name.c_str(), result.bucket_name.size(), NULL);
      }
      st_f_i_json.file_len = file_len;
      st_f_i_json.is_complete = is_incomplete;
      st_f_i_json.rw_flag = rw_flag;
      st_f_i_json.upload_flag = 1;
      return st_f_i_json;
    }
    else 
    {
    }
  }

  {
    file_info_json st_f_i_json;
    if (file_name && file_name_len) 
    {
        st_f_i_json.file_name = cJSON_EscapeStringWithBufferSize(file_name, file_name_len, NULL);
    }
    if (file_type && file_type_len) 
    {
        st_f_i_json.file_type = cJSON_EscapeStringWithBufferSize(file_type, file_type_len, NULL);
    }
    if (file_data && file_len) 
    {
      if (file_warn && file_warn_len && file_len >= (size_t)max_data_len) 
      {
        st_f_i_json.file_data = cJSON_EscapeStringWithBufferSize(file_data, max_data_len, NULL);
        st_f_i_json.file_real_len = max_data_len;
      }
      else 
      {
        st_f_i_json.file_data = cJSON_EscapeStringWithBufferSize(file_data, file_len, NULL);
        st_f_i_json.file_real_len = file_len;
      }
    }
    if (file_warn && file_warn_len) 
    {
        st_f_i_json.file_warn = cJSON_EscapeStringWithBufferSize(file_warn, file_warn_len, NULL);
    }
    st_f_i_json.file_len = file_len;
    st_f_i_json.is_complete = is_incomplete;
    st_f_i_json.rw_flag = rw_flag;
    st_f_i_json.upload_flag = 0;
    return st_f_i_json;
  }
}

const char* file_info_json_string(const file_info_json& json) {
    size_t malloc_size = json_format_str_len;

    auto f = [](const char *p) -> size_t{ return p? strlen(p):4; };

    malloc_size += f(json.file_name);
    malloc_size += f(json.file_type);
    malloc_size += f(json.file_warn);
    malloc_size += f(json.file_data);
    malloc_size += f(json.dir);
    malloc_size += f(json.sha256);
    malloc_size += f(json.bucket);
    malloc_size += 128;
    char *const json_str = (char*)malloc(malloc_size);
    if ( !json_str ) 
    {
        return NULL;
    }
    sprintf(json_str, json_file_info, or_null(json.file_name)
                                , or_null(json.file_type)
                                , json.file_len
                                , or_null(json.file_warn)
                                , or_null(json.file_data)
                                , json.is_complete
                                , json.rw_flag
                                , json.upload_flag
                                , or_null(json.dir)
                                , or_null(json.sha256)
                                , or_null(json.bucket)
                                , json.file_real_len);
    return json_str;
}

const char* file_info_format(const char* file_name, size_t file_name_len, 
                            const char* file_type, size_t file_type_len, 
                            const char* file_warn, size_t file_warn_len, 
                            const char* file_data, size_t file_len, 
                            int is_incomplete,
                            int rw_flag, FileUpload* fileupload , int max_data_len) 
{
    file_info_json json = file_info_format_encode(file_name, file_name_len,
                                                file_type, file_type_len,
                                                file_warn, file_warn_len,
                                                file_data, file_len,
                                                is_incomplete, rw_flag, fileupload, max_data_len);
    return file_info_json_string(json);
}

const char* http_file_info_format(const char* file_name, size_t file_name_len, 
                            const char* file_type, size_t file_type_len, 
                            const char* file_warn, size_t file_warn_len, 
                            const char* file_data, size_t file_len, 
                            int is_incomplete,
                            int rw_flag, FileUpload* fileupload, int *upload_flag ,int max_data_len) 
{
    file_info_json json = file_info_format_encode(file_name, file_name_len,
                                                file_type, file_type_len,
                                                file_warn, file_warn_len,
                                                file_data, file_len,
                                                is_incomplete, rw_flag, fileupload, max_data_len);

    cJSON_free_safe(json.file_data), json.file_data = NULL;
    *upload_flag = json.upload_flag;
    return file_info_json_string(json);
}