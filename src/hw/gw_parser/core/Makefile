
ifeq ("$(BUILD_CC_TOOL)","clang++")
CC              = clang++ -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG_PP
else ifeq ("$(BUILD_CC_TOOL)","clang")
CC              = clang -fsanitize=address -fno-omit-frame-pointer -D_CC_CLANG
else ifeq ("$(BUILD_CC_TOOL)","g++")
CC              = g++ -D_CC_GNU_PP
else ifeq ("$(BUILD_CC_TOOL)", "aarch64-linux-gnu-gcc")
CC              = aarch64-linux-gnu-gcc
else
CC              = gcc
endif
AR              = ar


CFLAGS          = -fvisibility=hidden -fPIC -I. -I.. -I../include -I.././utils/cjson/ -I.././utils/lockfree_queue/ -I.././utils/pcap_struct/  -I./l4 -I.././liblicutils_c_sdk -I.././utils/file_type/ -I../nacos_c++/include -I../libaws_api_c++/include -I.././utils/raw_sock/ -I../libmagic/include -I../libbrotli/include/
#CFLAGS          = -fPIC -I. -I.. -I../include -I.././utils/cjson/ -I.././utils/lockfree_queue/ -I.././utils/pcap_struct/  -I./l4 -I.././liblicutils_c_sdk -I.././utils/file_type/ -I../nacos_c++/include -I../libaws_api_c++/include

ifeq ("$(BUILD_ARCH)", "ARM")
CFLAGS          += -I/home/<USER>/3rd/libpcap-1.9.1/ -I/home/<USER>/3rd/zlib/include/
endif

LDFLAGS         = -shared 
ifeq ("$(BUILD_ARCH)", "x86")
LDFLAGS        += -lstdc++  -L../utils/file_type/ -lfile_type -L../nacos_c++/lib/ -lnacos-cli -L../libaws_api_c++/lib64 -laws-cpp-sdk-core -laws-cpp-sdk-s3
else ifeq ("$(BUILD_ARCH)", "ARM")
LDFLAGS        += -lstdc++  -L../utils/file_type/ -lfile_type -L/home/<USER>/3rd/nacos-sdk-cpp/lib/ -lnacos-cli -L/home/<USER>/3rd/aws-sdk-cpp/lib/ -laws-cpp-sdk-core -laws-cpp-sdk-s3
endif

include ../flags.make

O_FILES = watch_dog.o tcp_parser.o gw_logger.o gw_license.o gw_common.o gw_config.o filter_rule.o ipfilter_rule.o \
		portfilter_rule.o urlfilter_rule.o accoutfilter_rule.o mon_conf.o get_cpu_mem_info.o worker_queue.o \
		gw_stats.o interactive_service.o gw_i_source.o gw_i_upload.o gw_i_parser.o session_mgt.o session.o ob_cfg.o\
		
O_FILES += cpp_utils.o nacos_listen_conf.o minio_upload.o
CFLAGS += -DHAVE_TCP_STATES=1
CFLAGS += -D_BSD_SOURCE -D__BSD_SOURCE -D__FAVOR_BSD -DHAVE_NET_ETHERNET_H

O_FILES += cJSON.o cJSON_Utils.o utils.o 
O_FILES += pkt_queue.o
O_FILES += pp.o ip_fragment.o ip_options.o pp_checksum.o pp_hash.o pp_tcp.o util.o event_analyze.o
O_FILES += addr_port_collect.o pcap_header.o raw_sock.o

.PHONY: all clean


all: libgw_core.a 

ip_fragment.o: l4/ip_fragment.c l4/ip_fragment.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

ip_options.o: l4/ip_options.c
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

pp_checksum.o: l4/pp_checksum.c l4/pp_checksum.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

pp_hash.o: l4/pp_hash.c l4/pp_hash.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

pp_tcp.o: l4/pp_tcp.c l4/pp_tcp.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

util.o: l4/util.c l4/util.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON.o: .././utils/cjson/cJSON.c .././utils/cjson/cJSON.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

cJSON_Utils.o: .././utils/cjson/cJSON_Utils.c .././utils/cjson/cJSON_Utils.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

pkt_queue.o: .././utils/lockfree_queue/pkt_queue.c .././utils/lockfree_queue/pkt_queue.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

# parser_gene_file.o: ../utils/file_type/parser_gene_file.c ../utils/file_type/parser_gene_file.h
# 	$(CC) -c $(CFLAGS)	$(LIBS_CFLAGS) $<

# get_file_type.o: .././utils/file_type/get_file_type.c ../utils/file_type/get_file_type.h
# 	$(CC) -c $(CFLAGS)	$(LIBS_CFLAGS) $<

pcap_header.o: .././utils/pcap_struct/pcap_header.c .././utils/pcap_struct/pcap_header.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

raw_sock.o: .././utils/raw_sock/raw_sock.c .././utils/raw_sock/raw_sock.h
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<

%.o:%.cpp
	$(CC) -c $(CPPFLAGS)  $(LIBS_CFLAGS) $<

%.o:%.c
	$(CC) -c $(CFLAGS)  $(LIBS_CFLAGS) $<


libgw_core.a: $(O_FILES) 
	$(AR) cr $@ $^

clean:
	rm -f *.o *~ libgw_core.a
