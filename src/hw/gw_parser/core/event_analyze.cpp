#include "event_analyze.h"


#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <arpa/inet.h>

#include "pp.h"
#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "gw_stats.h"

#define EVENT_MSG_QPS "event_analyse"
#define UPLOAD_ANALYZE_MSG_QPS "upload_analyse"


typedef struct thread_local_upload_msg_data
{

} thread_local_upload_msg_data_t;

CEventAnalyze * CEventAnalyze::m_event_analyze = NULL;

CEventAnalyze::CEventAnalyze() : m_comm(NULL)
                                , m_quit_signal(0)
                                , m_p_wq_event_msg(NULL)
                                , m_p_wq_upload_msg(NULL)
                                , m_event_queue_name{0}
                                , m_upload_queue_name{0}
                                , m_queue_priority(100)
                                , m_conf_event_msg_thread_num(1)
                                , m_conf_event_msg_queue_max_num(20000)
                                , m_conf_event_msg_queue_memory_max_size_bytes(536870912) // 512M
                                , m_conf_event_queue_buffering_max_messages(20000)
                                , m_conf_event_queue_buffering_max_kbytes(1048576)
                                , m_conf_event_batch_num_messages(1000)
                                , source_ip(0)
                                , dest_ip(0)
                                , dest_port(0)
                                , timeout(0)
                                , analyze_flag(0)
                                , log_event_file(NULL)
                                , log_upload_file(NULL)
{
    snprintf(m_event_queue_name, COUNTOF(m_event_queue_name) - 1, EVENT_MSG_QPS);
    snprintf(m_upload_queue_name, COUNTOF(m_upload_queue_name) - 1, UPLOAD_ANALYZE_MSG_QPS);
}

CEventAnalyze::~CEventAnalyze()
{
    set_quit_signal();
    wait_for_stop();
    fini();
}

CEventAnalyze& CEventAnalyze::instance(){
        static CEventAnalyze* m_event_analyze = NULL;
        if (unlikely(m_event_analyze == NULL))
        {
            m_event_analyze = new CEventAnalyze();
        }
        return *m_event_analyze;
    }

void CEventAnalyze::set_gw_common(CGwCommon *comm)
{
  if (NULL == m_comm)
  {
    m_comm = comm;
  }
}

void CEventAnalyze::put_event_msg(bool is_request,const char* content,int content_len)
{
    AnalyzeMsg* msg = new AnalyzeMsg();
    msg->time = m_comm->gw_real_time_ms();
    msg->is_data = false;
    msg->is_request = is_request;
    msg->len = content_len;
    put_msg(msg);
}

void CEventAnalyze::put_upload_msg(UploadMsg *p_msg)
{
    ASSERT(m_comm != NULL);

    if (p_msg == NULL)
    {
        return;
    }

    p_msg->upload = this;

    if (!m_p_wq_upload_msg->queue_put_data(p_msg, p_msg->mem_size))
    {
        if (p_msg->destroy_func != NULL)
        {
            (*p_msg->destroy_func)(p_msg);
        }
    }
}

void CEventAnalyze::put_msg(AnalyzeMsg *p_msg)
{
    ASSERT(m_comm != NULL);

    if (p_msg == NULL)
    {
        return;
    }

    //停止时，不写入队列
    if (dest_ip == 0)
    {
        delete p_msg;
        return;
    }

    if (check_timeout())
    {   
        delete p_msg;
        return;
    }

    if (!m_p_wq_event_msg->queue_put_data(p_msg, sizeof(AnalyzeMsg)))
    {
        delete p_msg;
    }
}

void CEventAnalyze::init()
{
    ASSERT(m_comm != NULL);

    m_quit_signal = 0;

    load_conf(NULL);
    CWorkerQueue *pwq = m_comm->create_worker_queue();

    if (pwq != NULL)
    {
        pwq->set_gw_common(m_comm);
        pwq->set_watchdog(m_comm->get_watchdog());

        pwq->set_queue_num_and_bytes(m_conf_event_msg_queue_max_num, m_conf_event_msg_queue_memory_max_size_bytes);
        pwq->set_queue_name(m_event_queue_name);
        pwq->set_queue_destroy_callback((q_destroy_func_t)free_analyze_msg);
        pwq->init();
        pwq->create_queue();
        pwq->create_thread(m_conf_event_msg_thread_num, worker_routine_analyse_msg, this);

        m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq, m_queue_priority);
        m_comm->get_gw_stats()->set_qps(EVENT_MSG_QPS, &pwq->get_stats_task_data()->cnt_succ);
        m_comm->get_gw_stats()->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());
    }

    m_p_wq_event_msg = pwq;


    CWorkerQueue *pwq2 = m_comm->create_worker_queue();

    if (pwq2 != NULL)
    {
        pwq2->set_gw_common(m_comm);
        pwq2->set_watchdog(m_comm->get_watchdog());

        pwq2->set_queue_num_and_bytes(m_conf_event_msg_queue_max_num, m_conf_event_msg_queue_memory_max_size_bytes);
        pwq2->set_queue_name(m_upload_queue_name);
        pwq2->set_queue_destroy_callback((q_destroy_func_t)free_upload_msg);
        pwq2->init();
        pwq2->create_queue();
        pwq2->create_thread(m_conf_event_msg_thread_num, worker_routine_upload_msg, this);

        m_comm->get_gw_stats()->set_task(pwq2->get_queue_name(), pwq2, m_queue_priority);
        m_comm->get_gw_stats()->set_qps(UPLOAD_ANALYZE_MSG_QPS, &pwq2->get_stats_task_data()->cnt_succ);
        m_comm->get_gw_stats()->set_mem_stat(pwq2->get_queue_name(), &pwq2->get_queue_mem_size(), &pwq2->get_queue_max_mem_size());
    }

    m_p_wq_upload_msg = pwq2;
}

void CEventAnalyze::fini()
{
    if (m_comm == NULL)
    {
        return;
    }

    free_worker_queue(m_p_wq_event_msg);
    m_p_wq_event_msg = NULL;
    free_worker_queue(m_p_wq_upload_msg);
    m_p_wq_upload_msg = NULL;
    m_comm = NULL;
}

void CEventAnalyze::run()
{
    ASSERT(m_comm != NULL);

    if (m_p_wq_event_msg != NULL)
    {
        m_p_wq_event_msg->run();
    }
    if (m_p_wq_upload_msg != NULL)
    {
        m_p_wq_upload_msg->run();
    }
}

/**
 * 加载配置参数（Json字符串，支持动态）。
 * @param const char *
 */
bool CEventAnalyze::load_conf(const char *str_json)
{
    ASSERT(m_comm != NULL);
    CGwConfig *p_conf = m_comm->get_gw_config();

    m_str_stats_path = p_conf->read_conf_string("parser", "analyze_dir");
    if (m_str_stats_path.empty())
    {
        m_str_stats_path = "/opt/analyze/";
    }
    
    uint32_t flag = p_conf->read_conf_int("parser","analyze_flag",0);
    if (flag ==0 && analyze_flag >0)
    {
        analyze_flag++;
    }else if ((flag > 0) && (flag !=analyze_flag) && (flag != analyze_flag-1))
    {
        analyze_flag = flag;
    }

    timeout = p_conf->read_conf_int("parser","analyze_timeout",0);
    if (timeout ==0)
    {
        timeout = time(NULL)+600;
    }
    

    std::string ip = p_conf->read_conf_string("parser","analyze_dest_ip");
    if(ip.length()>0){
        dest_ip = inet_addr(ip.c_str());
    }else{
        dest_ip = 0;
        timeout = time(NULL);
    }
    ip = p_conf->read_conf_string("parser","analyze_source_ip");
    if(ip.length()>0){
        source_ip = inet_addr(ip.c_str());
    }else{
        source_ip = 0;
    }
    dest_port = p_conf->read_conf_int("parser","analyze_dest_port",0);
    if (dest_ip ==0 || time(NULL)>timeout )
    {   
        if (log_event_file!=NULL)
        {
            fclose(log_event_file);
            log_event_file = NULL;
        }  
        if (log_upload_file!=NULL)
        {
            fclose(log_upload_file);
            log_upload_file = NULL;
        }
        
    }
    else
    {
        if (log_event_file == NULL)
        {
            char full_path[1024];
            sprintf(full_path,"%s/event_analyze.txt",m_str_stats_path.c_str());
            log_event_file = fopen(full_path,"w+");
        }
        if (log_upload_file == NULL)
        {   
            char full_path[1024];
            sprintf(full_path,"%s/upload_analyze.txt",m_str_stats_path.c_str());
            log_upload_file = fopen(full_path,"w+");
        }
        
    }
    
    
    // m_conf_upload_msg_thread_num = p_conf->read_conf_int("parser", "upload_thread_num", m_conf_upload_msg_thread_num);
    // m_conf_upload_msg_queue_max_num = p_conf->read_conf_int("parser", "upload_queue_num", m_conf_upload_msg_queue_max_num);
    // // m_conf_upload_msg_queue_memory_max_size_bytes = 100 * 1024ULL * 1024ULL;
    // m_conf_upload_msg_queue_memory_max_size_bytes = p_conf->read_conf_int("parser", "queue_upload_msg_memory_size", m_conf_upload_msg_queue_memory_max_size_bytes/(1024UL*1024UL)) * (1024UL*1024UL);

    // m_conf_kafka_queue_buffering_max_messages = p_conf->read_conf_int("kafka", "queue_buffering_max_messages", m_conf_kafka_queue_buffering_max_messages);
    // m_conf_kafka_queue_buffering_max_kbytes = p_conf->read_conf_int_x("kafka", "queue_buffering_max_kbytes", m_conf_kafka_queue_buffering_max_kbytes);
    // m_conf_kafka_batch_num_messages = p_conf->read_conf_int("kafka", "batch_num_messages", m_conf_kafka_batch_num_messages);

    return true;
}

/**
 * 触发退出信号时处理
 */
void CEventAnalyze::set_quit_signal(void)
{
    ASSERT(m_comm != NULL);

    m_quit_signal = 1;
    if (m_p_wq_event_msg != NULL)
    {
        m_p_wq_event_msg->set_quit_signal();
    }
    if (m_p_wq_upload_msg != NULL)
    {
        m_p_wq_upload_msg->set_quit_signal();
    }
}

/**
 * 等待运行结束
 */
void CEventAnalyze::wait_for_stop(void)
{
    ASSERT(m_comm != NULL);

    if (m_p_wq_event_msg != NULL)
    {
        m_p_wq_event_msg->wait_for_stop();
    }
    if (m_p_wq_upload_msg != NULL)
    {
        m_p_wq_upload_msg->wait_for_stop();
    }
}

void CEventAnalyze::free_worker_queue(CWorkerQueue *p)
{
    if (p)
    {
        p->set_quit_signal();
        p->wait_for_stop();

        p->delete_queue();

        p->fini();

        if (m_comm != NULL)
        {
            m_comm->destory_worker_queue(p);
        }
    }
}

void CEventAnalyze::free_analyze_msg(AnalyzeMsg *p)
{
    delete p;
}


void CEventAnalyze::free_upload_msg(UploadMsg *p){
    if (p->destroy_func != NULL)
    {
        (*p->destroy_func)(p);
    }
}



int CEventAnalyze::worker_routine_upload_msg(void *args_ptr, worker_routine_param *pwrp, void *p)
{
    CEventAnalyze *pThis = (CEventAnalyze *)args_ptr;
    ASSERT(pThis != NULL);
    thread_local_upload_msg_data_t *tlumd = (thread_local_upload_msg_data_t *)pwrp->data;

    switch (pwrp->step)
    {
    case WQ_WR_BEGIN:
        if (tlumd == NULL)
        {
            pwrp->data = tlumd = new thread_local_upload_msg_data_t;
            // tlumd; 
        }
        break;

    case WQ_WR_END:
        delete tlumd;
        pwrp->data = NULL;
        break;

    case WQ_WR_DATA:
        return pThis->worker_routine_upload_msg_inner(tlumd, p);
        break;

    default:
        break;
    }

    return 0;
}




int CEventAnalyze::worker_routine_upload_msg_inner(thread_local_upload_msg_data *tlumd, void *p)
{
    UploadMsg *p_um = (UploadMsg *)p;
    // CParser *parser = (CParser *)p_um->parser;
    // ASSERT(parser != NULL);


    // 不马上停止写入，队列中可能还有数据等待写入
    // if (dest_ip == 0)
    // {   
    //     if (log_upload_file)
    //     {
    //         fclose(log_upload_file);
    //         log_upload_file = NULL;
    //     }
    //     return 0;
    // }
    

    if (log_upload_file == NULL)
    {
        char full_path[1024];
        sprintf(full_path,"%s/upload_analyze.txt",m_str_stats_path.c_str());
        log_upload_file = fopen(full_path,"w+");
    }

    if (p_um->upload_success)
    {
        fwrite(p_um->s,p_um->length,1,log_upload_file);
        fprintf(log_upload_file,"\n%12s\n","success");
        fflush(log_upload_file);
    }
    
    //滞后10秒钟停止写入
    if (time(NULL)>timeout + 10)
    {
        if (log_upload_file)
        {
            fclose(log_upload_file);
            log_upload_file = NULL;
        }
    }
    return 0;
}

int CEventAnalyze::worker_routine_analyse_msg(void *args_ptr, worker_routine_param *pwrp, void *p)
{
    CEventAnalyze *pThis = (CEventAnalyze *)args_ptr;
    ASSERT(pThis != NULL);
    thread_local_upload_msg_data_t *tlumd = (thread_local_upload_msg_data_t *)pwrp->data;

    switch (pwrp->step)
    {
    case WQ_WR_BEGIN:
        if (tlumd == NULL)
        {
            pwrp->data = tlumd = new thread_local_upload_msg_data_t;
            // tlumd;
        }
        break;

    case WQ_WR_END:
        delete tlumd;
        pwrp->data = NULL;
        break;

    case WQ_WR_DATA:
        return pThis->worker_routine_analyse_msg_inner(tlumd, p);
        break;

    default:
        break;
    }

    return 0;
}

int CEventAnalyze::worker_routine_analyse_msg_inner(thread_local_upload_msg_data *tlumd, void *p)
{
    AnalyzeMsg *msg = (AnalyzeMsg *)p;

    // if (dest_ip == 0)
    // {   
    //     if (log_event_file)
    //     {
    //         fclose(log_event_file);
    //         log_event_file = NULL;
    //     }
    //     return 0;
    // }

    if (log_event_file == NULL)
    {
        char full_path[1024];
        sprintf(full_path,"%s/event_analyze.txt",m_str_stats_path.c_str());
        log_event_file = fopen(full_path,"w+");
    }
    
    time_t timep = msg->time/1000;
    char tmp[64];
    strftime(tmp,sizeof(tmp),"%Y-%m-%d %H:%M:%S",localtime(&timep));

    fprintf(log_event_file,"%24s.%03ld | %12s | %12s | %12d\n",tmp,msg->time%1000,msg->is_data?"data":"event",msg->is_request?"req":"rsp",msg->len);
    fflush(log_event_file);

    if (time(NULL)>timeout+10)
    {
        if (log_event_file)
        {
            fclose(log_event_file);
            log_event_file = NULL;
        }
    }
    return 0;
}

bool CEventAnalyze::check_analyze_stream(struct tcp_stream *a_tcp){
    if (dest_ip == 0)
    {
        return false;
    }
    
    if (dest_ip != a_tcp->addr.daddr)
    {
        return false;
    }
    
    if (source_ip >0 && source_ip != a_tcp->addr.saddr)
    {
        return false;
    }

    if (dest_port>0 && dest_port != a_tcp->addr.dest)
    {
        return false;
    }
    

    return true;
}

bool CEventAnalyze::check_timeout(){
    if (time(NULL)>timeout)
    {
        analyze_flag++;
        dest_ip = 0;
        return true;
    }
    return false;
}


bool CEventAnalyze::check_analyze_log(uint32_t sip,uint32_t dip,uint16_t dport){
    //停止或超时，返回false，决定upload后不写入队列
    if (dest_ip == 0)
    {
        return false;
    }

    if (check_timeout())
    {
        return false;
    }
    
    
    if (dest_ip != dip)
    {
        return false;
    }
    
    if (source_ip >0 && source_ip != sip)
    {
        return false;
    }

    if (dest_port>0 && dest_port != dport)
    {
        return false;
    }

    return true;
}

