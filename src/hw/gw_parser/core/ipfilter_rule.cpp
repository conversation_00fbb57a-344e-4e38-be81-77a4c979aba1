/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <string.h>
#include <stdlib.h>
#include <ctype.h>
#include <arpa/inet.h>
#include <stdio.h>
#include <mutex>

#include "ipfilter_rule.h"
#include "utils.h"
#include "gw_logger.h"
#include "gw_common.h"
#include "gw_config.h"
#include "tcp_parser.h"
#include "nacos_listen_conf.h"

#define V6_MAX_COLON_NUM (7)

#define REVERSE_BYTES_2(x) (((x) & 0x00ff) << 8 | ((x) & 0xff00) >> 8)
int ipv6_2_ipv4(uint16_t* v6, uint32_t* v4) 
{
  if (v6[0] | v6[1] | v6[2] | v6[3] | v6[4] | v6[5]) 
  {
    return 0;
  }
  *v4 = REVERSE_BYTES_2(v6[6]) << 16 | REVERSE_BYTES_2(v6[7]);
  return 1;
}

int ipv4_2_ipv6(uint32_t* v4, uint16_t* v6) 
{
  v6[6] = REVERSE_BYTES_2(*v4 >> 16);
  v6[7] = REVERSE_BYTES_2(*v4 & 0x0000ffff);
  return 1;
}

CIpfilterRule::CIpfilterRule() : m_comm(NULL)
                               , m_tcpparser(NULL)
                               , m_conf_ip_filter_ptr(NULL)
                               , m_conf_ip_white_ptr(NULL)
{
}

CIpfilterRule::~CIpfilterRule()
{
    fini();
}

 /**
 * 命中过滤规则。
 * @param unsigned int data
 */
int CIpfilterRule::hit(unsigned data)
{
    return 0;
}

void CIpfilterRule::init()
{
    ASSERT(m_comm != NULL);
    ASSERT(m_tcpparser != NULL);
}

void CIpfilterRule::fini()
{
    free_conf_ip(m_conf_ip_filter_ptr);
    m_conf_ip_filter_ptr = NULL;
    free_conf_ip(m_conf_ip_white_ptr);
    m_conf_ip_white_ptr = NULL;
}

/**
 * 设置全局公共类对象实例。
 * @param CGwCommon *comm
 */
void CIpfilterRule::set_gw_common(CGwCommon *comm)
{
    m_comm = comm;
}

/**
 *  设置TcpParser对象实例 
 *  @paramount CTcpParser *tcpparser
 */
void CIpfilterRule::set_tcp_parser(CTcpParser *tcpparser)
{
    m_tcpparser = tcpparser;
}

/**
 * 设置过滤IP参数
 * @param const char*
 */ 
void CIpfilterRule::set_ip_filter(const char *p_ip_filter)
{
    int i_ret = 0;
    free_conf_ip(m_conf_ip_filter_ptr);
    m_conf_ip_filter_ptr = (conf_ip_filter_t*)malloc(sizeof(conf_ip_filter_t));
    if (NULL == m_conf_ip_filter_ptr)
    {
        return ;
    }
    memset(m_conf_ip_filter_ptr, 0, sizeof(conf_ip_filter_t));

    i_ret = read_conf_ip_inner(p_ip_filter, m_conf_ip_filter_ptr);
    if (i_ret != 0)
    {
        free_conf_ip(m_conf_ip_filter_ptr);
    }

    return;
}

/**
 * 设置白名单IP参数
 * @param const char*
 */ 
void CIpfilterRule::set_ip_white(const char *p_ip_white)
{
    int i_ret = 0;
    free_conf_ip(m_conf_ip_white_ptr);
    m_conf_ip_white_ptr = (conf_ip_filter_t*)malloc(sizeof(conf_ip_filter_t));
    if (NULL == m_conf_ip_white_ptr)
    {
        return ;
    }
    memset(m_conf_ip_white_ptr, 0, sizeof(conf_ip_filter_t));

    i_ret = read_conf_ip_inner(p_ip_white, m_conf_ip_white_ptr);
    if (i_ret != 0)
    {
        free_conf_ip(m_conf_ip_white_ptr);
    }

    return;
}

/**
 * 过滤IPv4地址
 * @param unsigned 
 * @return  1 命中 0 未命中
 */
int CIpfilterRule::ip_filter_hit(unsigned addr)
{
    return ip_hit_inner(m_conf_ip_filter_ptr, addr);
}

/**
 * 过滤IPv6地址
 * @param uint16_t*
 * @return 1 命中 0 未命中
 */
int CIpfilterRule::ip6_filter_hit(uint16_t *p_ipv6_addr)
{
    return ip6_hit_inner(m_conf_ip_filter_ptr, p_ipv6_addr);
}

/**
 * IPv4白名单
 * @param unsigned
 * return 1 命中 0 未命中 
 */
int CIpfilterRule::ip_white_hit(unsigned addr)
{
    return ip_hit_inner(m_conf_ip_white_ptr, addr);
}

/**
 * IPv6白名单
 * @param uint16_t*
 * @return 1 命中 0 未命中
 */
int CIpfilterRule::ip6_white_hit(uint16_t *p_ipv6_addr)
{
    return ip6_hit_inner(m_conf_ip_white_ptr, p_ipv6_addr);
}

 /**
 *  IP过滤参数动态更新 
 */
void CIpfilterRule::ip_filter_for_mon(int is_for_nacos, const char *s)
{
    if (!is_for_nacos)
    {
        ASSERT(m_comm != NULL);
        ASSERT(m_tcpparser != NULL);
        CGwConfig *pgwc = m_comm->get_gw_config();
        ASSERT(pgwc != NULL);
        std::string str_ip_filter = pgwc->read_conf_string("parser", "ip_filter");

        read_conf_ip_for_mon_inner(&m_conf_ip_filter_ptr, str_ip_filter.c_str());
    }
    else
    {
        read_conf_ip_for_mon_inner(&m_conf_ip_filter_ptr, s);
    }

    return;
}

/**
 *  IP白名单参数动态更新 
 */
void CIpfilterRule::ip_white_for_mon(int is_for_nacos, const char *s)
{
    if (!is_for_nacos)
    {
        ASSERT(m_comm != NULL);
        ASSERT(m_tcpparser != NULL);
        CGwConfig *pgwc = m_comm->get_gw_config();
        ASSERT(pgwc != NULL);
        std::string str_ip_white = pgwc->read_conf_string("parser", "ip_white");

        read_conf_ip_for_mon_inner(&m_conf_ip_white_ptr, str_ip_white.c_str());
    }
    else
    {
        read_conf_ip_for_mon_inner(&m_conf_ip_white_ptr, s);
    }

}

int CIpfilterRule::read_conf_ip_inner(const char *s, conf_ip_filter_t *conf_ptr)
{
  size_t length;
  int i_ret = 0;
  int mask_bits = 0;
  unsigned int mask = 0;
  unsigned int addr1 = 0;
  unsigned int addr2 = 0;
  uint16_t a_u16_v6_min_addr[8] = {0};
  uint16_t a_u16_v6_max_addr[8] = {0};
  uint16_t a_u16_v6_mask[8] = {0};
  unsigned int addr = 0;
  char *ss = NULL;
  const char *p = NULL;
  const char *p1 = s;
  const char *p2 = NULL;
  const char *pp = NULL;
  const char *pp1 = NULL;
  const char *pp2 = NULL;
  int k = 0;
  char buf[256] = {0};
  ip_filter_data_t *pda = NULL;

  // "***********/24,***********,***********-*************"
  if (s == NULL)
  {
    return 1;
  }
  ss = (char *)s;
  while ((ss = strchr(ss, ',')) != NULL)
  {
    ss++;
    conf_ptr->conf_ip_filter_num++;
  }
  conf_ptr->conf_ip_filter_num++;
  conf_ptr->conf_ip_filter_addr = (ip_filter_data_t *)malloc(conf_ptr->conf_ip_filter_num * sizeof(ip_filter_data_t));
  if (conf_ptr->conf_ip_filter_addr == NULL)
  {
    conf_ptr->conf_ip_filter_num = 0;
    return 1;
  }
  memset(conf_ptr->conf_ip_filter_addr, 0, conf_ptr->conf_ip_filter_num * sizeof(ip_filter_data_t));

  while (p1)
  {
    // 去除 ',' 后对空格
    if (*p1 == ' ')
    {
      p1++;
      continue;
    }

    memset(buf, 0, 256);
    p = strchr(p1, ',');
    p2 = (p == NULL) ? (p1 + strlen(p1)) : p++;
    length = p2 - p1;
    if (length >= sizeof(buf) || length == 0)
    {
      p1 = p;
      continue;
    }
    memcpy(buf, p1, length);
    p1 = p;
    buf[length] = '\0';

    /* 判断是否是IPv6地址 */
    char *p_tmp = strchr(buf, ':');
    if (p_tmp)
    {
      /* xx:xx:xx:xx:xx:xx:xx:xx/64, xx:xx:xx:xx:xx:xx:xx:xx, xx:xx:xx:xx:xx:xx:xx:xx-xx:xx:xx:xx:xx:xx:xx:xx */
      pp1 = strchr(buf, '-');
      pp2 = strchr(buf, '/');
      if (pp1 && pp2)
      {
        continue;
      }

      if (pp1)
      {
        /* xx:xx:xx::xx-xx:xx:xx::xx */
        i_ret = get_ipv6_addr(buf, pp1 - buf, a_u16_v6_min_addr);
        if (i_ret != 0)
        {
          continue;
        }

        i_ret = get_ipv6_addr(pp1 + 1, strlen(pp1 + 1), a_u16_v6_max_addr);
        if (i_ret != 0)
        {
          continue;
        }
      }
      else if (pp2)
      {
        /* xx:xx:xx::xx/64 */
        i_ret = get_ipv6_addr(buf, pp2 - buf, a_u16_v6_min_addr);
        if (i_ret != 0)
        {
          continue;
        }

        for (pp = pp2 + 1; *pp && isdigit(*pp); pp++)
        {

        }

        if (*pp)
        {
          continue;
        }
        mask_bits = atoi(pp2 + 1);
        if (!(mask_bits > 0 && mask_bits <= 128))
        {
          continue;
        }
        int i = 0;
        int mask_group = 0;

        if (mask_bits % 16 == 0)
        {
          mask_group = mask_bits / 16;
          
          for (i = 0; i < mask_group; ++i)
          {
            a_u16_v6_mask[i] = 0xffff;
          }

          for (; i < 8; ++i)
          {
            a_u16_v6_mask[i] = 0;
          }
        }
        else
        {
          mask_group = mask_bits / 16;
          int reman = mask_bits % 16;
          uint16_t u16_mask = 0;
          for (i = 0; i < mask_group; ++ i)
          {
            a_u16_v6_mask[i] = 0xffff;
          }

          u16_mask =  ~((1 << (16 - reman)) - 1);
          a_u16_v6_mask[i] = u16_mask;
          for (i = i + 1; i < 8; ++i)
          {
            a_u16_v6_mask[i] = 0;
          }
        }

        for (i = 0; i < 8; ++i)
        {
          a_u16_v6_min_addr[i] &= a_u16_v6_mask[i];
          a_u16_v6_max_addr[i] = a_u16_v6_min_addr[i] | ~a_u16_v6_mask[i];
        }
      }
      else
      {
        /* xx:xx:xx:xx::xx, ::*********** */
        i_ret = get_ipv6_addr(buf, length, a_u16_v6_min_addr);
        if (i_ret != 0)
        {
          continue;
        }
        memcpy(a_u16_v6_max_addr, a_u16_v6_min_addr, sizeof(a_u16_v6_min_addr));
      }

      i_ret = compare_ipv6_filter_data(a_u16_v6_min_addr, a_u16_v6_max_addr);
      if (i_ret > 0)
      {
        continue;
      }

      // add addr list
      if (k < conf_ptr->conf_ip_filter_num)
      {
        pda = &conf_ptr->conf_ip_filter_addr[k++];
        // pda->i_ip_type = 1;
        memcpy(pda->a_ipv6_addr_min, a_u16_v6_min_addr, sizeof(a_u16_v6_min_addr));
        memcpy(pda->a_ipv6_addr_max, a_u16_v6_max_addr, sizeof(a_u16_v6_max_addr));
        pda->is_v4 = ipv6_2_ipv4(pda->a_ipv6_addr_max, &pda->addr_max) && ipv6_2_ipv4(pda->a_ipv6_addr_min, &pda->addr_min);
      }
    }
    else
    {
      // 逗号分隔
      pp1 = strchr(buf, '-');
      pp2 = strchr(buf, '/');
      if (pp1 && pp2)
      {
        continue;
      }
      if (pp1)
      {
        // ***********-*************
        addr1 = get_ip_addr(buf, pp1 - buf);
        addr2 = get_ip_addr(pp1 + 1, strlen(pp1 + 1));
        if (addr1 == INADDR_NONE || addr2 == INADDR_NONE)
        {
          continue;
        }
      }
      else if (pp2)
      {
        // ***********/24
        addr = get_ip_addr(buf, pp2 - buf);
        if (addr1 == INADDR_NONE)
        {
          continue;
        }
        for (pp = pp2 + 1; *pp && isdigit(*pp); pp++)
        {
        }
        if (*pp)
        {
          continue;
        }
        // 掩码长度全部为数字字符
        mask_bits = atoi(pp2 + 1);
        if (!(mask_bits > 0 && mask_bits <= 32))
        {
          continue;
        }
        // 11110000
        mask = ~((1 << (32 - mask_bits)) - 1);
        addr1 = addr & mask;
        addr2 = addr1 | ~mask;
      }
      else
      {
        // ***********
        addr = get_ip_addr(buf, length);
        if (addr == INADDR_NONE)
        {
          continue;
        }
        addr1 = addr2 = addr;
      }
      if (addr1 > addr2)
      {
        continue;
      }

      // add addr list
      //printf("%d %.8X %.8X\n", k, addr1, addr2);
      if (k < conf_ptr->conf_ip_filter_num)
      {
        pda = &conf_ptr->conf_ip_filter_addr[k++];
        pda->is_v4 = 1;
        pda->addr_min = addr1;
        pda->addr_max = addr2;
        ipv4_2_ipv6(&pda->addr_max, pda->a_ipv6_addr_max);
        ipv4_2_ipv6(&pda->addr_min, pda->a_ipv6_addr_min);
      }
    }
    
  }

  conf_ptr->conf_ip_filter_last = k;
  for (; k < conf_ptr->conf_ip_filter_num; k++)
  {
    pda = &conf_ptr->conf_ip_filter_addr[k];
    pda->is_v4 = 1;
    pda->addr_min = INADDR_NONE;
    pda->addr_max = INADDR_NONE;
    ipv4_2_ipv6(&pda->addr_max, pda->a_ipv6_addr_max);
    ipv4_2_ipv6(&pda->addr_min, pda->a_ipv6_addr_min);
  }

  // 按 addr_min 进行升序排
  //qsort(conf_ptr->conf_ip_filter_addr, conf_ptr->conf_ip_filter_last, sizeof(conf_ptr->conf_ip_filter_addr[0]), compare_ip_filter_data);
  qsort(conf_ptr->conf_ip_filter_addr, conf_ptr->conf_ip_filter_last, sizeof(conf_ptr->conf_ip_filter_addr[0]), compare_ipv6_filter_data);
  return 0;
}

void CIpfilterRule::free_conf_ip(conf_ip_filter_t *p_conf_ip)
{
    if (NULL == p_conf_ip)
    {
        return ;
    }

    SAFE_FREE(p_conf_ip->conf_ip_filter_addr);
    free(p_conf_ip);
}

int CIpfilterRule::get_ipv6_addr(const char *p_buf, size_t length, uint16_t *p_v6_addr)
{
    size_t i = 0; 
    size_t v6_group_index = 0;
    size_t v6_group_len = 0;
    uint32_t u32_v6_group_index = 0;
    int i_colon_num = 0; 
    char *p_tmp = (char *)p_buf;
    int cnt = 0;

    while (   ((p_tmp = strchr(p_tmp, '.')) != NULL)
           && ((unsigned)(p_tmp - p_buf) < length) )
    {
        cnt++;
        p_tmp = p_tmp + 1;
    }

    if (cnt > 0)
    {
        if (cnt != 3)
        {
            return -1;
        }
        else
        {
            i_colon_num ++;
        }
    }

    p_tmp = (char*)p_buf;
    while (((p_tmp = strchr(p_tmp, ':')) != NULL) 
          && ((unsigned)(p_tmp - p_buf) < length))
    {
        p_tmp ++;
        i_colon_num++;
    }

    if (i_colon_num > V6_MAX_COLON_NUM)
    {
        return -1;
    }
    else if (i_colon_num < V6_MAX_COLON_NUM)
    {
        int flag = 0;
        for (i = 0; i < length -1; ++i)
        {
            if (p_buf[i] == ':' && p_buf[i+1] == ':')
            {
                flag = 1;
                break;
            }
        }

        if (!flag)
        {
            return -1;
        }
    }

    for (i = 0; i < length; ++i)
    {
        if (p_buf[i] == ':' || i == length - 1)
        {
            if (i == length - 1)
            {
                if ( !(isdigit(p_buf[i]) || (p_buf[i] >= 'a' && p_buf[i] <= 'f') || (p_buf[i] >= 'A' && p_buf[i] <= 'F') ))
                {
                    return -1;
                }
                i = length;
            }
            /* 计算长度 */
            v6_group_len = i - v6_group_index;
        
            /* 缩写表示方法 */
            if (v6_group_len == 0)
            {
                int i_add_group_num = V6_MAX_COLON_NUM + 1 - i_colon_num;
                if ( i+1 < length && p_buf[i+1] == ':')
                {
                    i_add_group_num += 1;
                    i++;
                }
                while (i_add_group_num-- > 0)
                {
                    p_v6_addr[u32_v6_group_index++]=0;
                }
            }
            else
            {
                if (v6_group_len <= 4)
                {
                    int j = 0;
                    uint16_t u16_num = 0;
                    uint16_t u16_result = 0;
                    uint8_t *p_u8_result = (uint8_t *) &u16_result;
                    int result_index = 3;
                    for (j = v6_group_len - 1; j >= 0; j--, result_index--)
                    {
                        size_t index = i + j - v6_group_len;
                        
                        if ( p_buf[index] >= '0' && p_buf[index] <= '9' )
                        {
                            u16_num = p_buf[index] - 48;
                        }
                        else if ( p_buf[index] >= 'a' && p_buf[index] <= 'f' )
                        {
                            u16_num = p_buf[index] -'a' + 10;
                        }
                        else if ( p_buf[index] >= 'A' && p_buf[index] <= 'F')
                        {
                            u16_num = p_buf[index] -'A' + 10;
                        }

                        // 下标为偶数时需要位移
                        p_u8_result[result_index >> 1] |= u16_num << (((result_index & 0x01) ^ 0x01) * 4);
                    }

                    p_v6_addr[u32_v6_group_index++] = u16_result;
                }
                else if (v6_group_len > 4 && u32_v6_group_index == 6)
                {
                    char a_buf[24] = {0};
                    uint32_t u32_ip = 0;
                    memcpy(a_buf, p_buf + v6_group_index, v6_group_len);
                    u32_ip = inet_addr(a_buf);
                    memcpy(&p_v6_addr[u32_v6_group_index++], &u32_ip, 2);
                    memcpy(&p_v6_addr[u32_v6_group_index++], (char*)&u32_ip + 2, 2);
                }
                else
                {
                    return -1;
                }
            }

            v6_group_index = i + 1;
        }
        else if (!(isdigit(p_buf[i]) || (p_buf[i] >= 'a' && p_buf[i] <= 'f') || (p_buf[i] >= 'A' && p_buf[i] <= 'F') || p_buf[i] == '.' ))
        {
            return -1;
        }

    }

    return 0;
}

int CIpfilterRule::compare_ipv6_filter_data(const void *a, const void *b)
{
  return memcmp(a, b, 16);
}

int CIpfilterRule::ip_hit_inner(conf_ip_filter_t *conf_ptr, unsigned addr)
{
    int i = 0;
    int ret = 0; // 1 命中;  0 未命中
    if (conf_ptr == NULL)
    {
        return 0;
    }

    if (conf_ptr->conf_ip_filter_addr == NULL || conf_ptr->conf_ip_filter_num == 0)
    {
        return 0;
    }
    if (addr == INADDR_ANY || addr == INADDR_NONE)
    {
        // 无效IP直接命中
        return 1;
    }
    for (i = 0; i < conf_ptr->conf_ip_filter_last; i++)
    {
        ip_filter_data_t *pda = &conf_ptr->conf_ip_filter_addr[i];
        if (pda->is_v4 == 0)
        {
            continue;
        }
        
        if (addr >= pda->addr_min && addr <= pda->addr_max)
        {
            // 命中
            return 1;
        }
    }

    return ret;
}

int CIpfilterRule::ip6_hit_inner(conf_ip_filter_t *conf_ptr, uint16_t *p_ipv6_addr)
{
    int i = 0;
    // size_t j = 0;
    uint16_t a_u16_in6addr_any[8] = {0};  /* IPv6地址 any */

    if (conf_ptr == NULL)
    {
        return 0;
    }

    if (conf_ptr->conf_ip_filter_addr == NULL || conf_ptr->conf_ip_filter_num == 0)
    {
        return 0;
    }

    if ( (memcmp(p_ipv6_addr, a_u16_in6addr_any, sizeof(a_u16_in6addr_any)) == 0) )
    {
        return 1;
    }

    for (i = 0; i < conf_ptr->conf_ip_filter_last; i++)
    {
        ip_filter_data_t *pda = &conf_ptr->conf_ip_filter_addr[i];
        
        if ( memcmp(p_ipv6_addr, pda->a_ipv6_addr_min, sizeof(pda->a_ipv6_addr_min)) >= 0 && memcmp(p_ipv6_addr, pda->a_ipv6_addr_max, sizeof(pda->a_ipv6_addr_max)) <= 0)
        {
            return 1;
        }
        
    }

    return 0;
}

// 转换为IP地址·
int CIpfilterRule::get_ip_addr(const char *s, size_t length)
{
  size_t i;
  size_t cnt = 0;
  char buf[64] = {0};
  unsigned int addr;

  if (length >= sizeof(buf))
  {
    return INADDR_NONE;
  }
  for (i = 0; i < length; i++)
  {
    if (s[i] == '.')
    {
      cnt++;
    }
    else if (!isdigit(s[i]))
    {
      return INADDR_NONE;
    }
  }
  if (cnt != 3)
  {
    return INADDR_NONE;
  }
  memcpy(buf, s, length);
  addr = ntohl(inet_addr(buf));
  //printf("addr=%.8X\n", addr);
  return addr;
}

 void CIpfilterRule::read_conf_ip_for_mon_inner(conf_ip_filter_t **_p_conf_ptr, const char *s)
 {
    conf_ip_filter_t *conf_ptr = NULL;

    conf_ptr = (conf_ip_filter_t *)malloc(sizeof(conf_ip_filter_t));

    if (conf_ptr == NULL)
    {
        return;
    }
    memset(conf_ptr, 0, sizeof(conf_ip_filter_t));

    if (0 == read_conf_ip_inner(s, conf_ptr))
    {
        std::lock_guard<std::mutex> lock(m_conf_mutex);
        conf_ip_filter_t *old_conf_ptr = *_p_conf_ptr;
        *_p_conf_ptr = conf_ptr;

        // 等待旧配置不在使用
        if (m_tcpparser->wait_for_worker_use_conf())
        {
            return;
        }
        free_conf_ip(old_conf_ptr);
        return;
    }

    free_conf_ip(conf_ptr);

 }