/*
  * Copyright 2010-2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.
  *
  * Licensed under the Apache License, Version 2.0 (the "License").
  * You may not use this file except in compliance with the License.
  * A copy of the License is located at
  *
  *  http://aws.amazon.com/apache2.0
  *
  * or in the "license" file accompanying this file. This file is distributed
  * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
  * express or implied. See the License for the specific language governing
  * permissions and limitations under the License.
  */
#pragma once

#include <aws/core/Core_EXPORTS.h>
#include <aws/core/utils/memory/stl/AWSVector.h>
#include <aws/core/utils/memory/stl/AWSString.h>
#include <aws/core/utils/memory/stl/AWSMap.h>
#include <functional>

namespace Aws
{

namespace FileSystem
{
    struct DirectoryEntry;
    class Directory;

    #ifdef _WIN32
        static const char PATH_DELIM = '\\';
    #else
        static const char PATH_DELIM = '/';
    #endif

    /**
    * Returns the directory path for the home dir env variable
    */
    AWS_CORE_API Aws::String GetHomeDirectory();

    /**
     * Returns the directory path for the directory containing the currently running executable
     */
    AWS_CORE_API Aws::String GetExecutableDirectory();

    /**
    * Creates directory if it doesn't exist. Returns true if the directory was created
    * or already exists. False for failure.
    */
    AWS_CORE_API bool CreateDirectoryIfNotExists(const char* path);

    /**
    * Creates directory if it doesn't exist. Returns true if the directory was created
    * or already exists. False for failure.
    */
    AWS_CORE_API bool RemoveDirectoryIfExists(const char* path);

    /**
    * Deletes file if it exists. Returns true if file doesn't exist or on success.
    */
    AWS_CORE_API bool RemoveFileIfExists(const char* fileName);

    /**
    * Moves the file. Returns true on success
    */
    AWS_CORE_API bool RelocateFileOrDirectory(const char* from, const char* to);

    /**
     * Copies a directory and all of its contents.
     */
    AWS_CORE_API bool DeepCopyDirectory(const char* from, const char* to);

    /**
     * Deletes a directory and all of its contents.
     */
    AWS_CORE_API bool DeepDeleteDirectory(const char* toDelete);

    /**
    * Computes a unique tmp file path
    */
    AWS_CORE_API Aws::String CreateTempFilePath();

    /**
     * Opens a directory for traversal.
     */
    AWS_CORE_API std::shared_ptr<Directory> OpenDirectory(const Aws::String& path, const Aws::String& relativePath = "");

    /**
     * Joins the leftSegment and rightSegment of a path together using platform specific delimiter.
     * e.g. C:\users\<USER>\ and .aws becomes C:\users\<USER>\.aws
     */
    AWS_CORE_API Aws::String Join(const Aws::String& leftSegment, const Aws::String& rightSegment);

	/**
	* Joins the leftSegment and rightSegment of a path together using the specified delimiter.
	* e.g. with delimiter & C:\users\<USER>\ and .aws becomes C:\users\<USER>