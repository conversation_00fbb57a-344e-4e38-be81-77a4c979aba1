/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __MONGO_PARSER_WIRE_PROTOCOL_TASK_WORKER_H__
#define __MONGO_PARSER_WIRE_PROTOCOL_TASK_WORKER_H__

#include "utils.h"

#include "mongo_parser_task_worker.h"

#include "mongo_parser_op.h"

class CSession;
// mongo wire protocal数据结构
typedef struct mongo_wire_protocol
{
  TaskWorkerData twd;

  ConnData conn; // 网络连接会话

  int dir;         // 数据方向
  // int64_t pcap_ts; // 事件时间(毫秒时间戳)
  CSession* p_session;
  int32 length;    // 数据长度
  union {
    char *s; // 数据指针
    const msg_header_t *pmh;
  };

} mongo_wire_protocol_t;

class CTaskWorkerWireProtocol : public CTaskWorkerMongo
{
public:
  virtual int deal_data(const TaskWorkerData *ptwd);
  virtual void free_data(const TaskWorkerData *ptwd);

protected:
  // thread_local_gzip_data_t m_tlgd;
};

#endif // __MONGO_PARSER_WIRE_PROTOCOL_TASK_WORKER_H__
