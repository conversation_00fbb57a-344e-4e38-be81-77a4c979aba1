/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __MONGO_PARSER_OP_H__
#define __MONGO_PARSER_OP_H__

#include <inttypes.h>

enum mongo_op_type
{
  // for request
  OP_UPDATE = 2001,
  OP_INSERT = 2002,
  OP_RESERVED = 2003,
  OP_QUERY = 2004,
  OP_GET_MORE = 2005,
  OP_DELETE = 2006,
  OP_KILL_CURSORS = 2007,
  OP_COMMAND = 2010,

  // for response
  OP_REPLY = 1,
  OP_COMMANDREPLY = 2011,

  OP_MSG = 2013, // for mongo 3.6 mongo 4.0

};

typedef int32_t int32;
typedef uint32_t uint32;
typedef int64_t int64;

typedef struct MsgHeader
{
  int32 messageLength; // total message size, including this
  int32 requestID;     // identifier for this message
  int32 responseTo;    // requestID from the original request
  //   (used in responses from db)
  int32 opCode;        // request type - see table below
} __attribute__((aligned(4), packed)) msg_header_t;

// OP_REPLY
// struct OP_QUERY {
//     MsgHeader header;                 // standard message header
//     int32     flags;                  // bit vector of query options.  See below for details.
//     cstring   fullCollectionName ;    // "dbname.collectionname"
//     int32     numberToSkip;           // number of documents to skip
//     int32     numberToReturn;         // number of documents to return
//                                       //  in the first OP_REPLY batch
//     document  query;                  // query object.  See below for details.
//   [ document  returnFieldsSelector; ] // Optional. Selector indicating the fields
//                                       //  to return.  See below for details.
// }

// OP_REPLY
// struct {
//     MsgHeader header;         // standard message header
//     int32     responseFlags;  // bit vector - see details below
//     int64     cursorID;       // cursor id if client needs to do get more's
//     int32     startingFrom;   // where in the cursor this reply is starting
//     int32     numberReturned; // number of documents in the reply
//     document* documents;      // documents
// }

// OP_COMMAND
// struct {
//    MsgHeader header;     // standard message header
//    cstring database;     // the name of the database to run the command on
//    cstring commandName;  // the name of the command
//    document metadata;    // a BSON document containing any metadata
//    document commandArgs; // a BSON document containing the command arguments
//    inputDocs;            // a set of zero or more documents
// };

// OP_COMMANDREPLY
// struct {
//    MsgHeader header;       // A standard wire protocol header
//    document metadata;      // A BSON document containing any required metadata
//    document commandReply;  // A BSON document containing the command reply
//    document outputDocs;    // A variable number of BSON documents
// }

#endif // __MONGO_PARSER_OP_H__
