/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <memory.h>
#include <inttypes.h>

#include <libbson-1.0/bson.h>

#include <algorithm>

#include "mongo_parser.h"
#include "mongo_parser_common.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "worker_queue.h"
#include "task_worker.h"
#include "gw_stats.h"

#include "cJSON.h"

#include "mongo_parser_wire_protocol_task_worker.h"
#include "display_stats_define.h"


static const char msg_event_type[] = "mongo_event";
static const char msg_content_type[] = "mongo_content";

static inline void stats_count_all(volatile stats_mongo_parser_t *p_smp[], size_t count)
{
  ASSERT(p_smp != NULL);
  for (size_t si = 0; si < count; si++)
  {
    ASSERT(p_smp[si] != NULL);
    __sync_fetch_and_add(&p_smp[si]->cnt_p, 1);
  }
}

static inline void stats_count_succ(volatile stats_mongo_parser_t *p_smp[], size_t count)
{
  ASSERT(p_smp != NULL);
  for (size_t si = 0; si < count; si++)
  {
    ASSERT(p_smp[si] != NULL);
    __sync_fetch_and_add(&p_smp[si]->cnt_p_succ, 1);
  }
}

static inline void stats_count_fail(volatile stats_mongo_parser_t *p_smp[], size_t count)
{
  ASSERT(p_smp != NULL);
  for (size_t si = 0; si < count; si++)
  {
    ASSERT(p_smp[si] != NULL);
    __sync_fetch_and_add(&p_smp[si]->cnt_p_fail, 1);
  }
}

/**
 * 在接收数据时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
int CMongoParser::parse(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon)
{
  CSession *p_session;
  int dir = a_app->dir; // =1 from client; =0 from server;
  int r;
  const msg_header_t *pmh;
  mongo_half_stream_t *mhs = NULL;
  mongo_stream_t *p_ms;
  const char *data;
  int data_len;
  int offset_out;
  // int ssl_offset = 0;
  // payload_data_stream_t *p_pds = NULL;
  StreamData *psd;
  int mongo_offset = 0;
  mongo_map_merge_data_t *p_map_merge_data;
  int64_t pcap_ts;

  p_session = psm->get_session(pcon);
  psd = p_session->get_stream_data_from_parser(this);
  p_ms = psd->p_mongo_stream;

  if (unlikely(p_ms->is_mongo != 1))
  {
    return -1;
  }
  // p_ms->is_mongo = 1;

  mhs = get_mongo_from_session(p_session, dir, psd);
  if (mhs == NULL)
  {
    // 未找到合适的session
    p_session->tcp_drop_data(this);
    return -1;
  }

  data = p_session->get_data(this, dir, &data_len, &offset_out);
  if (unlikely(0))
  {
    unsigned char *p = (unsigned char *)data;
    char buf[256] = {0};
    for (size_t i = 0; i < MIN(24, data_len); i++)
    {
      snprintf(buf + strlen(buf), COUNTOF(buf) - 1 - strlen(buf), " %.2X", p[i]);
    }
    GWLOG_TEST(m_comm, "parse buf=%s\n", buf);
  }

  p_map_merge_data = p_ms->map_session_data;

  while (mongo_offset < data_len)
  {
    pmh = (const msg_header_t *)(data + mongo_offset);
    if (mongo_offset == 0)
    {
      GWLOG_TRACE(m_comm, "msg header messageLength=%d requestID=%x responseTo=%x opCode=%d\n", pmh->messageLength, pmh->requestID, pmh->responseTo, pmh->opCode);
    }
    if (unlikely(0))
    {
      GWLOG_TEST(m_comm, "mongo_offset=%d data_len=%d msg header messageLength=%d requestID=%x responseTo=%x opCode=%d\n", mongo_offset, data_len, pmh->messageLength, pmh->requestID, pmh->responseTo, pmh->opCode);
    }
    if (pmh->messageLength < sizeof(msg_header_t))
    {
      GWLOG_TEST(m_comm, "drop tcp data\n");
      p_session->tcp_drop_data(this);
      return -1;
    }
    // GWLOG_TEST(m_comm, "(mongo_offset + pmh->messageLength)=%d data_len=%d\n", (mongo_offset + pmh->messageLength), data_len);
    if ((mongo_offset + pmh->messageLength) > data_len)
    {
      break;
    }

    // 解析协议数据内容
    // pmh; // 地址
    // pmh->messageLength; // 长度
    if (0)
    {
      pcap_ts = p_session->get_ts_ms();
    }
    else
    {
      struct timespec tp;
      clock_gettime(CLOCK_REALTIME, &tp);
      pcap_ts = (uint64_t)tp.tv_sec * 1000 + tp.tv_nsec / (1000LL * 1000LL);
    }

    if (m_conf_mongo_wire_protocol_mode == 1)
    {
      send_mongo_wire_protocol_content(dir, pcon, p_session, pmh);
    }
    else
    {
      parse_content(p_map_merge_data, dir, pcon, p_session, pmh);
    }

    mongo_offset += pmh->messageLength;
  }

  p_session->discard(this, dir, mongo_offset);
  return 0;
}


int CMongoParser::parse_clear(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon) {
  return 0;
}
/**
 * 在连接关闭时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
int CMongoParser::parse_on_close(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon)
{
  return parse(psm, a_app, pcon);
}

/**
 * 在连接重置时，解析数据流协议。
 * @param CSessionMgt *
 * @param app_stream_t *
 * @param struct conn *
 */
int CMongoParser::parse_on_reset(CSessionMgt *psm, const app_stream_t *a_app, const struct conn *pcon)
{
  return parse(psm, a_app, pcon);
}

bool CMongoParser::read_int64_data_and_move(const unsigned char *&p, const unsigned char *p_end, int64 &data) const volatile
{
  if (p + sizeof(int64) > p_end)
  {
    return false;
  }
  data = *(int64 *)p;
  p += sizeof(int64);

  return true;
}

bool CMongoParser::read_uint8_data_and_move(const unsigned char *&p, const unsigned char *p_end, uint8_t &data) const volatile
{
  if (p + sizeof(uint8_t) > p_end)
  {
    return false;
  }
  data = *(uint8_t *)p;
  p += sizeof(uint8_t);

  return true;
}

bool CMongoParser::read_uint32_data_and_move(const unsigned char *&p, const unsigned char *p_end, uint32 &data) const volatile
{
  if (p + sizeof(uint32) > p_end)
  {
    return false;
  }
  data = *(uint32 *)p;
  p += sizeof(uint32);

  return true;
}

bool CMongoParser::read_int32_data_and_move(const unsigned char *&p, const unsigned char *p_end, int32 &data) const volatile
{
  if (p + sizeof(int32) > p_end)
  {
    return false;
  }
  data = *(int32 *)p;
  p += sizeof(int32);

  return true;
}

bool CMongoParser::read_int32_data(const unsigned char *&p, const unsigned char *p_end, int32 &data) const volatile
{
  if (p + sizeof(int32) > p_end)
  {
    return false;
  }
  data = *(int32 *)p;

  return true;
}

bool CMongoParser::read_cstring_data_and_move(const unsigned char *&p, const unsigned char *p_end, const char *&s) const volatile
{
  int k;
  for (k = 0; p + k <= p_end; k++)
  {
    if (*(p + k) == '\0')
    {
      break;
    }
  }
  if (p + k > p_end)
  {
    return false;
  }
  s = (const char *)p;
  p += k + 1;

  return true;
}

bool CMongoParser::read_document_data_and_move(const unsigned char *&p, const unsigned char *p_end, int &document_length, bson_json_data_t &bjd) const volatile
{
  bson_reader_t *reader;
  const bson_t *b;

  size_t offset = -1;
  int docnum;
  docnum = 0;

  if (!read_int32_data(p, p_end, document_length))
  {
    return false;
  }
  if (document_length <= 0 || document_length > p_end - p)
  {
    return false;
  }

  bjd.json_length = 0;
  bjd.str = NULL;

  reader = bson_reader_new_from_data((const uint8_t *)p, document_length);
  // GWLOG_TEST(m_comm, "reader=%p p=%p p_end=%p %u\n", reader, p, p_end, p_end - p);
  if (!(reader != NULL))
  {
    goto end;
  }

  if ((b = bson_reader_read(reader, NULL)) != NULL)
  {
    docnum++;
    if (!bson_validate(b,
                       (bson_validate_flags_t)(BSON_VALIDATE_UTF8 | BSON_VALIDATE_UTF8_ALLOW_NULL),
                       &offset))
    {
      bson_error_t error = {0};
      // bson_validate_with_error(b,
      //                          (bson_validate_flags_t)(BSON_VALIDATE_UTF8 | BSON_VALIDATE_UTF8_ALLOW_NULL),
      //                          &error);
      GWLOG_TEST(m_comm,
                 "Document %u is invalid :%s at offset %d.\n",
                 docnum, error.message, (int)offset);
      goto end;
    }

    // bjd.str = bson_as_canonical_extended_json(b, &bjd.json_length);
    bjd.str = bson_as_json(b, &bjd.json_length);
    // GWLOG_TEST(m_comm, "docnum=%d offset=%d b=%p\n", docnum, offset, b);
    // GWLOG_TEST(m_comm, "docnum=%d offset=%d b=%p json_length=%u %u json=%s\n", docnum, offset, b, bjd.json_length, strlen(bjd.str), bjd.str);
    // bson_free(bjd.str);
  }

  // GWLOG_TEST(m_comm, "docnum=%d offset=%u\n", docnum, offset);

  /*
   * Cleanup after our reader, which closes the file descriptor.
   */
  bson_reader_destroy(reader);

end:
  p += document_length;

  return true;
}

int CMongoParser::parse_content_deal_op_update(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.req, &m_stats_mongo.op[STATS_OP_UPDATE]};

  int res = -1;

  // TODO
  stats_count_all(p_smp, COUNTOF(p_smp));
  // stats_count_succ(p_smp, COUNTOF(p_smp));
  // stats_count_fail(p_smp, COUNTOF(p_smp));

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);

    // mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    // if (pmod != NULL)
    // {
    //   wire_protocol_lock();
    //   bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
    //   wire_protocol_unlock();
    //   if (!b)
    //   {
    //     del_session_merge_op_data(pmod);
    //     delete pmod;
    //   }
    // }
  }

  return res;
}

int CMongoParser::parse_content_deal_op_insert(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.req, &m_stats_mongo.op[STATS_OP_INSERT]};

  int res = -1;

  // TODO
  stats_count_all(p_smp, COUNTOF(p_smp));
  // stats_count_succ(p_smp, COUNTOF(p_smp));
  // stats_count_fail(p_smp, COUNTOF(p_smp));

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);

    // mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    // if (pmod != NULL)
    // {
    //   wire_protocol_lock();
    //   bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
    //   wire_protocol_unlock();
    //   if (!b)
    //   {
    //     del_session_merge_op_data(pmod);
    //     delete pmod;
    //   }
    // }
  }

  return res;
}

int CMongoParser::parse_content_deal_op_reserved(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.req, &m_stats_mongo.op[STATS_OP_RESERVED]};

  int res = -1;

  // TODO
  stats_count_all(p_smp, COUNTOF(p_smp));
  // stats_count_succ(p_smp, COUNTOF(p_smp));
  // stats_count_fail(p_smp, COUNTOF(p_smp));

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);

    // mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    // if (pmod != NULL)
    // {
    //   wire_protocol_lock();
    //   bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
    //   wire_protocol_unlock();
    //   if (!b)
    //   {
    //     del_session_merge_op_data(pmod);
    //     delete pmod;
    //   }
    // }
  }

  return res;
}

int CMongoParser::parse_content_deal_op_query(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.req, &m_stats_mongo.op[STATS_OP_QUERY]};

  const unsigned char *p;
  const unsigned char *p_base;
  const unsigned char *p_end;
  int32 length = pmh->messageLength;
  int k;
  int document_length;
  // char *str;
  // size_t json_length;
  bson_json_data_t bjd;
  bson_json_data_t bjd_query = {0};
  bson_json_data_t bjd_return_fields = {0};

  int res = -1;

  int32 flags; // bit vector of query options.  See below for details.
  // cstring fullCollectionName; // "dbname.collectionname"
  const char *fullCollectionName;
  int32 numberToSkip;   // number of documents to skip
  int32 numberToReturn; // number of documents to return
  // document  query;                  // query object.  See below for details.

  stats_count_all(p_smp, COUNTOF(p_smp));
  do
  {
    p_base = (const unsigned char *)pmh;
    p_end = p_base + length;
    p = (const unsigned char *)(pmh + 1);

    // GWLOG_TEST(m_comm, "pmh=%p sizeof(msg_header_t)=%u p=%p\n", pmh, sizeof(msg_header_t), p);
    if (!read_int32_data_and_move(p, p_end, flags))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (!read_cstring_data_and_move(p, p_end, fullCollectionName))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (!read_int32_data_and_move(p, p_end, numberToSkip))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (!read_int32_data_and_move(p, p_end, numberToReturn))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    // GWLOG_TEST(m_comm, "flags=%d fullCollectionName=%s numberToSkip=%d numberToReturn=%d\n", flags, fullCollectionName, numberToSkip, numberToReturn);

    // document query;
    if (!read_document_data_and_move(p, p_end, document_length, bjd))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (bjd.str != NULL)
    {
      // GWLOG_TEST(m_comm, "query json_length=%u %u json=%s\n", bjd.json_length, strlen(bjd.str), bjd.str);
      bjd_query = bjd;
    }

    if (p == p_end)
    {
      stats_count_succ(p_smp, COUNTOF(p_smp));
      res = 0;
      break;
    }

    //
    // [ document  returnFieldsSelector; ] // Optional. Selector indicating the fields
    //                                     //  to return.  See below for details.
    if (!read_document_data_and_move(p, p_end, document_length, bjd))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }
    if (bjd.str != NULL)
    {
      GWLOG_TEST(m_comm, "returnFieldsSelector json_length=%u %u json=%s\n", bjd.json_length, strlen(bjd.str), bjd.str);
      bjd_return_fields = bjd;
    }

    if (p == p_end)
    {
      stats_count_succ(p_smp, COUNTOF(p_smp));
      res = 0;
      break;
    }

    stats_count_fail(p_smp, COUNTOF(p_smp));
  } while (0);

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);
    mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    if (pmod != NULL)
    {
      pmod->op_code = pmh->opCode;

      pmod->us_op_query.flags = flags;
      pmod->us_op_query.fullCollectionName = strdup(fullCollectionName);
      pmod->us_op_query.numberToSkip = numberToSkip;
      pmod->us_op_query.numberToReturn = numberToReturn;
      pmod->us_op_query.bjd_query = bjd_query;
      pmod->us_op_query.bjd_return_fields = bjd_return_fields;

      bjd_query.str = NULL;
      bjd_return_fields.str = NULL;

      wire_protocol_lock();
      bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
      wire_protocol_unlock();
      if (!b)
      {
        del_session_merge_op_data(pmod);
        delete pmod;
      }
    }
  }

  if (bjd_query.str != NULL)
  {
    bson_free(bjd_query.str);
  }

  if (bjd_return_fields.str != NULL)
  {
    bson_free(bjd_return_fields.str);
  }

  return res;
}

int CMongoParser::parse_content_deal_op_get_more(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.req, &m_stats_mongo.op[STATS_OP_GET_MORE]};

  int res = -1;

  // TODO
  stats_count_all(p_smp, COUNTOF(p_smp));
  // stats_count_succ(p_smp, COUNTOF(p_smp));
  // stats_count_fail(p_smp, COUNTOF(p_smp));

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);

    // mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    // if (pmod != NULL)
    // {
    //   wire_protocol_lock();
    //   bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
    //   wire_protocol_unlock();
    //   if (!b)
    //   {
    //     del_session_merge_op_data(pmod);
    //     delete pmod;
    //   }
    // }
  }

  return res;
}

int CMongoParser::parse_content_deal_op_delete(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.req, &m_stats_mongo.op[STATS_OP_DELETE]};

  int res = -1;

  // TODO
  stats_count_all(p_smp, COUNTOF(p_smp));
  // stats_count_succ(p_smp, COUNTOF(p_smp));
  // stats_count_fail(p_smp, COUNTOF(p_smp));

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);

    // mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    // if (pmod != NULL)
    // {
    //   wire_protocol_lock();
    //   bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
    //   wire_protocol_unlock();
    //   if (!b)
    //   {
    //     del_session_merge_op_data(pmod);
    //     delete pmod;
    //   }
    // }
  }

  return res;
}

int CMongoParser::parse_content_deal_op_kill_cursors(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.req, &m_stats_mongo.op[STATS_OP_KILL_CURSORS]};

  int res = -1;

  // TODO
  stats_count_all(p_smp, COUNTOF(p_smp));
  // stats_count_succ(p_smp, COUNTOF(p_smp));
  // stats_count_fail(p_smp, COUNTOF(p_smp));

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);

    // mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    // if (pmod != NULL)
    // {
    //   wire_protocol_lock();
    //   bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
    //   wire_protocol_unlock();
    //   if (!b)
    //   {
    //     del_session_merge_op_data(pmod);
    //     delete pmod;
    //   }
    // }
  }

  return res;
}

int CMongoParser::parse_content_deal_op_command(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.req, &m_stats_mongo.op[STATS_OP_COMMAND]};

  const unsigned char *p;
  const unsigned char *p_base;
  const unsigned char *p_end;
  int32 length = pmh->messageLength;
  int k;
  int document_length;
  // char *str;
  // size_t json_length;
  bson_json_data_t bjd;
  bson_json_data_t bjd_metadata = {0};
  bson_json_data_t bjd_command_args = {0};
  std::vector<bson_json_data_t> vec_bjd_documents;

  int res = -1;

  // cstring database;     // the name of the database to run the command on
  // cstring commandName;  // the name of the command
  // document metadata;    // a BSON document containing any metadata
  // document commandArgs; // a BSON document containing the command arguments
  // inputDocs;            // a set of zero or more documents
  const char *database;
  const char *commandName;

  stats_count_all(p_smp, COUNTOF(p_smp));
  do
  {

    p_base = (const unsigned char *)pmh;
    p_end = p_base + length;
    p = (const unsigned char *)(pmh + 1);

    if (!read_cstring_data_and_move(p, p_end, database))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (!read_cstring_data_and_move(p, p_end, commandName))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    // GWLOG_TEST(m_comm, "database=%s commandName=%s\n", database, commandName);

    // document metadata;
    if (!read_document_data_and_move(p, p_end, document_length, bjd))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }
    if (bjd.str != NULL)
    {
      // GWLOG_TEST(m_comm, "metadata json_length=%u %u json=%s\n", bjd.json_length, strlen(bjd.str), bjd.str);
      bjd_metadata = bjd;
    }

    // document commandArgs;
    if (!read_document_data_and_move(p, p_end, document_length, bjd))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }
    if (bjd.str != NULL)
    {
      if (bjd.json_length > 3)
      {
        GWLOG_TEST(m_comm, "commandArgs json_length=%u %u json=%s\n", bjd.json_length, strlen(bjd.str), bjd.str);
        bjd_command_args = bjd;
      }
      else
      {
        bson_free(bjd.str);
      }
    }

    if (p == p_end)
    {
      stats_count_succ(p_smp, COUNTOF(p_smp));
      res = 0;
      break;
    }

    // inputDocs
    for (k = 0; true; k++)
    {
      if (p == p_end)
      {
        stats_count_succ(p_smp, COUNTOF(p_smp));
        res = 0;
        break;
      }

      // document* documents;      // documents
      //
      if (!read_document_data_and_move(p, p_end, document_length, bjd))
      {
        stats_count_fail(p_smp, COUNTOF(p_smp));
        break;
      }

      if (bjd.str != NULL)
      {
        GWLOG_TEST(m_comm, "inputDocs %d json_length=%u %u json=%s\n", k, bjd.json_length, strlen(bjd.str), bjd.str);
        vec_bjd_documents.push_back(bjd);
      }
    }
  } while (0);

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);
    mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    if (pmod != NULL)
    {
      pmod->op_code = pmh->opCode;

      pmod->us_op_command.database = strdup(database);
      pmod->us_op_command.commandName = strdup(commandName);

      pmod->us_op_command.bjd_metadata = bjd_metadata;
      pmod->us_op_command.bjd_command_args = bjd_command_args;
      bjd_metadata.str = NULL;
      bjd_command_args.str = NULL;

      pmod->us_op_command.bjd_documents_size = vec_bjd_documents.size();
      pmod->us_op_command.bjd_documents = NULL;
      if (pmod->us_op_command.bjd_documents_size > 0)
      {
        pmod->us_op_command.bjd_documents = new bson_json_data_t[pmod->us_op_command.bjd_documents_size];
        memcpy(pmod->us_op_command.bjd_documents, &vec_bjd_documents[0], sizeof(bson_json_data_t) * pmod->us_op_command.bjd_documents_size);
        vec_bjd_documents.clear();
      }
    }

    wire_protocol_lock();
    bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
    wire_protocol_unlock();
    if (!b)
    {
      del_session_merge_op_data(pmod);
      delete pmod;
    }
  }

  if (bjd_metadata.str)
  {
    bson_free(bjd_metadata.str);
  }

  if (bjd_command_args.str)
  {
    bson_free(bjd_command_args.str);
  }

  for (size_t i = 0; i < vec_bjd_documents.size(); i++)
  {
    if (vec_bjd_documents[i].str != NULL)
    {
      bson_free(vec_bjd_documents[i].str);
    }
  }

  return res;
}

int CMongoParser::parse_content_deal_op_reply(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.rsp, &m_stats_mongo.op[STATS_OP_REPLY]};

  const unsigned char *p;
  const unsigned char *p_base;
  const unsigned char *p_end;
  int32 length = pmh->messageLength;
  int k;
  int document_length;
  // char *str;
  // size_t json_length;
  bson_json_data_t bjd;
  std::vector<bson_json_data_t> vec_bjd_documents;

  int res = -1;

  int32 responseFlags;  // bit vector - see details below
  int64 cursorID;       // cursor id if client needs to do get more's
  int32 startingFrom;   // where in the cursor this reply is starting
  int32 numberReturned; // number of documents in the reply
  // document* documents;      // documents

  stats_count_all(p_smp, COUNTOF(p_smp));
  do
  {

    p_base = (const unsigned char *)pmh;
    p_end = p_base + length;
    p = (const unsigned char *)(pmh + 1);

    if (!read_int32_data_and_move(p, p_end, responseFlags))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (!read_int64_data_and_move(p, p_end, cursorID))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (!read_int32_data_and_move(p, p_end, startingFrom))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (!read_int32_data_and_move(p, p_end, numberReturned))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    // GWLOG_TEST(m_comm, "responseFlags=%d cursorID=%" PRId64 " startingFrom=%d numberReturned=%d\n", responseFlags, cursorID, startingFrom, numberReturned);

    // document* documents;      // documents
    //
    for (k = 0; k < numberReturned; k++)
    {
      if (!read_document_data_and_move(p, p_end, document_length, bjd))
      {
        break;
      }

      if (bjd.str != NULL)
      {
        // GWLOG_TEST(m_comm, "documents %d json_length=%u %u json=%s\n", k, bjd.json_length, strlen(bjd.str), bjd.str);
        vec_bjd_documents.push_back(bjd);
      }
    }

    if (k < numberReturned)
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (p == p_end)
    {
      stats_count_succ(p_smp, COUNTOF(p_smp));
      res = 0;
      break;
    }

    stats_count_fail(p_smp, COUNTOF(p_smp));
  } while (0);

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);
    mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    if (pmod != NULL)
    {
      pmod->op_code = pmh->opCode;

      pmod->us_op_reply.responseFlags = responseFlags;
      pmod->us_op_reply.cursorID = cursorID;
      pmod->us_op_reply.startingFrom = startingFrom;
      pmod->us_op_reply.numberReturned = numberReturned;

      pmod->us_op_reply.bjd_documents_size = vec_bjd_documents.size();
      pmod->us_op_reply.bjd_documents = NULL;
      if (pmod->us_op_reply.bjd_documents_size > 0)
      {
        pmod->us_op_reply.bjd_documents = new bson_json_data_t[pmod->us_op_reply.bjd_documents_size];
        memcpy(pmod->us_op_reply.bjd_documents, &vec_bjd_documents[0], sizeof(bson_json_data_t) * pmod->us_op_reply.bjd_documents_size);
        vec_bjd_documents.clear();
      }

      wire_protocol_lock();
      bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
      wire_protocol_unlock();
      if (!b)
      {
        del_session_merge_op_data(pmod);
        delete pmod;
      }
    }
  }

  for (size_t i = 0; i < vec_bjd_documents.size(); i++)
  {
    if (vec_bjd_documents[i].str != NULL)
    {
      bson_free(vec_bjd_documents[i].str);
    }
  }

  return res;
}

int CMongoParser::parse_content_deal_op_commandreply(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {&m_stats_mongo.rsp, &m_stats_mongo.op[STATS_OP_COMMANDREPLY]};

  const unsigned char *p;
  const unsigned char *p_base;
  const unsigned char *p_end;
  int32 length = pmh->messageLength;
  int k;
  int document_length;
  // char *str;
  // size_t json_length;
  bson_json_data_t bjd;
  bson_json_data_t bjd_metadata = {0};
  bson_json_data_t bjd_command_reply = {0};
  bson_json_data_t bjd_output_docs = {0};

  int res = -1;

  // document metadata;      // A BSON document containing any required metadata
  // document commandReply;  // A BSON document containing the command reply
  // document outputDocs;    // A variable number of BSON documents

  stats_count_all(p_smp, COUNTOF(p_smp));
  do
  {

    p_base = (const unsigned char *)pmh;
    p_end = p_base + length;
    p = (const unsigned char *)(pmh + 1);

    // document metadata;
    if (!read_document_data_and_move(p, p_end, document_length, bjd))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }
    if (bjd.str != NULL)
    {
      // GWLOG_TEST(m_comm, "metadata json_length=%u %u json=%s\n", bjd.json_length, strlen(bjd.str), bjd.str);
      bjd_metadata = bjd;
    }

    // document commandReply;
    if (!read_document_data_and_move(p, p_end, document_length, bjd))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }
    if (bjd.str != NULL)
    {
      if (bjd.json_length > 3)
      {
        GWLOG_TEST(m_comm, "commandReply json_length=%u %u json=%s\n", bjd.json_length, strlen(bjd.str), bjd.str);
        bjd_command_reply = bjd;
      }
      else
      {
        bson_free(bjd.str);
      }
    }

    if (p == p_end)
    {
      stats_count_succ(p_smp, COUNTOF(p_smp));
      res = 0;
      break;
    }

    // document outputDocs;
    if (!read_document_data_and_move(p, p_end, document_length, bjd))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }
    if (bjd.str != NULL)
    {
      GWLOG_TEST(m_comm, "outputDocs json_length=%u %u json=%s\n", bjd.json_length, strlen(bjd.str), bjd.str);
      bjd_output_docs = bjd;
    }

    if (p == p_end)
    {
      stats_count_succ(p_smp, COUNTOF(p_smp));
      res = 0;
      break;
    }
    stats_count_fail(p_smp, COUNTOF(p_smp));
  } while (0);

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);
    mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    if (pmod != NULL)
    {
      pmod->op_code = pmh->opCode;

      pmod->us_op_commandreply.bjd_metadata = bjd_metadata;
      pmod->us_op_commandreply.bjd_command_reply = bjd_command_reply;
      pmod->us_op_commandreply.bjd_output_docs = bjd_output_docs;

      bjd_metadata.str = NULL;
      bjd_command_reply.str = NULL;
      bjd_output_docs.str = NULL;

      wire_protocol_lock();
      bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
      wire_protocol_unlock();

      if (!b)
      {
        del_session_merge_op_data(pmod);
        delete pmod;
      }
    }
  }

  if (bjd_metadata.str != NULL)
  {
    bson_free(bjd_metadata.str);
  }

  if (bjd_command_reply.str != NULL)
  {
    bson_free(bjd_command_reply.str);
  }

  if (bjd_output_docs.str != NULL)
  {
    bson_free(bjd_output_docs.str);
  }

  return res;
}

int CMongoParser::parse_content_deal_op_msg(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  volatile stats_mongo_parser_t *p_smp[] = {((dir == STREAM_REQ) ? &m_stats_mongo.req : &m_stats_mongo.rsp), &m_stats_mongo.op[STATS_OP_MSG]};

  const unsigned char *p;
  const unsigned char *p_base;
  const unsigned char *p_end;
  int32 length = pmh->messageLength;
  int k;
  int document_length;
  // char *str;
  // size_t json_length;
  bson_json_data_t bjd;
  bson_json_data_t bjd_body = {0};

  int res = -1;

  uint32 flagBits; // message flags
  // Sections[] sections;       // data sections
  uint8_t kind;
  uint32 checksum; // optional<uint32> checksum; // optional CRC-32C checksum

  stats_count_all(p_smp, COUNTOF(p_smp));
  do
  {

    p_base = (const unsigned char *)pmh;
    p_end = p_base + length;
    p = (const unsigned char *)(pmh + 1);

    if (!read_uint32_data_and_move(p, p_end, flagBits))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (!read_uint8_data_and_move(p, p_end, kind))
    {
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    bool b_break = true;

    switch (kind)
    {
    case 0:
      // BODY
      if (!read_document_data_and_move(p, p_end, document_length, bjd))
      {
        stats_count_fail(p_smp, COUNTOF(p_smp));
        break;
      }
      if (bjd.str != NULL)
      {
        if (bjd.json_length > 3)
        {
          // GWLOG_TEST(m_comm, "BODY document json_length=%u %u json=%s\n", bjd.json_length, strlen(bjd.str), bjd.str);
          bjd_body = bjd;
        }
        else
        {
          bson_free(bjd.str);
        }
      }
      b_break = false;
      break;

    case 1:
      // Document Sequence
      GWLOG_WARN(m_comm, "kind=1\n");
      // TODO
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;

    default:
      GWLOG_WARN(m_comm, "kind=%d\n", kind);
      stats_count_fail(p_smp, COUNTOF(p_smp));
      break;
    }

    if (b_break)
    {
      break;
    }

    // checksumPresent
    if ((flagBits & (1 << 0)) != 0)
    {
      if (!read_uint32_data_and_move(p, p_end, checksum))
      {
        stats_count_fail(p_smp, COUNTOF(p_smp));
        break;
      }
    }

    if (p == p_end)
    {
      stats_count_succ(p_smp, COUNTOF(p_smp));
      res = 0;
      break;
    }

    stats_count_fail(p_smp, COUNTOF(p_smp));
  } while (0);

  if (0 == res)
  {
    ASSERT(p_map_merge_data != NULL);
    mongo_session_merge_op_data_t *pmod = new mongo_session_merge_op_data_t();
    if (pmod != NULL)
    {
      pmod->op_code = pmh->opCode;

      pmod->us_op_msg.flagBits = flagBits;
      pmod->us_op_msg.kind = kind;
      pmod->us_op_msg.bjd_body = bjd_body;
      pmod->us_op_msg.checksum = checksum;

      bjd_body.str = NULL;

      wire_protocol_lock();
      bool b = add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod);
      wire_protocol_unlock();

      if (!b)
      {
        del_session_merge_op_data(pmod);
        delete pmod;
      }
    }
  }

  if (bjd_body.str != NULL)
  {
    bson_free(bjd_body.str);
  }

  return res;
}

int CMongoParser::parse_content_deal(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, CSession *p_session, const msg_header_t *pmh)
{
  int res = -1;

  ASSERT(p_map_merge_data != NULL);

  switch (pmh->opCode)
  {
  case OP_UPDATE:
    GWLOG_WARN(m_comm, "mongo opCode: OP_UPDATE\n");
    res = parse_content_deal_op_update(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_INSERT:
    GWLOG_WARN(m_comm, "mongo opCode: OP_INSERT\n");
    res = parse_content_deal_op_insert(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_RESERVED:
    GWLOG_WARN(m_comm, "mongo opCode: OP_RESERVED\n");
    res = parse_content_deal_op_reserved(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_QUERY:
    res = parse_content_deal_op_query(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_GET_MORE:
    GWLOG_WARN(m_comm, "mongo opCode: OP_GET_MORE\n");
    res = parse_content_deal_op_get_more(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_DELETE:
    GWLOG_WARN(m_comm, "mongo opCode: OP_DELETE\n");
    res = parse_content_deal_op_delete(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_KILL_CURSORS:
    GWLOG_WARN(m_comm, "mongo opCode: OP_KILL_CURSORS\n");
    res = parse_content_deal_op_kill_cursors(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_COMMAND:
    res = parse_content_deal_op_command(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_REPLY:
    res = parse_content_deal_op_reply(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_COMMANDREPLY:
    res = parse_content_deal_op_commandreply(p_map_merge_data, dir, pcon, pmh);
    break;

  case OP_MSG:
    res = parse_content_deal_op_msg(p_map_merge_data, dir, pcon, pmh);
    break;

  default:
    GWLOG_WARN(m_comm, "mongo opCode: unknown\n");
    return 1;
    break;
  }

  if (res == 0)
  {
    parse_content_deal_merge(p_map_merge_data, dir, pcon, p_session, pmh);
  }

  return res;
}

int CMongoParser::parse_content(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, CSession *p_session, const msg_header_t *pmh)
{
  int res;

  __sync_fetch_and_add(&m_stats_mongo.p.cnt_p, 1);
  res = parse_content_deal(p_map_merge_data, dir, pcon, p_session, pmh);
  if (0 == res)
  {
    __sync_fetch_and_add(&m_stats_mongo.p.cnt_p_succ, 1);
  }
  else
  {
    __sync_fetch_and_add(&m_stats_mongo.p.cnt_p_fail, 1);
  }

  return res;
}

CWorkerQueue *CMongoParser::new_wq_wire_protocol(void)
{
  m_p_wq[MONGOPARSER_WQ_WIRE_PROTOCOL] = m_comm->create_worker_queue();
  CWorkerQueue *pwq = get_wq_wire_protocol();
  if (pwq == NULL)
  {
    return NULL;
  }

  CTaskWorkerWireProtocol *ptw = new CTaskWorkerWireProtocol();
  ptw->set_parser(this);
  ptw->set_wq(pwq);
  m_p_tw[MONGOPARSER_WQ_WIRE_PROTOCOL] = ptw;

  pwq->set_gw_common(m_comm);
  pwq->set_watchdog(m_comm->get_watchdog());
  pwq->set_task_worker(ptw);

  pwq->set_queue_num_and_bytes(m_conf_mongo_wire_protocol_queue_max_num, m_conf_mongo_wire_protocol_queue_memory_max_size_bytes);
  pwq->set_queue_name(MONGO_WIRE_QUEUE);
  // pwq->set_queue_destroy_callback((q_destroy_func_t)free_mongo_wire_protocol);
  pwq->init();
  pwq->create_queue();
  // pwq->create_thread(m_conf_mongo_wire_protocol_thread_num, worker_routine_mongo_wire_protocol, this);
  pwq->adjust_worker_thread_num(m_conf_mongo_wire_protocol_thread_num);

  m_comm->get_gw_stats()->set_task(pwq->get_queue_name(), pwq->get_stats_task_data(), 51);
  m_comm->get_gw_stats()->set_qps(MONGO_WIRE_QPS, &pwq->get_stats_task_data()->cnt);
  m_comm->get_gw_stats()->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());

  return pwq;
}

bool CMongoParser::send_mongo_wire_protocol_content(int dir, const struct conn *pcon, CSession *p_session,  const msg_header_t *pmh)
{
  size_t mem_size = sizeof(mongo_wire_protocol_t) + pmh->messageLength;
  mongo_wire_protocol_t *pmwp = new mongo_wire_protocol_t;
  pmwp->conn = *pcon;                     // conn
  pmwp->dir = dir;                        // dir
  pmwp->p_session = p_session;                // pcap_ts
  pmwp->length = pmh->messageLength;      // data length
  pmwp->s = new char[pmh->messageLength]; // data
  memcpy(pmwp->s, pmh, pmwp->length);

  if (!get_wq_wire_protocol()->queue_put_data(pmwp, mem_size))
  {
    free_mongo_wire_protocol_inner(pmwp);

    delete pmwp;
    return false;
  }

  return true;
}

int CMongoParser::worker_routine_mongo_wire_protocol_inner(const void *p)
{
  int res;
  const mongo_wire_protocol_t *pmwp = (const mongo_wire_protocol_t *)p;

  // 获取线程对应的Session
  const struct conn *pcon = &pmwp->conn;
  // wire_protocol_lock((pcon->client.port&0xf));
  // wire_protocol_unlock((pcon->client.port&0xf));

  res = parse_content(&m_map_session, pmwp->dir, pcon, pmwp->p_session, pmwp->pmh);

  return res;
}

void CMongoParser::free_mongo_wire_protocol_inner(const mongo_wire_protocol_t *p) volatile const
{
  delete[] p->s;
}

int CMongoParser::parse_content_deal_merge(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, CSession *p_session, const msg_header_t *pmh)
{
  ASSERT(p_map_merge_data != NULL);
  wire_protocol_lock();
  int32 message_id;
  mongo_map_merge_data_t &mmd = (*p_map_merge_data);
  mongo_session_merge_data_t *pmsmd = &mmd[*pcon];
  std::vector<mongo_session_merge_op_data_t *>::const_reverse_iterator iter;
  std::vector<mongo_session_merge_op_data_t *>::const_reverse_iterator iter2;
  const mongo_session_merge_op_data_t *pmod = NULL;
  const mongo_session_merge_op_data_t *pmod2 = NULL;
  std::vector<mongo_session_merge_op_data_t *> *p_vec_mod = NULL;
  std::vector<mongo_session_merge_op_data_t *> *p_vec_mod2 = NULL;
  const mongo_session_merge_op_data_t **pmod_req = NULL;
  const mongo_session_merge_op_data_t **pmod_rsp = NULL;

  switch (dir)
  {
  case STREAM_RSP:
    pmod_req = &pmod2;
    pmod_rsp = &pmod;
    p_vec_mod2 = &pmsmd->vec_req_op_data;
    p_vec_mod = &pmsmd->vec_rsp_op_data;
    message_id = pmh->responseTo;
    for (iter = p_vec_mod->rbegin(); iter != p_vec_mod->rend(); iter++)
    {
      if ((*iter)->message_id == message_id)
      {
        pmod = *iter;
        break;
      }
    }
    break;

  case STREAM_REQ:
    pmod_req = &pmod;
    pmod_rsp = &pmod2;
    p_vec_mod2 = &pmsmd->vec_rsp_op_data;
    p_vec_mod = &pmsmd->vec_req_op_data;
    message_id = pmh->requestID;
    for (iter = p_vec_mod->rbegin(); iter != p_vec_mod->rend(); iter++)
    {
      if ((*iter)->message_id == message_id)
      {
        pmod = *iter;
        break;
      }
    }
    break;

  default:
    ASSERT(false);
    break;
  }

  if (pmod != NULL)
  {
    for (iter2 = p_vec_mod2->rbegin(); iter2 != p_vec_mod2->rend(); iter2++)
    {
      if ((*iter2)->message_id == message_id)
      {
        pmod2 = *iter2;
        break;
      }
    }

    if (pmod2 != NULL && pmod != NULL)
    {

      __sync_fetch_and_add(&m_stats_mongo.cnt_session, 1);

      p_vec_mod->erase(std::find(p_vec_mod->begin(), p_vec_mod->end(), pmod));
      p_vec_mod2->erase(std::find(p_vec_mod2->begin(), p_vec_mod2->end(), pmod2));
    }
  }

  wire_protocol_unlock();

  if (pmod2 != NULL && pmod != NULL)
  {
    parse_content_deal_merge_msg(pcon, p_session, (*pmod_req), (*pmod_rsp));

    del_session_merge_op_data(pmod);
    delete pmod;
    del_session_merge_op_data(pmod2);
    delete pmod2;
  }

  return 0;
}

int CMongoParser::parse_content_deal_merge_msg(const struct conn *pcon, CSession *p_session, const mongo_session_merge_op_data_t *req, const mongo_session_merge_op_data_t *rsp)
{
  ASSERT(pcon != NULL);
  ASSERT(req != NULL);
  ASSERT(rsp != NULL);
  ASSERT(req->message_id == rsp->message_id);

  // 推送消息
  // GWLOG_TEST(m_comm, "message_id=%d req opcode=%d rsp opcode=%d\n", req->message_id, req->op_code, rsp->op_code);

  cJSON *root_send = cJSON_CreateObject();

  StreamData* psd = p_session->get_stream_data_from_parser(this);

  //sessionID、username、version
  cJSON_AddStringToObject(root_send,"sessionId",psd->p_mongo_stream->sessionid.c_str());
  cJSON_AddStringToObject(root_send,"username",psd->p_mongo_stream->username.c_str());
  cJSON_AddStringToObject(root_send,"serverVersion",psd->p_mongo_stream->version.c_str());

  //request id、time
  std::string reqid = psd->p_mongo_stream->sessionid;
  reqid += std::to_string(req->message_id);
  cJSON_AddStringToObject(root_send, "id", reqid.c_str());

  cJSON_AddNumberToObject(root_send, "timestamp", req->tm);


  cJSON *root_req = parse_content_deal_merge_msg_op_data(req);
  char* reqstr = cJSON_PrintUnformatted(root_req);
  cJSON_AddStringToObject(root_send, "reqContent", reqstr);
  cJSON_free(reqstr);
  cJSON_Delete(root_req);
  // cJSON_AddStringToObject(root_send,"reqContent","");
  std::string operation = "";
  cJSON_AddNumberToObject(root_send, "success", 1);

  switch(req->op_code){
    case OP_INSERT:
      operation = "insert";
    break;
    case OP_DELETE:
      operation = "delete";
    break;
    case OP_UPDATE:
      operation = "update";
    break;
    case OP_QUERY:
      operation = "query";
    break;
    case OP_COMMAND:
      operation = req->us_op_command.commandName;
    break;
  }
  cJSON_AddStringToObject(root_send,"operation",operation.c_str());
  cJSON_AddNumberToObject(root_send, "errCode", 0);  
  cJSON_AddStringToObject(root_send,"errMsg","");

  // 连接地址信息
  char src_ip_buf[128] = {0};
  char dst_ip_buf[128] = {0};
  m_comm->get_ip_addr(src_ip_buf, COUNTOF(src_ip_buf), &pcon->client);
  m_comm->get_ip_addr(dst_ip_buf, COUNTOF(dst_ip_buf), &pcon->server);


  cJSON_AddStringToObject(root_send, "ip", src_ip_buf);
  cJSON_AddNumberToObject(root_send, "port", pcon->client.port);
  cJSON_AddStringToObject(root_send, "serverIp", dst_ip_buf);
  cJSON_AddNumberToObject(root_send, "serverPort", pcon->server.port);
  cJSON_AddNumberToObject(root_send, "reqTime",rsp->tm - req->tm);


  cJSON_AddNumberToObject(root_send, "resourceType", RESOURCE_TYPE_MONGO);

  const char *s = cJSON_PrintUnformatted(root_send);
  cJSON_Delete(root_send);

  send_upload_msg(s,msg_event_type, psd->p_mongo_stream->sessionid);

  root_send = cJSON_CreateObject();
  cJSON_AddStringToObject(root_send, "eventId", reqid.c_str());
  cJSON_AddStringToObject(root_send,"operation",operation.c_str());
  cJSON_AddNumberToObject(root_send, "timestamp", req->tm);
  cJSON *root_rsp = parse_content_deal_merge_msg_op_data(rsp);
  char* resstr = cJSON_PrintUnformatted(root_rsp);
  cJSON_AddStringToObject(root_send, "responseBody", resstr);
  cJSON_free(resstr);
  cJSON_Delete(root_rsp);
  cJSON_AddNumberToObject(root_send, "resourceType", RESOURCE_TYPE_MONGO);

  s = cJSON_PrintUnformatted(root_send);
  cJSON_Delete(root_send);

  send_upload_msg(s,msg_content_type, psd->p_mongo_stream->sessionid);

  return 0;
}

cJSON *CMongoParser::parse_content_deal_merge_msg_op_data(const mongo_session_merge_op_data_t *p_mod)
{
  ASSERT(p_mod != NULL);
  cJSON *root = cJSON_CreateObject();
  cJSON *root_data = cJSON_CreateObject();

  if (root == NULL || root_data == NULL)
  {
    cJSON_Delete(root_data);
    cJSON_Delete(root);
    return NULL;
  }

  cJSON_AddItemToObject(root, "data", root_data);
  cJSON_AddNumberToObject(root, "op_code", p_mod->op_code);

  switch (p_mod->op_code)
  {
  case OP_QUERY:
    cJSON_AddNumberToObject(root_data, "flags", p_mod->us_op_query.flags);
    cJSON_AddStringToObject(root_data, "fullCollectionName", p_mod->us_op_query.fullCollectionName);
    cJSON_AddNumberToObject(root_data, "numberToSkip", p_mod->us_op_query.numberToSkip);
    cJSON_AddNumberToObject(root_data, "numberToReturn", p_mod->us_op_query.numberToReturn);
    cJSON_AddStringToObject(root_data, "query", p_mod->us_op_query.bjd_query.str);
    if (p_mod->us_op_query.bjd_return_fields.str != NULL)
    {
      cJSON_AddStringToObject(root_data, "returnFieldsSelector", p_mod->us_op_query.bjd_return_fields.str);
    }
    break;

  case OP_COMMAND:
    cJSON_AddStringToObject(root_data, "database", p_mod->us_op_command.database);
    cJSON_AddStringToObject(root_data, "commandName", p_mod->us_op_command.commandName);
    cJSON_AddStringToObject(root_data, "metadata", p_mod->us_op_command.bjd_metadata.str);
    cJSON_AddStringToObject(root_data, "commandArgs", p_mod->us_op_command.bjd_command_args.str);

    if (p_mod->us_op_command.bjd_documents_size > 0)
    {
      cJSON *array = cJSON_CreateArray();
      cJSON_AddItemToObject(root_data, "inputDocs", array);
      for (size_t i = 0; i < p_mod->us_op_command.bjd_documents_size; i++)
      {
        cJSON_AddItemToArray(array, cJSON_CreateString(p_mod->us_op_command.bjd_documents[i].str));
      }
    }
    break;

  case OP_REPLY:
    cJSON_AddNumberToObject(root_data, "responseFlags", p_mod->us_op_reply.responseFlags);
    {
      char buf[128] = {0};
      snprintf(buf, COUNTOF(buf), "%" PRId64 "", p_mod->us_op_reply.cursorID);
      cJSON_AddStringToObject(root_data, "cursorID", buf);
    }
    cJSON_AddNumberToObject(root_data, "startingFrom", p_mod->us_op_reply.startingFrom);
    cJSON_AddNumberToObject(root_data, "numberReturned", p_mod->us_op_reply.numberReturned);

    if (p_mod->us_op_reply.bjd_documents_size > 0)
    {
      cJSON *array = cJSON_CreateArray();
      cJSON_AddItemToObject(root_data, "documents", array);
      for (size_t i = 0; i < p_mod->us_op_reply.bjd_documents_size; i++)
      {
        cJSON_AddItemToArray(array, cJSON_CreateString(p_mod->us_op_reply.bjd_documents[i].str));
      }
    }
    break;

  case OP_COMMANDREPLY:
    cJSON_AddStringToObject(root_data, "metadata", p_mod->us_op_commandreply.bjd_metadata.str);
    cJSON_AddStringToObject(root_data, "commandReply", p_mod->us_op_commandreply.bjd_command_reply.str);
    if (p_mod->us_op_commandreply.bjd_output_docs.str != NULL)
    {
      cJSON_AddStringToObject(root_data, "outputDocs", p_mod->us_op_commandreply.bjd_output_docs.str);
    }
    break;

  case OP_MSG:
    cJSON_AddNumberToObject(root_data, "flagBits", p_mod->us_op_msg.flagBits);
    {
      cJSON *array = cJSON_CreateArray();
      cJSON_AddItemToObject(root_data, "sections", array);

      switch (p_mod->us_op_msg.kind)
      {
      case 0:
        // for kind 0 body
        {
          cJSON *root_section = cJSON_CreateObject();
          cJSON_AddItemToArray(array, root_section);
          cJSON_AddNumberToObject(root_section, "kind", p_mod->us_op_msg.kind);
          cJSON_AddStringToObject(root_section, "body", p_mod->us_op_msg.bjd_body.str);
        }
        break;

      case 1:
        // for kind 1 Document Sequence
        break;

      default:
        break;
      }
    }

    // checksumPresent
    if ((p_mod->us_op_msg.flagBits & (1 << 0)) != 0)
    {
      cJSON_AddNumberToObject(root_data, "checksum", p_mod->us_op_msg.checksum);
    }
    break;

  default:
    break;
  }

  return root;
}

mongo_session_merge_op_data_t *CMongoParser::new_map_merge_op_data(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh)
{
  mongo_session_merge_op_data_t *pmod = NULL;

  if (!add_map_merge_op_data(p_map_merge_data, dir, pcon, pmh, pmod))
  {
    delete pmod;
    return NULL;
  }

  return pmod;
}

bool CMongoParser::add_map_merge_op_data(mongo_map_merge_data_t *p_map_merge_data, int dir, const struct conn *pcon, const msg_header_t *pmh, mongo_session_merge_op_data_t *&pmod)
{
  mongo_map_merge_data_t &mmd = (*p_map_merge_data);

  int64_t pcap_ts;
  struct timespec tp;

  switch (dir)
  {
  case STREAM_RSP:
    if (pmod == NULL)
    {
      pmod = new mongo_session_merge_op_data_t();
    }
    mmd[*pcon].vec_rsp_op_data.push_back(pmod);
    pmod->message_id = pmh->responseTo;
    clock_gettime(CLOCK_REALTIME, &tp);
    pmod->tm = (uint64_t)tp.tv_sec * 1000 + tp.tv_nsec / (1000LL * 1000LL);
    break;

  case STREAM_REQ:
    if (pmod == NULL)
    {
      pmod = new mongo_session_merge_op_data_t();
    }
    mmd[*pcon].vec_req_op_data.push_back(pmod);
    pmod->message_id = pmh->requestID;
    clock_gettime(CLOCK_REALTIME, &tp);
    pmod->tm = (uint64_t)tp.tv_sec * 1000 + tp.tv_nsec / (1000LL * 1000LL);
    break;

  default:
    return false;
  }

  return true;
}

void CMongoParser::del_session_mongo_map_merge_data(mongo_map_merge_data_t *p_mmmd) const
{
  mongo_map_merge_data_t::const_iterator iter;
  ASSERT(p_mmmd != NULL);

  for (iter = p_mmmd->begin(); iter != p_mmmd->end(); iter++)
  {

    for (size_t i = 0; i < iter->second.vec_req_op_data.size(); i++)
    {
      del_session_merge_op_data(iter->second.vec_req_op_data[i]);
      delete iter->second.vec_req_op_data[i];
    }

    for (size_t i = 0; i < iter->second.vec_rsp_op_data.size(); i++)
    {
      del_session_merge_op_data(iter->second.vec_rsp_op_data[i]);
      delete iter->second.vec_rsp_op_data[i];
    }
  }

  p_mmmd->clear();
}

void CMongoParser::del_session_merge_op_data(const mongo_session_merge_op_data_t *p_msmod) const
{
  ASSERT(p_msmod != NULL);

  if (p_msmod == NULL)
  {
    return;
  }

  switch (p_msmod->op_code)
  {
  case OP_QUERY:
    SAFE_FREE((void *)p_msmod->us_op_query.fullCollectionName);
    BSON_SAFE_FREE(p_msmod->us_op_query.bjd_query);
    BSON_SAFE_FREE(p_msmod->us_op_query.bjd_return_fields);
    break;

  case OP_COMMAND:
    SAFE_FREE((void *)p_msmod->us_op_command.database);
    SAFE_FREE((void *)p_msmod->us_op_command.commandName);
    BSON_SAFE_FREE(p_msmod->us_op_command.bjd_metadata);
    BSON_SAFE_FREE(p_msmod->us_op_command.bjd_command_args);

    if (p_msmod->us_op_command.bjd_documents_size > 0)
    {
      for (size_t i = 0; i < p_msmod->us_op_command.bjd_documents_size; i++)
      {
        BSON_SAFE_FREE(p_msmod->us_op_command.bjd_documents[i]);
      }
    }
    delete[] p_msmod->us_op_command.bjd_documents;
    break;

  case OP_REPLY:
    if (p_msmod->us_op_reply.bjd_documents_size > 0)
    {
      for (size_t i = 0; i < p_msmod->us_op_reply.bjd_documents_size; i++)
      {
        BSON_SAFE_FREE(p_msmod->us_op_reply.bjd_documents[i]);
      }
    }
    delete[] p_msmod->us_op_reply.bjd_documents;
    break;

  case OP_COMMANDREPLY:
    BSON_SAFE_FREE(p_msmod->us_op_commandreply.bjd_metadata);
    BSON_SAFE_FREE(p_msmod->us_op_commandreply.bjd_command_reply);
    BSON_SAFE_FREE(p_msmod->us_op_commandreply.bjd_output_docs);
    break;

  case OP_MSG:
    BSON_SAFE_FREE(p_msmod->us_op_msg.bjd_body);
    break;

  default:
    break;
  }

  return;
}
