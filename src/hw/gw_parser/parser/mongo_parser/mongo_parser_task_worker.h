/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __MONGO_PARSER_TASK_WORKER_H__
#define __MONGO_PARSER_TASK_WORKER_H__

#include "task_worker.h"

//  TaskWorkerData twd;

class CMongoParser;

class CTaskWorkerMongo : public CTaskWorker
{
public:
  virtual CWorkerQueue *get_wq(void) const;
  // virtual int deal_data(const TaskWorkerData *) = 0;
  // virtual void free_data(const TaskWorkerData *) = 0;

  virtual void init(void);
  virtual void fini(void);
  virtual void release(void) const;

public:
  virtual ~CTaskWorkerMongo();

  inline void set_wq(CWorkerQueue *pwq)
  {
    m_pwq = pwq;
  }

  inline void set_parser(CMongoParser *parser)
  {
    m_parser = parser;
  }

  inline CMongoParser *get_parser(void)
  {
    return m_parser;
  }

protected:
  CWorkerQueue *m_pwq;
  CMongoParser *m_parser;
};

#endif // __MONGO_PARSER_TASK_WORKER_H__
