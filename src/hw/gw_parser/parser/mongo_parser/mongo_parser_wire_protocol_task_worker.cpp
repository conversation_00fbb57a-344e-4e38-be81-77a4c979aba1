/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <memory.h>
#include <unistd.h>
#include <arpa/inet.h>

#include "mongo_parser.h"
#include "mongo_parser_common.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "mongo_parser_wire_protocol_task_worker.h"

int CTaskWorkerWireProtocol::deal_data(const TaskWorkerData *ptwd)
{
  return get_parser()->worker_routine_mongo_wire_protocol_inner(ptwd);
}

void CTaskWorkerWireProtocol::free_data(const TaskWorkerData *ptwd)
{
  const mongo_wire_protocol_t *p = (const mongo_wire_protocol_t *)ptwd;
  get_parser()->free_mongo_wire_protocol_inner(p);

  delete p;
}
