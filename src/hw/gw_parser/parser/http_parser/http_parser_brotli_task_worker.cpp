/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <errno.h>
#include <vector>
#include <algorithm>

#include "http_parser_brotli_task_worker.h"
#include "http_parser.h"
#include "worker_queue.h"
#include "gw_logger.h"
#include "gw_stats.h"
#include "utils.h"
#include "http_parser_common.h"

// Thread-local storage - unified with Gzip implementation
thread_local thread_local_brotli_data_t CTaskWorkerBrotli::m_tlbd;
thread_local BrotliDecoderState *CTaskWorkerBrotli::m_decoder_state;


// 重置Brotli decoder状态的辅助函数
void CTaskWorkerBrotli::reset_brotli_decoder()
{
  if (m_decoder_state) {
    BrotliDecoderDestroyInstance(m_decoder_state);
    m_decoder_state = nullptr;
  }

  // 创建新的decoder实例以获得干净状态
  m_decoder_state = BrotliDecoderCreateInstance(nullptr, nullptr, nullptr);
}

int CTaskWorkerBrotli::deal_data(const TaskWorkerData *ptwd)
{
    thread_local_brotli_data_t *ptlbd = &CTaskWorkerBrotli::m_tlbd;

    reset_brotli_decoder();

    return get_parser()->worker_routine_http_brotli_inner(ptlbd, ptwd, m_decoder_state);
}

void CTaskWorkerBrotli::free_data(const TaskWorkerData *ptwd)
{
  const http_brotli_t *p = (const http_brotli_t *)ptwd;
  get_parser()->free_http_brotli_inner(p);

  delete p;
}

void CTaskWorkerBrotli::init(void)
{
  const size_t buf_maxsize = HTTP_RESPONSE_BODY_MAX_LENGTH;
  const int brotli_padding = 8;
  const size_t body_buf_size = (buf_maxsize + brotli_padding);
  thread_local_brotli_data_t *ptlbd = &CTaskWorkerBrotli::m_tlbd;
  ptlbd->buf_maxsize = buf_maxsize;
  ptlbd->body_buf_size = body_buf_size;
  ptlbd->body = new char[body_buf_size];
  memset(ptlbd->body, 0, sizeof(char) * (body_buf_size));
  ptlbd->body2 = new char[body_buf_size];
  memset(ptlbd->body2, 0, sizeof(char) * (body_buf_size));

  m_decoder_state = BrotliDecoderCreateInstance(nullptr, nullptr, nullptr);
}

void CTaskWorkerBrotli::fini(void)
{
  thread_local_brotli_data_t *ptlbd = &CTaskWorkerBrotli::m_tlbd;
  delete[] ptlbd->body;
  delete[] ptlbd->body2;

  if (m_decoder_state) {
    BrotliDecoderDestroyInstance(m_decoder_state);
    m_decoder_state = nullptr;
  }
}

// ============================================================================
// CTaskWorkerBrotliParser Implementation
// ============================================================================

int CTaskWorkerBrotliParser::deal_data(const TaskWorkerData *ptwd)
{
  return get_parser()->http_brotli_parser_routine(ptwd);
}

void CTaskWorkerBrotliParser::free_data(const TaskWorkerData *ptwd)
{
  const http_brotli_parser_t *p = (const http_brotli_parser_t*)ptwd;
  get_parser()->free_http_brotli_parser_data(p);

  return;
}
