#include <string.h>
#include "utils.h"
#include "gw_common.h"
#include "http_parser.h"
#include "http_parser_minio_task_worker.h"
#include "display_stats_define.h"

CWorkerQueue *CHttpParser::new_wq_minio_file(void)
{
    m_p_wq[HTTPPARSER_WQ_MINIO_FILE] = m_comm->create_worker_queue();
    CWorkerQueue *pwq = get_wq_minio_file();
    if (NULL == pwq)
    {
        return NULL;
    }

    CGwStats *pgws = m_comm->get_gw_stats();
    ASSERT(pgws != NULL);

    CTaskWorkerMinioFile *ptw = new CTaskWorkerMinioFile();
    ptw->set_parser(this);
    ptw->set_wq(pwq);
    m_p_tw[HTTPPARSER_WQ_MINIO_FILE] = ptw;

    pwq->set_gw_common(m_comm);
    pwq->set_watchdog(m_comm->get_watchdog());
    pwq->set_task_worker(ptw);

    pwq->set_queue_num_and_bytes(m_conf_http_minio_file_queue_max_num, m_conf_http_minio_file_queue_memory_max_size_bytes);
    pwq->set_queue_name(HTTP_MINIO_QUEUE);
    pwq->init();
    pwq->create_queue();
    pwq->adjust_worker_thread_num(m_conf_http_minio_file_thread_num);

    pgws->set_task(pwq->get_queue_name(), pwq, 51);
    pgws->set_qps(HTTP_MINIO_QPS, &pwq->get_stats_task_data()->cnt);
    pgws->set_mem_stat(pwq->get_queue_name(), &pwq->get_queue_mem_size(), &pwq->get_queue_max_mem_size());

    return pwq;
}

void CHttpParser::http_cb_http_minio(upload_http_info_t *p_upload_http_info, int unknown_rule, int analyze)
{
    size_t mem_size = sizeof(http_minio_file_t) 
                    + p_upload_http_info->http_req_info.header_length
                    + p_upload_http_info->http_req_info.body.length
                    + p_upload_http_info->http_rsp_info.header_length
                    + p_upload_http_info->http_rsp_info.body.length;

    http_minio_file_t *p_http_minio_file = NULL;
    upload_http_info *p_upload_http_info_ex = NULL;

    p_http_minio_file = (http_minio_file_t *)malloc(sizeof(http_minio_file_t));
    if (p_http_minio_file == NULL)
    {
        
        goto end;
    }

    p_upload_http_info_ex = (upload_http_info*)malloc(sizeof(upload_http_info));
    if (p_upload_http_info == NULL)
    {
        goto end;
    }

    memcpy(p_upload_http_info_ex, p_upload_http_info, sizeof(upload_http_info));
    p_http_minio_file->unknown_rule = 2;
    p_http_minio_file->analyze = analyze;
    p_http_minio_file->p_upload_http_info = p_upload_http_info_ex;

    if (!get_wq_minio_file()->queue_put_data(p_http_minio_file, mem_size))
    {
        goto end;
    }

    // 释放原始数据，因为已经复制到队列中
    if (p_upload_http_info->found_gzip || p_upload_http_info->found_brotli)
    {
        free(p_upload_http_info);
    }
    return;

    end:
        get_wq_minio_file()->status_count(WQ_QUEUE_FAIL, 1);   /* 记录入队列失败 */
        SAFE_FREE(p_http_minio_file);

        if (p_upload_http_info->found_gzip || p_upload_http_info->found_brotli)
        {
            SAFE_FREE(p_upload_http_info);
        }
        else
        {
            BSTR_SAFE_FREE(p_upload_http_info->http_rsp_info.body.p_value);
        }

        // 根据压缩类型选择正确的释放函数
        if (p_upload_http_info_ex)
        {
            if (p_upload_http_info->found_gzip)
            {
                free_upload_gzip_info(p_upload_http_info_ex);
            }
            else if (p_upload_http_info->found_brotli)
            {
                free_upload_brotli_info(p_upload_http_info_ex);
            }
            else
            {
                free_upload_http_info(p_upload_http_info_ex);
            }
        }

        return;
}


int CHttpParser::http_minio_file_routine(const http_minio_file_t *p_http_minio_file)
{
    char *buffer = NULL;
    size_t buffer_len = 0;
    char str[1024] = "\0";

    if (! m_use_new_event_format)
    {
        buffer = simple_json_encode_inner(p_http_minio_file->p_upload_http_info, &buffer_len, str);
    }
    else
    {
        buffer = new_event_format_encode(p_http_minio_file->p_upload_http_info, &buffer_len);
    }


    if (buffer)
    {
        http_cb_upload_msg(buffer, p_http_minio_file->unknown_rule, p_http_minio_file->analyze, buffer_len, str);
    }

    return 0;
}

void CHttpParser::free_http_minio_file_data(const http_minio_file_t *p_http_minio_file)
{
    if (!p_http_minio_file)
    {
        return;
    }
    upload_http_info *p_upload_http_info = p_http_minio_file->p_upload_http_info;

    // 根据压缩类型选择正确的释放函数
    if (p_upload_http_info->found_gzip)
    {
        BSTR_SAFE_FREE(p_upload_http_info->http_rsp_info.body.p_value);
        free_upload_gzip_info(p_upload_http_info);
    }
    else if (p_upload_http_info->found_brotli)
    {
        BSTR_SAFE_FREE(p_upload_http_info->http_rsp_info.body.p_value);
        free_upload_brotli_info(p_upload_http_info);
    }
    else
    {
        // 普通HTTP数据，使用通用释放函数
        free_upload_http_info(p_upload_http_info);
    }

    free((void*)p_http_minio_file);
}