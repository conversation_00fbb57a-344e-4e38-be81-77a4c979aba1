/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#ifndef __HTTP_PARSER_BROTLI_TASK_WORKER_H__
#define __HTTP_PARSER_BROTLI_TASK_WORKER_H__

#include "http_parser_task_worker.h"
#include "decode.h"
#include "simple_json.h"

class CHttpParser;
struct upload_http_info;
struct http_parser_ext_data;

// brotli解压数据结构 - 与gzip结构体对齐
typedef struct http_brotli
{
  TaskWorkerData twd;

  size_t length;       // 未解压brotli数据长度
  char *s;             // 未解压brotli数据
  upload_http_info_t *p_upload_http_info;
  int deep;            // 嵌套解压的深度。
  int unkown_rule;     //
  int upload_flag;     // 上传文件标识(1 表示上传文件， 0 表示下载文件)
  bool log_to_analyze;
} http_brotli_t;

// Thread-local Brotli decompression state
typedef struct thread_local_brotli_data
{
	BrotliDecoderState *decoder_state;
	size_t buf_maxsize;
	size_t body_buf_size;
	char *body;
	char *body2;

	thread_local_brotli_data() : decoder_state(nullptr),
	                             buf_maxsize(0), body_buf_size(0),
	                             body(nullptr), body2(nullptr) {}
} thread_local_brotli_data_t;

class CTaskWorkerBrotli : public CTaskWorkerHttp
{
public:
  virtual int deal_data(const TaskWorkerData *ptwd);
  virtual void free_data(const TaskWorkerData *ptwd);
  virtual void init(void);
  virtual void fini(void);

protected:
  static thread_local thread_local_brotli_data_t m_tlbd;
  static thread_local BrotliDecoderState *m_decoder_state;

  // 重置Brotli decoder状态的辅助函数
  void reset_brotli_decoder();
};

typedef struct http_brotli_parser
{
  TaskWorkerData twd;
  upload_http_info_t *p_upload_http_info;
  int unknown_rule;
  int upload_flag;
  bool log_to_analyze;
  size_t length;       /* 解压之后的数据长度 */
  char *s;             /* 解压的数据 */
}http_brotli_parser_t;

class CTaskWorkerBrotliParser : public CTaskWorkerHttp
{
public:
  virtual int deal_data(const TaskWorkerData *ptwd);
  virtual void free_data(const TaskWorkerData *ptwd);
};

#endif // __HTTP_PARSER_BROTLI_TASK_WORKER_H__
