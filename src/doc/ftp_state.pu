
@startuml
title ftp 命令解析状态
' ftp 命令解析流程；相关代码：ftp_parser_inner.c, ftp_parser_inner.h
' cat ftp_state.pu | grep "FTP_.*[1-9]$"  | sed 's/-->/ /g;s/:/ /g;s/,/ /g;s/FTP_//g' | awk '{print "XX("$1",",$4",","'\''"$3"'\''"",",$2")"}'

[*] --> FTP_ACCT : A
[*] --> FTP_CDUP : C
[*] --> FTP_DELE : D
[*] --> FTP_EPSV : E
[*] --> FTP_FEAT : F
[*] --> FTP_HELP : H
[*] --> FTP_LIST : L
[*] --> FTP_MODE : M
[*] --> FTP_NLST : N
[*] --> FTP_OPTS : O
[*] --> FTP_PASS : P
[*] --> FTP_QUIT : Q
[*] --> FTP_REIN : R
[*] --> FTP_SITE : S
[*] --> FTP_TYPE : T
[*] --> FTP_USER : U
[*] --> FTP_XCCT : X

FTP_XCCT --> FTP_XCUP : C,1
FTP_XCCT --> FTP_XMKD : M,1
FTP_XCCT --> FTP_XPWD : P,1
FTP_XCCT --> FTP_XRCT : R,1
FTP_XRCT --> FTP_XRCP : C,2
FTP_XRCT --> FTP_XRMD : M,2
FTP_XRCT --> FTP_XRSQ : S,2
FTP_XCCT --> FTP_XSET : S,1
FTP_XSET --> FTP_XSEM : M,3
FTP_XSET --> FTP_XSEN : N,3
FTP_ACCT --> FTP_ABOR : B,1
FTP_ACCT --> FTP_ALLO : L,1
FTP_ACCT --> FTP_APPE : P,1
FTP_ACCT --> FTP_AUTH : U,1
FTP_CDUP --> FTP_CWD : W,1
FTP_EPSV --> FTP_EPRT : R,2
FTP_MODE --> FTP_MKD : K,1
FTP_MODE --> FTP_MDTM : D,1
FTP_NLST --> FTP_NOOP : O,1
FTP_PASS --> FTP_PASV : V,3
FTP_PASS --> FTP_PORT : O,1
FTP_PASS --> FTP_PWD : W,1
FTP_REIN --> FTP_REST : S,2
FTP_REIN --> FTP_RETR : T,2
FTP_REIN --> FTP_RMD : M,1
FTP_REIN --> FTP_RNFR : N,1
FTP_RNFR --> FTP_RNTO : T,2
FTP_SITE --> FTP_SMNT : M,1
FTP_SITE --> FTP_SIZE : Z,2
FTP_SITE --> FTP_STAT : T,1
FTP_SITE --> FTP_SYST : Y,1
FTP_STAT --> FTP_STOR : O,2
FTP_STAT --> FTP_STRU : R,2
FTP_FEAT --> FTP_FEAT : E,1

@enduml
