#!/bin/bash

# 每10秒检查一次
while true
do
    # 检查gw_parser是否存在
    if [ -n "$(pgrep gw_parser)" ]; then
        # 获取RSS（单位KB）
        rss_kb=$(ps -C gw_parser -o rss= | awk '{sum+=$1} END{print sum}')

        # 判断是否超过16GB
        if [ "$rss_kb" -gt 16777216 ]; then
            echo "[`date`] Memory exceeded! Killing gw_parser..."
            echo "[`date`] gw_parser RSS: $rss_kb KB"
            pkill gw_parser
        fi
    else
        echo "[`date`] gw_parser not running."
    fi

    sleep 30
done
