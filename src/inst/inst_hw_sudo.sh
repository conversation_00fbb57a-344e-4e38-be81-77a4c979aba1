#!/bin/bash
source ./inst_function.sh

# 检查运行环境
function check_env()
{
	check_base_env

	if [[ -d ${DEST_PATH} ]]; then
		rm -rf ${DEST_PATH}
	fi

	return
}

# 配置 系统环境
function conf_system()
{
	# /etc/security/limits.conf 文件
	# * - nofile 4096
	# * - nofile 65536
	if [[ `grep '^[^#].*nofile' /etc/security/limits.conf | wc -l` -eq 0 ]]; then
		sudo_ "echo '* - nofile 65536' >> /etc/security/limits.conf"
	fi

	# /etc/selinux/config 文件
	if [[ -f /etc/selinux/config ]]; then
		sudo sed -i -e 's/^SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config
	fi
	# 关闭 selinux
	sudo setenforce 0 >/dev/null 2>&1

	# /etc/sysctl.conf 文件
	if [[ -f /etc/sysctl.conf ]]; then
		if [[ `grep kernel.nmi_watchdog /etc/sysctl.conf | wc -l` -eq 0 ]]; then
			sudo_ "echo 'kernel.nmi_watchdog=0' >> /etc/sysctl.conf"
		fi
		if [[ `grep kernel.hung_task_timeout_secs /etc/sysctl.conf | wc -l` -eq 0 ]]; then
			sudo_ "echo 'kernel.hung_task_timeout_secs=0' >> /etc/sysctl.conf"
		fi
		sudo sysctl -p
	fi

	# 关闭firewalld或iptables并移除启动项
	sudo_ 'command -v systemctl &>/dev/null && systemctl stop firewalld >/dev/null 2>&1 && systemctl disable firewalld >/dev/null 2>&1 || (service iptables stop >/dev/null 2>&1 && chkconfig iptables off >/dev/null 2>&1)'

	# # 调整开机脚本权限
	# if [[ ! -x /etc/rc.d/rc.local ]]; then
	# 	sudo chmod +x /etc/rc.d/rc.local
	# fi

	# 禁用中断分配服务
	sudo systemctl stop irqbalance >/dev/null 2>&1;
	sudo systemctl disable irqbalance >/dev/null 2>&1;
	# 禁用无关的服务
	sudo systemctl stop lvm2-monitor.service >/dev/null 2>&1
	sudo systemctl stop tuned.service >/dev/null 2>&1

	# 增加普通用户
	sudo useradd ${USERNAME} >/dev/null 2>&1

	# 增加license文件路径
	sudo mkdir -p "/opt/licutils/"

	# 增加日志目录
	sudo mkdir -p "/opt/apigw/logs/"
	sudo chown -R ${USERNAME}  "/opt/apigw/logs/"

	# 增加运行状态目录
	sudo mkdir -p "/opt/apigw/run/"
	sudo chown -R ${USERNAME}  "/opt/apigw/run/"

	# 增加配置目录
    sudo mkdir -p "/opt/data/apigw/gwhw/"
	sudo chown -R ${USERNAME}  "/opt/data/apigw/gwhw/"

	if [ $processor -lt 4 ]
	then
		cpuLimit=100000
	else
		cpuLimit=$[processor * 100000 / 4]
	fi

	if [ $mem -lt ********** ]
	then
		memLimit=$[1024 * 1024 * 1024]
	else
		memLimit=$[mem / 4]
	fi

	# 配置cgroup
	if [ $(grep "group gwhw" /etc/cgconfig.conf | wc -l) -eq 1 ]
	then
		lineNumStart=$(cat -n /etc/cgconfig.conf | grep "group gwhw" | awk '{print $1}')
		lineNumEnd=$[lineNumStart + 8]
		sed -i "$lineNumStart, $lineNumEnd d" /etc/cgconfig.conf
	fi

	echo "group gwhw{" >> /etc/cgconfig.conf
	echo -e "\tcpu{" >> /etc/cgconfig.conf
	echo -e "\t\tcpu.cfs_quota_us = 1600000;" >> /etc/cgconfig.conf
	echo -e "\t\tcpu.cfs_period_us = 100000;" >> /etc/cgconfig.conf
	echo -e "\t}" >> /etc/cgconfig.conf
	echo -e "\tmemory{" >> /etc/cgconfig.conf
	echo -e "\t\tmemory.limit_in_bytes = 12884901888;" >> /etc/cgconfig.conf
	echo -e "\t}" >> /etc/cgconfig.conf
	echo "}" >> /etc/cgconfig.conf

	if [ $(grep "/opt/apigw/gwhw/gw_parser" /etc/cgrules.conf | wc -l) -eq 0 ]
	then
		echo "*:/opt/apigw/gwhw/gw_parser cpu,memory gwhw" >> /etc/cgrules.conf
	fi

	sudo systemctl restart cgconfig > /dev/null 2>&1
	sudo systemctl restart cgred > /dev/null 2>&1
	sudo systemctl enable cgconfig > /dev/null 2>&1
	sudo systemctl enable cgred > /dev/null 2>&1

	# 关闭rpcbind服务
	systemctl stop rpcbind.socket > /dev/null 2>&1
	systemctl stop rpcbind > /dev/null 2>&1
	systemctl disable rpcbind.socket > /dev/null 2>&1
	systemctl disable rpcbind > /dev/null 2>&1

	# 直接执行echo "-1" > /proc/sys/kernel/sched_rt_runtime_us会失败，原因未知
	echo "-1" | tee /proc/sys/kernel/sched_rt_runtime_us > /dev/null

	return
}

# 安装主程序
function inst_gw_parser()
{
	# 增加配置目录
	sudo mkdir -p "${DEST_PATH}./"
	sudo mkdir -p "${DEST_PATH}./logs/"
	sudo mkdir -p "${DEST_PATH}./lib/"
	sudo mkdir -p "${DEST_PATH}./kmod/"
	sudo mkdir -p "${DEST_PATH}./kmod/xinchuang/"
	sudo mkdir -p "${DEST_PATH}./tools/"
	sudo mkdir -p "${DEST_PATH}./stats_srv"
	sudo mkdir -p "${DEST_PATH}./stats_srv/tmpdir/"
    sudo cp -f version.txt "${DEST_PATH}./"
	sudo cp -f gw_parser "${DEST_PATH}./"
    sudo cp magic.mgc "${ETC_PATH}./"

	inst_plugin_config

	sudo chmod +x "${DEST_PATH}./"gw_parser
	sudo cp -f liblicutils.so "${DEST_PATH}./lib/"
	sudo cp -f libfile_type.so "${DEST_PATH}./lib/"
	sudo cp -f libnacos-cli.so "${DEST_PATH}./lib/"
	sudo cp -f libcurl.so "${DEST_PATH}./lib/"
	sudo cp -f librdkafka.so "${DEST_PATH}./lib/"
	sudo cp -f libaws-cpp-sdk*.so "${DEST_PATH}./lib/"
	sudo cp -f libbrotli.so "${DEST_PATH}./lib/"

	# kernel_version=$(uname -r)
	# if [ $(ls pfring_ko | grep "$kernel_version" | wc -l) -eq 0 ]; then
	# 	echo "Warning: 没有${kernel_version}内核对应的pfring驱动！！！"

	# else
	# 	cp -f pfring_ko/$kernel_version/pf_ring.ko "${DEST_PATH}./kmod/"
	# fi

	sudo cp -f *.sh "${DEST_PATH}./tools/"
	sudo cp -f *.py "${DEST_PATH}./tools/"
	sudo cp -f dpdk-devbind "${DEST_PATH}./tools/"
	sudo cp -f set_irq_affinity "${DEST_PATH}./tools/"
	sudo cp -f dpdk_alloc_GB "${DEST_PATH}./tools/"
	sudo cp -f licutils_demo "${DEST_PATH}./tools/"

	if [[ -f dpdk_black_list.txt && ! -f /opt/data/apigw/gwhw/dpdk_black_list.txt ]]; then
		sudo cp -f dpdk_black_list.txt /opt/data/apigw/gwhw/
	fi

	if [[ ! -d "/opt/urlbase/" ]];then
		sudo mkdir -p "/opt/urlbase/"
		sudo cp -f url_filter_base.file /opt/urlbase/
	fi

	if [[ ! -f "/opt/urlbase/url_filter_base.file" ]];then
		sudo cp -f url_filter_base.file /opt/urlbase/
	fi

	# 提高ssh服务的优先级,增加ssh连接的响应速度
	# if [[ -f renice_sshd.sh ]]; then
	# 	sh renice_sshd.sh
	# fi

	# 安装时SO库需要进行符号连接
	# 删除当前所有库文件
	sudo rm -f "${DEST_PATH}./lib/"lib*.so.*
	sudo cp -rf lib/* "${DEST_PATH}./lib/"
	cd "${DEST_PATH}./lib/"
	ls lib*.so.* | awk -Fso. '{printf("sudo ln -sf %s %sso\n", $0, $1)}' | sh

	# 给protobuf创建动态链接
	ln -s libprotobuf.so.31.0.1 libprotobuf.so.31

	if [[ -f libgcrypt.so.20.2.5 ]];then
		ln -s libgcrypt.so.20.2.5 libgcrypt.so.20
	fi

	if [[ -f libgpg-error.so.0.27.0 ]];then
		ln -s libgpg-error.so.0.27.0 libgpg-error.so.0
	fi

	if [[ -f libpfring.so ]];then
    	ln -sf libpfring.so libpfring.so.8
    fi

	if [[ -f libcurl.so ]];then
       	ln -sf libcurl.so libcurl.so.4
    fi

   	if [[ -f librdkafka.so ]];then
           	ln -sf librdkafka.so librdkafka.so.1
    fi

	cd - >>/dev/null

	sudo cp -f supervisord "${DEST_PATH}./"
	sudo cp -f supervisord_hw.conf "${DEST_PATH}./"
	# sudo cp -f "stats/"*.sh  "${DEST_PATH}./stats_srv"
	# sudo cp -f "stats/"*.conf  "${DEST_PATH}./stats_srv"
	# sudo cp -f "stats/gw_stat_srv"  "${DEST_PATH}./stats_srv"
	sudo cp -f "stats/gw_stats_srv-exe"  "${DEST_PATH}./stats_srv"
	sudo cp -f "stats/"*.conf  "${DEST_PATH}./stats_srv"
	# 配置文件
	if [[ ! -f "${DEST_PATH}./stats_srv/gw_stats_srv.conf" ]]; then
		sudo cp -f "stats/gw_stats_srv.conf" "${DEST_PATH}./stats_srv/"
	fi

	# 安装openssl库
	if [[ -f openssl-bin-1.1.1w.zip ]]; then
		if [[ ! -d /opt/openssl/ ]]; then
			sudo mkdir /opt/openssl/
		fi
		sudo -qo unzip openssl-bin-1.1.1w.zip -d /opt/openssl/ >/dev/null 2>&1
	fi

	sudo chown -R ${USERNAME}  "${DEST_PATH}./"

	#配置vsftp配置文件
	#conf_vsftp

	#sudo sh "${DEST_PATH}./tools/"agent_upload_quota.sh

	#配置systemctl
	inst_config_systemctl

	if [ "TOOL" == "$product" ]; then
	    /opt/apigw/gwhw/supervisord -c /opt/apigw/gwhw/supervisord_hw.conf ctl stop gw_parser
	fi
}

function main()
{
	func_log  parse_parameter $@
	func_log  check_env
	func_log  conf_system
	func_log  inst_gw_parser
	func_log  start_crontab
	func_log  install_agent_server
	return
}

main $@
