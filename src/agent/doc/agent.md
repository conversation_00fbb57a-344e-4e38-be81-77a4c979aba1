#### 0.agent帮助

```
命令: bash agent help 或者bash agent
```

#### １.配置agent

```
命令: sudo bash agent config

写着[required]的选项都是需要用户根据实际情况修改的，没有写着[required]的选项，可以不修改
```

#### ２.检查agent

```
命令: sudo bash agent health

其中最主要的一项是[tet ftp],若显示[OK],表示FTP是通的；若显示[Failed]，表示FTP不通
```

#### ３.启动/停止/重启agent

```
启动命令: sudo bash agent start
停止命令: sudo bash agent stop
重启命令: sudo bash agent restart

启动之后建议使用 agent health 命令检查agent是否已经运行正常
```

#### ４.查看连接速度

```
命令: sudo bash agent speed

该命令需要iftop工具支持，若客户机没有这个命令，就无法使用
```

#### ５.查看cpu占用率

```
命令: sudo bash agent cpu

输出结果统计了当前系统每个CPU核的使用率，以及所有核的平均使用率．方便维护人员
查看当前系统的cpu使用信息．

```

#### ６.查看日志

```
运行过程中的日志，在agent的安装目录下，有个log目录，每个模块都有自己的日志

模块说明:
Capture : 运行tcpdump抓取pcap文件
Upload  : 上传pcap文件到FTP服务器中
Monitor : 监控保存pcap文件的目录是否满了
Daemon  : 监控agent服务是否正常运行，如果agent不正常，会重启agent服务
```

#### ７.开启开机启动

```
在/etc/rc.local文件中，加入如下命令
bash agent安装目录/agent start
```

#### ８.关闭开机启动

```
在/etc/rc.local文件中，去除添加的agent启动命令
```

#### 工作原理介绍
```
1) 抓包:agent启动后，会开启tcpdump服务，按照一定的时间间隔{可配置},源源不断的把pcap文件
   写入到pcap目录{可配置}，pcap文件名格式以日期时间命名

2) 上传:当一个pcap文件写完后,会在pcap目录下生成一个新的pcap文件，同时会把已完成的pcap文
   件上传到FTP,上传成功后这个文件就会被删除．目前没有保留老的pcap文件的功能，看需求开发

3) 磁盘监控: 当pcap目录下的所有pcap文件总和超过了设定的最大值{可配置}，会停止抓包，直到目
   录有空闲．建议目录的最大容量根据客户流量的实际大小进行设置，不要设置过小，否则容易出现
   pcap文件堆积，建议设置在１０Ｇ以上．

4) cpu监控: 若整个系统的cpu占用率达到了某个阈值(可配置),会停止抓包．直到cpu使用率恢复正常
   这个参数需要根据客户实际情况设置，若希望一直持续抓包，可以把阈值设置成101

```
