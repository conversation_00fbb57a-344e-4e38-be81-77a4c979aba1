#!/bin/bash

VERSION=0.7
TOOL=$(basename $0)
WORK_PATH=$(cd $(dirname $0); pwd)

. $WORK_PATH/bin/health.sh

#set -e
if [ -z $EDITOR ];then
    EDITOR=vi
fi

help() {
    echo QuanZhi agent tools for Pcap, version ${VERSION}
    echo Usage:
    echo -e "\t" ${TOOL} help
    echo -e "\t" ${TOOL} config : edit config.json
    #echo -e "\t" ${TOOL} install : install agent client
    #echo -e "\t" ${TOOL} uninstall : uninstall agent client
    echo -e "\t" ${TOOL} start : start agent service
    echo -e "\t" ${TOOL} stop : stop agent service
    echo -e "\t" ${TOOL} restart : restart agent service
    echo -e "\t" ${TOOL} health : check agent service health
    echo -e "\t" ${TOOL} cpu : check cpu usage 
    echo -e "\t" ${TOOL} speed : display connections speed \(need iftop command\)
}

config() {
    $EDITOR $WORK_PATH/conf/ag.ini
    $WORK_PATH/bin/checkcfg.sh
}

check_user()
{
    USER=`whoami`

    if [ x"$USER" != x"root" ];then
        echo "Please run as root"
        exit 
    fi
}

check_config()
{
    $WORK_PATH/bin/checkcfg.sh
    if [ $? -ne 0 ];then
        exit 
    fi
}

start() {
    check_user

    check_config

    command -v fuser > /dev/null
    if [[ ! $? -eq 0 ]];then
        echo "command fuser not exist, please install, you can use yum install -y psmisc"
        exit 1
    fi

    check_capture silent
    if [ $? -eq 1 ];then
       echo "agent already running" 
       return 0
    fi

    check_upload silent
    if [ $? -eq 1 ];then
       echo "agent already running" 
       return 0
    fi

    check_stats silent
    if [ $? -eq 1 ];then
        echo "agent already running"
        return 0
    fi

    check_monitor silent
    if [ $? -eq 1 ];then
       echo "agent already running" 
       return 0
    fi

    check_daemon silent
    if [ $? -eq 1 ];then
       echo "agent already running" 
       return 0
    fi

    init

    check_env
    if [ $? -ne 0 ];then
        echo "Agent enviroment check failed. please check again"
        return 1
    fi

    start_agent

    echo "start agent success"
}

stop() {
    check_user

    stop_agent

    echo "stop agent success"
}

restart() {
    stop
    start
}

init(){
    $WORK_PATH/bin/init.sh
}

health() {
    check_user 
    check_config
    init
    check_agent
    check_env
}

cpu() {
    $WORK_PATH/bin/cpu.sh debug
}

speed() {
    check_user
    $WORK_PATH/bin/speed.sh
}

if [ $# == 0 ];then
    help
elif [ $1 == "help" ];then
    help
elif [ $1 == "config" ];then
    config
elif [ $1 == "start" ];then
    start
elif [ $1 == "stop" ];then
    stop
elif [ $1 == "restart" ];then
    restart
elif [ $1 == "health" ];then
    health 
elif [ $1 == "cpu" ];then
    cpu 
elif [ $1 == "speed" ];then
    speed 
else
    help
fi
