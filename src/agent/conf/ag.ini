[CAPTURE]
INTERFACE=enp0s8
;网卡接口名[required]
ROTATE_SECS=30
;生成pcap文件的时间间隔(秒), 根据实际需要修改,默认可以不修改
PCAP_OUTPUT=/var/pcap
;Pcap文件输出的目录, 建议配置在大磁盘空间目录下[required]
PCAP_OUTPUT_CAPCITY=10240
;PCAP文件临时保存目录大小, 大于该值,则暂停抓包.单位MB[required]
TCPDUMP_WAIT_SECS=30
;若抓包暂停后, 等待多少时间重新尝试抓包(秒), 默认不需要修改
CAPTURE_PORT=80|8080|8000
;需要捕获流量的端口号, 用'|'分割[required]
;IP_FILTER=*************|************
;需要过滤的IP [黑名单]
;NET_FILTER=192.168.0|192.178.0
;需要过滤的网络段 [黑名单]

[UPLOAD]
UPLOAD_METHOD=0
;0表示使用ftp上传文件 1表示采用scp免密登录
UPLOAD_PATH=/opt/pcap/task/
;UPLOAD_METHOD为1时使用，为上传文件的目录 
FTPSERVER=*************
;FTP Server[required]
FTPUSER=ftp
;FTP 用户名[required]
FTPPASSWORD=ftp
;FTP 密码[required]
PROTOCOL=FTP
;上传协议[required]
SCP_LOGIN_USER=root
;UPLOAD_METHOD为1时使用，登陆到用户名 
SCP_AS_USER=root
;UPLOAD_METHOD为1时使用，执行上传操作时，使用到用户
LIMITRATE=10M
;上传文件限速(单位为MB),可根据实际情况进行修改[required]
AGENT_IP=
;INTERFACE字段指定网卡的ip地址，默认为自动获取。

[MONITOR]
CHECK_SECS=10
;监控时间间隔(秒), 默认不需要修改
CPUUSAGE_KILL_THRESHOLD=35
;当系统的CPU平均使用率超过阈值, 抓包就会停止(百分比), 由用户根据实际情况修改[required]
CPUUSAGE_RECOVER_THRESHOLD=30
;当系统的CPU平均使用率低于阈值，抓包就会启动(百分比)，由用户根据实际情况修改[required]

[DAEMON]
CHECK_SECS=60
;Daemon的检查时间间隔(秒), 默认不需要修改

[LOG]
DEBUG=false
;如果设置为true, 终端界面会打印出日志, 默认不需要修改

[STATS]
STATS_SEC=60
; 上传状态时间间隔(秒)
STATS_DST_IP=************:8080
; 上传状态的目的地址
EVENTS=audit-core/agent/save.do
