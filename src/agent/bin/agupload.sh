#!/bin/bash

MODULE=Upload

CURRENTDIR=$(cd $(dirname $0); pwd)
. $CURRENTDIR/parse.sh
. $CURRENTDIR/log.sh

#配置文件
CFG=$CURRENTDIR/../conf/ag.ini

#网络接口 
INTERFACE=`readIni $CFG CAPTURE INTERFACE`

#Pcap 目录
PCAP_OUTPUT=`readIni $CFG CAPTURE PCAP_OUTPUT`

UPLOAD_METHOD=`readIni $CFG UPLOAD UPLOAD_METHOD`

UPLOAD_PATH=`readIni $CFG UPLOAD UPLOAD_PATH`

#协议
if [[ $UPLOAD_METHOD == "0" ]];then
    PROTOCOL=`readIni $CFG UPLOAD PROTOCOL`
else
    PROTOCOL=scp
fi

#FTP server
FTPSERVER=`readIni $CFG UPLOAD FTPSERVER`

#FTP user
FTPUSER=`readIni $CFG UPLOAD FTPUSER`

#FTP password
FTPPASSWORD=`readIni $CFG UPLOAD FTPPASSWORD`

#FTP limit rate
LIMITRATE=`readIni $CFG UPLOAD LIMITRATE`

# agent ip
AGENT_IP=`readIni $CFG UPLOAD AGENT_IP`

#LOG
LOG=$CURRENTDIR/../log/upload.log
DEBUG=`readIni $CFG LOG DEBUG`

SCP_AS_USER=`readIni $CFG UPLOAD SCP_AS_USER`

if [ x"$SCP_AS_USER" == x"" ];then
    SCP_AS_USER="root"
fi

SCP_LOGIN_USER=`readIni $CFG UPLOAD SCP_LOGIN_USER`

if [ x"$SCP_LOGIN_USER" == x"" ];then
    SCP_LOGIN_USER= "root"
fi

FINISH=/tmp/finish
upload_stats=/tmp/upload_stats

log_print $LOG "[$MODULE] [Info] Start $MODULE"  $DEBUG

sleep 1

#IP地址
if [ x"$AGENT_IP" != x"" ]; then
    IPADDR=$AGENT_IP
elif [ x"$INTERFACE" == x"lo" ];then
    IPADDR="127.0.0.1"
elif [ x"$INTERFACE" == x"" ];then
    log_print $LOG "[$MODULE] [Error] INTERFACE name is null" $DEBUG
    IPADDR=""
# elif [ x"`ifconfig $INTERFACE 2>/dev/null | grep '\<ether\>' | awk '{print $2}'`" = x"" ]; then
#     log_print $LOG "[$MODULE] [Error] INTERFACE name($INTERFACE) error" $DEBUG
#     IPADDR=""
else
    # IPADDR=$(ifconfig | tr '\n' '=' | sed 's/==/\n/g' | grep "`ifconfig $INTERFACE | grep '\<ether\>' | awk '{print $2}'`" | tr '=' '\n' | grep '\<inet\>' | awk '{print $2}')
    if [[ -f /etc/redhat-release ]];then #centos
            if [[ `cat /etc/redhat-release | grep "\s6.*" | wc -l` -eq 1 ]] ;then
                if [[ `ifconfig $INTERFACE 2>/dev/null | grep HWaddr | awk '{print $5}' | wc -l` -eq 0 ]];then
                    log_print $LOG "[$MODULE] [Error] INTERFACE name($INTERFACE) error" $DEBUG
                    IPADDR=""
                else
                    IPADDR=$(ifconfig | tr '\n' '=' | sed 's/==/\n/g'| grep `ifconfig $INTERFACE | grep HWaddr | awk '{print $5}'` | tr '=' '\n' | grep 'inet addr' | awk -F ':' '{print $2}' | awk '{print $1}')
                fi
            else
                if [[ `ifconfig $INTERFACE 2>/dev/null | grep '\<ether\>' | awk '{print $2}' | wc -l` -eq 0 ]];then
                    log_print $LOG "[$MODULE] [Error] INTERFACE name($INTERFACE) error" $DEBUG
                    IPADDR=""
                else
                    IPADDR=$(ifconfig | tr '\n' '=' | sed 's/==/\n/g' | grep "`ifconfig $INTERFACE | grep '\<ether\>' | awk '{print $2}'`" | tr '=' '\n' | grep '\<inet\>' | awk '{print $2}')
                fi
            fi
    elif [[ -f /etc/issue ]];then   #ubuntu
        IPADDR=$(ifconfig $INTERFACE | grep '\<inet\>' | awk '{print $2}' | awk -F ':' '{print $2}')
    fi
fi

if [[ ! -f "$upload_stats" ]];then
    touch $upload_stats
else
    rm -f $upload_stats
    touch $upload_stats
fi

while true
do
    sleep 5
    
    #PCAP文件列表(排序)
    PCAP_LIST=`ls $PCAP_OUTPUT/*.pcap 2>/dev/null | sort -n`

    touch $FINISH

    for file in $PCAP_LIST
    do
        if [ x"$file" == x"" ];then
            continue
        fi
        
        #如果文件正在被tcpdump打开,则继续等待
        fuser $file 1>/dev/null 2>/dev/null
        if [ $? == 0 ];then
            continue
        fi

	DATE=`date +%Y%m%d%H%M%S.%N`
 	UPLOAD_PREFIX=${IPADDR}_${DATE}

        if [[ x"$UPLOAD_METHOD" == x"0" ]];then
            if [[ x"$PROTOCOL" == x"FTP" ]];then
                #上传pcap文件到FTP
                log_print $LOG "[$MODULE] [Debug] curl --limit-rate $LIMITRATE -u $FTPUSER:$FTPPASSWORD -T ${file} ftp://$FTPSERVER/$UPLOAD_DIR${UPLOAD_PREFIX}.pcap"  $DEBUG
                curl --limit-rate $LIMITRATE  -u $FTPUSER:$FTPPASSWORD -T ${file} ftp://$FTPSERVER/$UPLOAD_DIR${UPLOAD_PREFIX}.pcap -S -s 2>>$LOG
            elif [[ x"$PROTOCOL" == x"FTPS" ]];then
                #上传PCAP文件至FTPS服务器
                log_print $LOG "[$MODULE] [Debug] curl --limit-rate $LIMITRATE -k --ftp-ssl -u $FTPUSER:$FTPPASSWORD -T ${file} ftp://$FTPSERVER/$UPLOAD_DIR${UPLOAD_PREFIX}.pcap" $DEBUG
                curl --limit-rate $LIMITRATE -k --ftp-ssl -u $FTPUSER:$FTPPASSWORD -T ${file} ftp://$FTPSERVER/${UPLOAD_DIR}${UPLOAD_PREFIX}.pcap -S -s 2>>$LOG
            else
                echo "invaild protocol $PROTOCOL,please check conf file"
                exit 1
            fi
        else
            log_print $LOG "[$MODULE] [Debug] runuer ${SCP_AS_USER} -c \"scp ${file} ${SCP_LOGIN_USER}@$FTPSERVER:$UPLOAD_PATH/${UPLOAD_PREFIX}.pcap\"" $DEBUG
            chmod 777 $file
            #echo "runuser ${SCP_AS_USER} -c \"scp ${file} ${SCP_LOGIN_USER}@$FTPSERVER:$UPLOAD_PATH/${UPLOAD_PREFIX}.pcap\"" >>$LOG
            runuser ${SCP_AS_USER} -c "scp ${file} ${SCP_LOGIN_USER}@$FTPSERVER:$UPLOAD_PATH/${UPLOAD_PREFIX}.pcap" 2>>$LOG
        fi

        file_name=`echo $file | awk -F '/' '{print $NF}'`
        if [[ $? -eq 0 ]];then
            file_stats=success
        else
            file_stats=fail
        fi
        file_size=`stat -c "%s" $file`
        file_protocol=$PROTOCOL

        echo "$file_name $file_size $file_stats $file_protocol" >>  $upload_stats

        #删除pcap
        if [ $? -eq 0 ];then
            log_print $LOG "[$MODULE] [Debug] [Delete pcap] rm  -fr $file" $DEBUG
            #RM=`which --skip-alias rm`
            rm -fr $file 2>>$LOG
        fi
    done 
done

