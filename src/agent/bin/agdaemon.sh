#!/bin/bash

MODULE=Daemon

CURRENTDIR=$(cd $(dirname $0); pwd)
. $CURRENTDIR/parse.sh
. $CURRENTDIR/log.sh
. $CURRENTDIR/health.sh

#配置文件
CFG=$CURRENTDIR/../conf/ag.ini

#LOG
LOG=$CURRENTDIR/../log/daemon.log
DEBUG=`readIni $CFG LOG DEBUG`

#间隔
CHECK_SECS=`readIni $CFG DAEMON CHECK_SECS`

#恢复抓包的CPU监控阈值
CPUUSAGE_RECOVER_THRESHOLD=`readIni $CFG MONITOR CPUUSAGE_RECOVER_THRESHOLD`

log_print $LOG "[$MODULE] [Info] Start $MODULE"  $DEBUG

sleep 1

while true
do
    sleep $CHECK_SECS

    check_capture silent
    if [ $? -eq 0 ];then
        cpuusage=`get_cpu_usage`

        if [ $cpuusage -le $CPUUSAGE_RECOVER_THRESHOLD ];then
            log_print $LOG "[$MODULE] [Warning] Capture not running, restart capture"  $DEBUG
	        stop_capture
            start_module agcapture >/dev/null
        else
            log_print $LOG "[$MODULE] [Warning] Not restart capture, ${cpuusage}%(current cpu usage) > ${CPUUSAGE_RECOVER_THRESHOLD}%(recover capture threshold)" $DEBUG
        fi
        continue
    fi

    check_upload silent
    if [ $? -eq 0 ];then
        log_print $LOG "[$MODULE] [Warning] Upload not running, restart upload"  $DEBUG
	stop_upload
        start_module agupload >/dev/null
        continue
    fi

    check_stats silent
    if [ $? -eq 0 ];then
        log_print $LOG "[$MODULE] [Warning] stats not running, restart upload" $DEBUG
        stop_stats
        start_module agstats >/dev/null
        continue
    fi
    
    check_monitor silent
    if [ $? -eq 0 ];then
        log_print $LOG "[$MODULE] [Warning] Monitor not running, restart monitor"  $DEBUG
	stop_monitor
        start_module agmonitor >/dev/null
        continue
    fi

done
