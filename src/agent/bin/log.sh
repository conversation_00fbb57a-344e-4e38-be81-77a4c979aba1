#!/bin/bash

log_print()
{
    LOG_FILE=$1
    LOG_CONTENT=$2
    DEBUG=$3
    if [[ x"$DEBUG" == x"true" ]]; then
        echo "$LOG_CONTENT"
    fi

    LOG_SIZE=`stat -c "%s" $LOG_FILE 2>/dev/null` 
    if [ x"$LOG_SIZE" == x"" ];then
        LOG_SIZE=0
    fi

    #日志超过100M, 会重新创建
    if [[ $LOG_SIZE -ge 102400000 ]];then
        # RM=`which --skip-alias rm`
        command rm $LOG_FILE -fr  2>/dev/null
    fi

    TIME=`date "+%Y-%m-%d %H:%M:%S"`
    echo "${TIME} $LOG_CONTENT" >> $LOG_FILE
}
