#! /bin/bash

CURRENTDIR=$(cd $(dirname $0); pwd)
. $CURRENTDIR/parse.sh

#配置文件
CFG=$CURRENTDIR/../conf/ag.ini
if [ ! -e $CFG ];then
    echo "Can not find $CFG"
    exit 1
fi

#[CAPTURE]字段检测
INTERFACE=`readIni $CFG CAPTURE INTERFACE`
if [ x"$INTERFACE" == x"" ];then
    echo "Invalid INTERFACE filed. Please check $CFG"
    exit 1
fi

ROTATE_SECS=`readIni $CFG CAPTURE ROTATE_SECS`
if [ x"$ROTATE_SECS" == x"" ];then
    echo "Invalid ROTATE_SECS filed. Please check $CFG"
    exit 1
fi

PCAP_OUTPUT=`readIni $CFG CAPTURE PCAP_OUTPUT`
if [ x"$PCAP_OUTPUT" == x"" ];then
    echo "Invalid PCAP_OUTPUT filed. Please check $CFG"
    exit 1
fi

PCAP_OUTPUT_CAPCITY=`readIni $CFG CAPTURE PCAP_OUTPUT_CAPCITY`
if [ x"$PCAP_OUTPUT_CAPCITY" == x"" ];then
    echo "Invalid PCAP_OUTPUT_CAPCITY filed. Please check $CFG"
    exit 1
fi
TCPDUMP_WAIT_SECS=`readIni $CFG CAPTURE TCPDUMP_WAIT_SECS`
if [ x"$TCPDUMP_WAIT_SECS" == x"" ];then
    echo "Invalid TCPDUMP_WAIT_SECS filed. Please check $CFG"
    exit 1
fi
#CAPTURE_PORT=`readIni $CFG CAPTURE CAPTURE_PORT`
#if [ x"$CAPTURE_PORT" == x"" ];then
#    echo "Invalid CAPTURE_PORT filed. Please check $CFG"
#    exit 1
#fi

#[UPLOAD]字段检测
UPLOAD_METHOD=`readIni $CFG UPLOAD UPLOAD_METHOD`
if [ x"$UPLOAD_METHOD" == x"" ];then
    echo "Invaild UPLOAD_METHOD filed, Please check $CFG"
    exit 1
fi

UPLOAD_PATH=`readIni $CFG UPLOAD UPLOAD_PATH`
if [ x"$UPLOAD_METHOD" == x"" ];then
    echo "Invaild UPLOAD_PATH filed, Please check $CFG"
    exit 1
fi

FTPSERVER=`readIni $CFG UPLOAD FTPSERVER`
if [ x"$FTPSERVER" == x"" ];then
    echo "Invalid FTPSERVER filed. Please check $CFG"
    exit 1
fi

FTPUSER=`readIni $CFG UPLOAD FTPUSER`
if [ x"$FTPUSER" == x"" ];then
    echo "Invalid FTPUSER filed. Please check $CFG"
    exit 1
fi

FTPPASSWORD=`readIni $CFG UPLOAD FTPPASSWORD`
if [ x"$FTPPASSWORD" == x"" ];then
    echo "Invalid FTPPASSWORD filed. Please check $CFG"
    exit 1
fi

PROTOCOL=`readIni $CFG UPLOAD PROTOCOL`
if [ x"$PROTOCOL" == x"" ];then
    echo "Invaild PROTOCOL filed. please check $CFG"
fi

LIMITRATE=`readIni $CFG UPLOAD LIMITRATE`
if [ x"$LIMITRATE" == x"" ];then
    echo "Invaild LIMITRATE filed, please check $CFG"
fi

#[MONNITOR字段检测]
CHECK_SECS=`readIni $CFG MONITOR CHECK_SECS`
if [ x"$CHECK_SECS" == x"" ];then
    echo "Invalid CHECK_SECS filed. Please check $CFG"
    exit 1
fi

CPUUSAGE_KILL_THRESHOLD=`readIni $CFG MONITOR CPUUSAGE_KILL_THRESHOLD`
if [ x"$CPUUSAGE_KILL_THRESHOLD" == x"" ];then
    echo "Invalid CPUUSAGE_KILL_THRESHOLD filed. Please check $CFG"
    exit 1
fi

CPUUSAGE_RECOVER_THRESHOLD=`readIni $CFG MONITOR CPUUSAGE_RECOVER_THRESHOLD`
if [ x"$CPUUSAGE_RECOVER_THRESHOLD" == x"" ];then
    echo "invaild CPUUSAGE_RECOVER_THRESHOLD filed. Please check $CFG"
    exit 1
fi

#[DAEMON字段检测]
CHECK_SECS=`readIni $CFG DAEMON CHECK_SECS`
if [ x"$CHECK_SECS" == x"" ];then
    echo "Invalid CHECK_SECS filed. Please check $CFG"
    exit 1
fi

#[LOG字段检测]
DEBUG=`readIni $CFG LOG DEBUG`
if [ x"$DEBUG" == x"" ];then
    echo "Invalid DEBUG filed. Please check $CFG"
    exit 1
fi

#[stats字段检测]
STATS_SEC=`readIni $CFG STATS STATS_SEC`
if [ x"$STATS_SEC" == x"" ];then
    echo "Invaild STATS_SEC filed, please check $CFG"
    exit 1
fi

STATS_DST_IP=`readIni $CFG STATS STATS_DST_IP`
if [ x"$STATS_DST_IP" == x"" ];then
    echo "Invaild STATS_DST_IP filed. please check $CFG"
    exit 1
fi
