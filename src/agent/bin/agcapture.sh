#!/bin/bash

MODULE=Capture

CURRENTDIR=$(cd $(dirname $0); pwd)
. $CURRENTDIR/parse.sh
. $CURRENTDIR/health.sh
. $CURRENTDIR/log.sh

#配置文件
CFG=$CURRENTDIR/../conf/ag.ini

#网卡接口
INTERFACE=`readIni $CFG CAPTURE INTERFACE`

#pcap轮替时间，单位秒，表示每隔多少秒生成一个pcap文件
ROTATE_SECS=`readIni $CFG CAPTURE ROTATE_SECS`

#Pcap 输出目录
PCAP_OUTPUT=`readIni $CFG CAPTURE PCAP_OUTPUT`

#CAPTURE_PORT
CAPTURE_PORT=`readIni $CFG CAPTURE CAPTURE_PORT`

IP_FILTER=`readIni $CFG CAPTURE IP_FILTER`

NET_FILTER=`readIni $CFG CAPTURE NET_FILTER`

PFRING_TCPDUMP=`readIni $CFG CAPTURE PFRING_TCPDUMP`

#Pcap 文件名格式
PCAP_FORMAT="%Y_%m%d_%H%M_%S.pcap"

#LOG
LOG=$CURRENTDIR/../log/capture.log
DEBUG=`readIni $CFG LOG DEBUG`

#WAIT TIME
TCPDUMP_WAIT_SECS=`readIni $CFG CAPTURE TCPDUMP_WAIT_SECS`

#CPU监控阈值
CPUUSAGE_KILL_THRESHOLD=`readIni $CFG MONITOR CPUUSAGE_KILL_THRESHOLD`

log_print $LOG "[$MODULE] [Info] Start $MODULE"  $DEBUG

#等待cpu.sh启动
sleep 5

get_filter_list() {
    IFS="|"
    PORT_LIST=($CAPTURE_PORT)
    PORT_NUM=${#PORT_LIST[@]}

    IP_LIST=($IP_FILTER)
    IP_NUM=${#IP_LIST[@]}

    NET_LIST=($NET_FILTER)
    NET_NUM=${#NET_LIST[@]}

    let counter=0
    for PORT in ${PORT_LIST[@]}
    do
        PORT_RULE=$PORT_RULE"port $PORT"
        counter=$counter+1
        if [[ $counter -ne $PORT_NUM ]];then
            PORT_RULE=$PORT_RULE" or "
        else
            break
        fi
    done

    let counter=0
    for IP in ${IP_LIST[@]}
    do
        IP_RULE=$IP_RULE"host $IP"

        counter=$counter+1
        if [[ $counter -ne $IP_NUM ]];then
            IP_RULE=$IP_RULE" or "
        else
            break
        fi
    done

    let counter=0
    for NET in ${NET_FILTER[@]}
    do 
        NET_RULE=$NET_RULE"net $NET"
        counter=$counter+1
        if [[ $counter -ne $NET_NUM ]];then
            NET_RULE=$NET_RULE" or "
        else
            break
        fi
    done

    if [ x"$IP_RULE" != x"" ];then
        if [ x"$NET_RULE" != x"" ];then
            FILTER_RULE="not ("$IP_RULE" or "$NET_RULE")"
        else
            FILTER_RULE="not ("$IP_RULE")"
        fi
    else
        if [ x"$NET_RULE" != x"" ];then
            FILTER_RULE="not ("$NET_RULE")"
        fi
    fi

    if [ x"$PORT_RULE" != x"" ];then
        if [ x"$FILTER_RULE" != x"" ];then
            FILTER_RULE="("$PORT_RULE") and "$FILTER_RULE
        else
            FILTER_RULE="("$PORT_RULE")"
        fi
    fi

    echo "$FILTER_RULE"
}


capture_run() {
    INTERFACE_NOW=$1
    while true
    do
        check_capcity silent
        CURRENT_SIZE=$?

        if [ $CURRENT_SIZE != 0 ];then
            log_print $LOG "[$MODULE] [Warninng] $PCAP_OUTPUT capcity is $CURRENT_SIZE, wait $TCPDUMP_WAIT_SECS secs try agin"  $DEBUG
            sleep $TCPDUMP_WAIT_SECS
            continue
        fi

        cpuusage=$(get_cpu_usage)
        if [[ $cpuusage -ge $CPUUSAGE_KILL_THRESHOLD ]];then
            log_print $LOG "[$MODULE] [Warning] ${cpuusage}%(current cpu usage) > ${CPUUSAGE_KILL_THRESHOLD}%(threshold), wait $TCPDUMP_WAIT_SECS secs try agin"  $DEBUG
            sleep $TCPDUMP_WAIT_SECS
            continue
        fi

        PORT_RULE=$(get_filter_list)

        log_print $LOG "[$MODULE] [Info] tcpdump -i ${INTERFACE_NOW} -s0 -Z root -B 20000 -G ${ROTATE_SECS} ${PORT_RULE} -w ${PCAP_OUTPUT}/${PCAP_FORMAT}-${INTERFACE_NOW}.pcap"  $DEBUG
        if [[ -f ${CURRENTDIR}/../tools/tcpdump ]];then
            ${CURRENTDIR}/../tools/tcpdump -i ${INTERFACE_NOW} -s0  -Z root -B 50000 -G ${ROTATE_SECS} ${PORT_RULE} -w ${PCAP_OUTPUT}/${PCAP_FORMAT}-${INTERFACE_NOW}.pcap  2>>$LOG
        else
            tcpdump -i ${INTERFACE_NOW} -s0 -Z root -B 50000 -G ${ROTATE_SECS} ${PORT_RULE} -w ${PCAP_OUTPUT}/${PCAP_FORMAT}-${INTERFACE_NOW}.pcap  2>>$LOG
        fi

        if [ $? -ne 0 ];then
            exit 1
        fi
    done
}

INTERFACE_LISR=`echo ${INTERFACE} | tr ',' ' '`

for inter in ${INTERFACE_LISR}
do
    capture_run ${inter} &
done

