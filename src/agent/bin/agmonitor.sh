#!/bin/bash

MODULE=Monitor

CURRENTDIR=$(cd $(dirname $0); pwd)
. $CURRENTDIR/parse.sh
. $CURRENTDIR/health.sh
. $CURRENTDIR/log.sh


CFG=$CURRENTDIR/../conf/ag.ini

#PCAP目录
PCAP_OUTPUT=`readIni $CFG CAPTURE PCAP_OUTPUT`

#PCAP目录容量
PCAP_OUTPUT_CAPCITY=`readIni $CFG CAPTURE PCAP_OUTPUT_CAPCITY`

#目录容量检查时间间隔
CHECK_SECS=`readIni $CFG MONITOR CHECK_SECS`

#CPU监控阈值
CPUUSAGE_KILL_THRESHOLD=`readIni $CFG MONITOR CPUUSAGE_KILL_THRESHOLD`

#LOG
LOG=$CURRENTDIR/../log/monitor.log
DEBUG=`readIni $CFG LOG DEBUG`

log_print $LOG "[$MODULE] [Info] Start $MODULE"  $DEBUG

sleep 1


monitor_capcity(){
    CURRENT_SIZE=$(du -csm $PCAP_OUTPUT |head -n1 |cut -f1)
    if [[ "$CURRENT_SIZE" -ge $PCAP_OUTPUT_CAPCITY ]]; then
        check_capture silent
        if [ $? -eq 0 ];then
            continue
        fi

        log_print $LOG "[$MODULE] [Warning] Stop capture. ${CURRENT_SIZE}MB(current size) > ${PCAP_OUTPUT_CAPCITY}MB(max size)"  $DEBUG

        stop_capture
    fi
}

monitor_cpu(){
    cpuusage=`get_cpu_usage`
    if [ $cpuusage -ge $CPUUSAGE_KILL_THRESHOLD ];then
	#继续等待十秒钟，　若十秒钟之后，CPU占用率仍然超过阈值，就停止抓包
	sleep 10

	cpuusage=`get_cpu_usage`
	if [ $cpuusage -ge $CPUUSAGE_KILL_THRESHOLD ];then
            check_capture silent
            if [ $? -eq 0 ];then
                continue
            fi

            log_print $LOG "[$MODULE] [Warning] Stop capture. ${cpuusage}%(current cpu usage) > ${CPUUSAGE_KILL_THRESHOLD}%(threshold)"  $DEBUG

            stop_capture
	fi
    fi
}

if [[ `ps -ef | grep  "/bin/cpu.sh" | grep -v grep | wc -l` -eq 0 ]];then
    $CURRENTDIR/cpu.sh &
fi

while true
do
    sleep $CHECK_SECS

    monitor_capcity

    monitor_cpu

done
