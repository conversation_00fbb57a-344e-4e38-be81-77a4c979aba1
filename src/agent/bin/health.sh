#!/bin/bash

CURRENTDIR=$(cd $(dirname $0); pwd)
BASENAME=$(basename $CURRENTDIR)

if [ x"$BASENAME" != x"bin" ];then
    BINDIR=${CURRENTDIR}/bin/
else
    BINDIR=${CURRENTDIR}
fi


. ${BINDIR}/parse.sh

CFG=${BINDIR}/../conf/ag.ini

SCP_AS_USER=`readIni $CFG UPLOAD SCP_AS_USER`

if [ x"$SCP_AS_USER" == x"" ];then
    SCP_AS_USER="root"
fi

SCP_LOGIN_USER=`readIni $CFG UPLOAD SCP_LOGIN_USER`

if [ x"$SCP_LOGIN_USER" == x"" ];then
    SCP_LOGIN_USER= "root"
fi

check_capture(){
    capture_pid=`ps -ef | grep "agcapture.sh" | grep -v grep | awk '{print $2}'`
    if [ x"$capture_pid" != x"" ];then
        if [ x"$1" != x"silent" ];then
            printf "capture\t\t[running][$capture_pid]\n"
        fi
        return 1
    else
        if [ x"$1" != x"silent" ];then
            printf "capture\t\t[not running]\n"
        fi
        return 0
    fi
}

check_upload()
{
    upload_pid=`ps -ef | grep "agupload.sh" | grep -v grep | awk '{print $2}'`
    if [ x"$upload_pid" != x"" ];then
        if [ x"$1" != x"silent" ];then
            printf "upload\t\t[running][$upload_pid]\n"
        fi
        return 1
    else
        if [ x"$1" != x"silent" ];then
            printf "upload\t\t[not running]\n"
        fi
        return 0
    fi
}

check_monitor()
{
    monitor_pid=`ps -ef | grep "agmonitor.sh" | grep -v grep | awk '{print $2}'`
    if [ x"$monitor_pid" != x"" ];then
        if [ x"$1" != x"silent" ];then
            printf "monitor\t\t[running][$monitor_pid]\n"
        fi
        return 1
    else
        if [ x"$1" != x"silent" ];then
            printf "monitor\t\t[not running]\n"
        fi
        return 0
    fi
}

check_stats()
{
    stats_pid=`ps -ef | grep "agstats.sh" | grep -v grep | awk '{print $2}'`
    if [ x"$stats_pid" != x"" ];then
        if [ x"$1" != x"silent" ];then
            printf "stats\t\t[running] [$stats_pid]\n"
        fi
        return 1
    else
        if [ x"$1" != x"silent" ];then
            printf "stats\t\t[not running]\n"
        fi
        return 0
    fi
}

check_daemon()
{
    daemon_pid=`ps -ef | grep "agdaemon.sh" | grep -v grep | awk '{print $2}'`
    if [ x"$daemon_pid" != x"" ];then
        if [ x"$1" != x"silent" ];then
            printf "daemon\t\t[running][$daemon_pid]\n"
        fi
        return 1
    else
        if [ x"$1" != x"silent" ];then
            printf "damon\t\t[not running]\n"
        fi
        return 0
    fi
}

check_agent(){
    check_capture
    check_upload
    check_stats
    check_monitor
    check_daemon
}

stop_capture(){
    capture_pid=`ps -ef | grep "agcapture.sh" | grep -v grep | awk '{print $2}'`
    kill -9 $capture_pid 2>/dev/null

    tcpdump_pid_list=`ps -ef | grep "tcpdump" | grep -v grep | awk '{print $2}'`
    for tcpdump_pid in ${tcpdump_pid_list}
    do
        kill -9 $tcpdump_pid 2>/dev/null
    done

}

stop_upload(){
    upload_pid=`ps -ef | grep "agupload.sh" | grep -v grep | awk '{print $2}'`
    kill -9 $upload_pid 2>/dev/null
}

stop_monitor(){
    monitor_pid=`ps -ef | grep "agmonitor.sh" | grep -v grep | awk '{print $2}'`
    kill -9 $monitor_pid 2>/dev/null
}

stop_daemon(){
    daemon_pid=`ps -ef | grep "agdaemon.sh" | grep -v grep | awk '{print $2}'`
    kill -9 $daemon_pid 2>/dev/null

    cpu_pid=`ps -ef | grep "cpu.sh" | grep -v grep | awk '{print $2}'`
    kill -9 $cpu_pid 2>/dev/null
}

stop_stats()
{
    stats_id=`ps -ef | grep agstats.sh | grep -v grep | awk '{print $2}'`
    kill -9 $stats_id 2>/dev/null
}

stop_agent() {
    stop_capture
    stop_upload
    stop_monitor
    if [ x"$1" == x"" ];then
        stop_daemon
    fi
    stop_stats
}

start_agent(){
    $BINDIR/agcapture.sh  &
    $BINDIR/agupload.sh &
    $BINDIR/agmonitor.sh &

    if [ x"$1" == x"" ];then
        $BINDIR/agdaemon.sh &
    fi

    $BINDIR/agstats.sh &
}

start_module(){
    $BINDIR/$1.sh  &
}

check_depent(){
    ret=0

    PFRING_TCPDUMP=`readIni $CFG CAPTURE PFRING_TCPDUMP`

    COMMAND=curl
    command -v $COMMAND 1>/dev/null 2>/dev/null
    if [ $? != 0 ];then
        printf "check $COMMAND\t[Failed]\n"
	ret=1
    else
        printf "check $COMMAND\t[OK]\n"
    fi

    COMMAND=tcpdump
    if [[ -f ${BINDIR}/tools/tcpdump ]];then
        printf "check $COMMAND\t[OK]\n"
    else
        command -v $COMMAND 1>/dev/null 2>/dev/null
        if [ $? != 0 ];then
            printf "check $COMMAND\t[Failed]\n"
	        ret=1
        else
            printf "check $COMMAND\t[OK]\n"
        fi
    fi

    COMMAND=ifconfig
    command -v $COMMAND 1>/dev/null 2>/dev/null
    if [ $? != 0 ];then
        printf "check $COMMAND\t[Failed]\n"
	ret=1
    else
        printf "check $COMMAND\t[OK]\n"
    fi
    return $ret
}

check_ftp(){
    ret=0

    CURRENTDIR=$(cd $(dirname $0); pwd)
    . $CURRENTDIR/bin/parse.sh

    UPLOAD_METHOD=`readIni $CFG UPLOAD UPLOAD_METHOD`

    UPLOAD_PATH=`readIni $CFG UPLOAD UPLOAD_PATH`

    #配置文件
    CFG=$CURRENTDIR/conf/ag.ini

    #FTP server
    FTPSERVER=`readIni $CFG UPLOAD FTPSERVER`

    #FTP user
    FTPUSER=`readIni $CFG UPLOAD FTPUSER`

    #FTP password
    FTPPASSWORD=`readIni $CFG UPLOAD FTPPASSWORD`

    #PROTOCOL
    PROTOCOL=`readIni $CFG UPLOAD PROTOCOL`

    #CURL timeout
    TIMEOUT=60

    
    TEST_FILE=/tmp/ftpcheck
    touch $TEST_FILE

    if [[ ${UPLOAD_METHOD} == "0" ]]; then
        COMMAND=FTP
        if [[ ${PROTOCOL} == "FTP" ]];then
            curl -m $TIMEOUT -u $FTPUSER:$FTPPASSWORD -T $TEST_FILE ftp://$FTPSERVER/ -S -s 1>>/dev/null
        elif [[ ${PROTOCOL} == "FTPS" ]];then
            curl -m $TIMEOUT -k --ftp-ssl -u $FTPUSER:$FTPPASSWORD -T $TEST_FILE ftp://$FTPSERVER/ -S -s 1>>/dev/null
        else
            echo "protocol invaild, please check conf"
        fi
    else
        COMMAND=SCP
        runuser ${SCP_AS_USER} -c "scp ${TEST_FILE} ${SCP_LOGIN_USER}@$FTPSERVER:$UPLOAD_PATH" 1>>/dev/null
    fi
    if [ $? != 0 ];then
        printf "check $COMMAND\t[Failed]\n"
	ret=1
    else
        printf "check $COMMAND\t[OK]\n"
    fi

    return $ret
}

check_capcity(){
    #PCAP目录
    PCAP_OUTPUT=`readIni $CFG CAPTURE PCAP_OUTPUT`

    #PCAP目录容量
    PCAP_OUTPUT_CAPCITY=`readIni $CFG CAPTURE PCAP_OUTPUT_CAPCITY`

    CURRENT_SIZE=$(du -csm $PCAP_OUTPUT |head -n1 |cut -f1)
    if [[ "$CURRENT_SIZE" -ge $PCAP_OUTPUT_CAPCITY ]]; then
        if [ x"$1" != x"silent" ];then
            printf "check capcity\t[Failed][$PCAP_OUTPUT ${CURRENT_SIZE}MB(current size) > ${PCAP_OUTPUT_CAPCITY}MB(max size)]\n"
        fi
        return $CURRENT_SIZE
    else
        if [ x"$1" != x"silent" ];then
            printf "check capcity\t[OK][$PCAP_OUTPUT ${CURRENT_SIZE}MB(current size) < ${PCAP_OUTPUT_CAPCITY}MB(max size)]\n"
        fi
        return 0
    fi
}

check_env(){
    check_depent
    depent_ret=$?

    check_capcity
    capcity_ret=$?

    check_ftp
    ftp_ret=$?

    if [[ $depent_ret -ne 0 ]];then
        return 1
    fi
    if [[ $capcity_ret -ne 0 ]];then
        return 1
    fi
    if [[ $ftp_ret -ne 0 ]];then
        return 1
    fi
}

get_cpu_usage()
{
    CPUUSAGE=/tmp/cpuusage
    cpuusage=`cat $CPUUSAGE`
    if [[ x"$cpuusage" == x"" ]]; then
        cpuusage=0
    fi
    echo $cpuusage
}

