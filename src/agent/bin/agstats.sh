#!/bin/bash

MODULE=Stats
CURRENTDIR=$(cd $(dirname $0); pwd)

. $CURRENTDIR/parse.sh
. $CURRENTDIR/log.sh
. $CURRENTDIR/health.sh

CFG=$CURRENTDIR/../conf/ag.ini
upload_file=/tmp/upload_stats

INTERFACE=`readIni $CFG CAPTURE INTERFACE`       #网卡名称
STATS_SEC=`readIni $CFG STATS STATS_SEC`         #上传agent状态的时间间隔
STATS_DST_IP=`readIni $CFG STATS STATS_DST_IP`   #上传状态的目的地址
EVENTS=`readIni $CFG STATS EVENTS`
AGENT_IP=`readIni $CFG UPLOAD AGENT_IP`
LOG=$CURRENTDIR/../log/stats.log
DEBUG=`readIni $CFG LOG DEBUG`
GW_IP=`readIni $CFG UPLOAD FTPSERVER`

log_print $LOG "[$MODULE] [Info] Start $MODULE"  $DEBUG

while true
do
    sleep $STATS_SEC
    tm=`date +%s`       # 获取时间戳
    #echo $tm

    version=`grep "VERSION=" $CURRENTDIR/../qzkj_agent | awk -F= {'print $2'}`     #获取agent版本版本号
    #echo $version

    if [ x"$AGENT_IP" != x"" ]; then
        ip=$AGENT_IP
    elif [ x"$INTERFACE" == x"lo" ];then
        ip="127.0.0.1"
    elif [ x"$INTERFACE" == x"" ];then
        log_print $LOG "[$MODULE] [Error] INTERFACE name is null" $DEBUG
        ip=""
    # elif [ x"`ifconfig $INTERFACE 2>/dev/null | grep '\<ether\>' | awk '{print $2}'`" = x"" ]; then
    #     log_print $LOG "[$MODULE] [Error] INTERFACE name($INTERFACE) error" $DEBUG
    #     ip=""
    else
        # ip=$(ifconfig | tr '\n' '=' | sed 's/==/\n/g' | grep "`ifconfig $INTERFACE | grep '\<ether\>' | awk '{print $2}'`" | tr '=' '\n' | grep '\<inet\>' | awk '{print $2}')
        if [[ -f /etc/redhat-release ]];then #centos
            if [[ `cat /etc/redhat-release | grep "\s6.*" | wc -l` -eq 1 ]] ;then
                if [[ `ifconfig $INTERFACE 2>/dev/null | grep HWaddr | awk '{print $5}' | wc -l` -eq 0 ]];then
                    log_print $LOG "[$MODULE] [Error] INTERFACE name($INTERFACE) error" $DEBUG
                    ip=""
                else
                    ip=$(ifconfig | tr '\n' '=' | sed 's/==/\n/g'| grep `ifconfig $INTERFACE | grep HWaddr | awk '{print $5}'` | tr '=' '\n' | grep 'inet addr' | awk -F ':' '{print $2}' | awk '{print $1}')
                fi
            else
                if [[ `ifconfig $INTERFACE 2>/dev/null | grep '\<ether\>' | awk '{print $2}' | wc -l` -eq 0 ]];then
                    log_print $LOG "[$MODULE] [Error] INTERFACE name($INTERFACE) error" $DEBUG
                    ip=""
                else
                    ip=$(ifconfig | tr '\n' '=' | sed 's/==/\n/g' | grep "`ifconfig $INTERFACE | grep '\<ether\>' | awk '{print $2}'`" | tr '=' '\n' | grep '\<inet\>' | awk '{print $2}')
                fi
            fi
        elif [[ -f /etc/issue ]];then   #ubuntu
            ip=$(ifconfig $INTERFACE | grep '\<inet\>' | awk '{print $2}' | awk -F ':' '{print $2}')
        fi
    fi
    #echo $ip

    monitor_id=`ps -ef | grep agmonitor.sh | grep -v grep | awk '{print $2}'` #获取监控进程的状态
    if [ x"$monitor_id" = x"" ];then 
        monitor_stats=dead
    else
        monitor_stats=active
    fi 
    #echo "monitor stats $monitor_stats"

    daemon_id=`ps -ef | grep agdaemon.sh | grep -v grep | awk '{print $2}'`   #获取守护进程的状态
    if [ x"$daemon_id" = x"" ];then 
        daemon_stats=dead
    else
        daemon_stats=active
    fi
    #echo "daemon stats $daemon_stats"

    capture_id=`ps -ef | grep agcapture.sh | grep -v grep | awk '{print $2}'` #获取抓包进程的状态
    if [ x"$capture_id" = x"" ];then
        cap_stats=dead
    else
        cap_stats=active
    fi
    #echo "capture stats $cap_stats"

    upload_id=`ps -ef | grep agupload.sh | grep -v grep | awk '{print $2}'`  #获取上传文件进程的状态
    if [ x"$upload_id" = x"" ];then
        upload_stats=dead
    else
        upload_stats=active
    fi
    #echo "upload stats $upload_stats"

    cpu_usage=`get_cpu_usage`
    check_capcity silent
    pcap_capcity=$?

    while true
    do
        fuser $upload_file 1>/dev/null 2>/dev/null
        if [[ $? -eq 0 ]];then
            continue
        fi
        file_row=`awk '{print NR}' $upload_file | tail -n 1`
        if [[ x$file_row = x"" ]];then
            stats=""
            break;
        fi
        
        for ((i=1;i<=$file_row;++i))
        do
            #echo $i
            line=`awk 'NR=='$i'' $upload_file`
            #echo $line
            file_name=`echo $line | awk '{print $1}'`
            file_size=`echo $line | awk '{print $2}'`
            file_stats=`echo $line | awk '{print $3}'`
            file_protocol=`echo $line | awk '{print $4}'`
            if [[ $file_row = 1 ]];then 
                stats=\"file_stats\":[{\"file_name\":\"$file_name\",\"file_size\":$file_size,\"file_stats\":\"$file_stats\",\"file_protocol\":\"$file_protocol\"}]
            else
                if [[ $i = 1 ]];then
                    stats=\"file_stats\":[{\"file_name\":\"$file_name\",\"file_size\":$file_size,\"file_stats\":\"$file_stats\",\"file_protocol\":\"$file_protocol\"},
                    #echo ${stats}
                elif [[ $i = $file_row ]];then
                    stats=${stats}{\"file_name\":\"$file_name\",\"file_size\":$file_size,\"file_stats\":\"$file_stats\",\"file_protocol\":\"$file_protocol\"}]
                else
                    stats=${stats}{\"file_name\":\"$file_name\",\"file_size\":$file_size,\"file_stats\":\"$file_stats\",\"file_protocol\":\"$file_protocol\"},
                fi
            fi
        done
        #echo $stats
        for ((i=1;i<=$file_row;++i))
        do
            sed -i -n '1d' $upload_file
        done

        break
    done

    if [[ x$stats = x"" ]];then
        agent_stats={\"tm\":$tm,\"version\":\"$version\",\"agent_ip\":\"$ip\",\"gw_ip\":\"${GW_IP}\",\"monitor_stats\":\"$monitor_stats\",\"daemon_stats\":\"$daemon_stats\",\"cap_stats\":\"$cap_stats\",\"upload_stats\":\"$upload_stats\",\"cpu_usage\":$cpu_usage,\"pcap_capcity\":$pcap_capcity,\"file_stats\":[]}
    else

        agent_stats={\"tm\":$tm,\"version\":\"$version\",\"agent_ip\":\"$ip\",\"gw_ip\":\"${GW_IP}\",\"monitor_stats\":\"$monitor_stats\",\"daemon_stats\":\"$daemon_stats\",\"cap_stats\":\"$cap_stats\",\"upload_stats\":\"$upload_stats\",\"cpu_usage\":$cpu_usage,\"pcap_capcity\":$pcap_capcity,$stats}
    fi
    #echo $agent_stats

    log_print $LOG "[$MODULE] [Debug] curl -x POST -F 'data='${agent_stats}'' http://$STATS_DST_IP/$EVENTS"  $DEBUG
    curl -X POST  -F 'data='$agent_stats'' http://$STATS_DST_IP/$EVENTS -S -s 1>>$LOG 2>>$LOG
    #curl -X POST  -F 'data='$agent_stats'' http://$STATS_DST_IP/$EVENTS

done
