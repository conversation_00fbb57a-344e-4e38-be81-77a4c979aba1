#! /bin/bash

# 函数调用日志
function func_log()
{
	func=$1
	shift
	# ${func} $*
	# return
	echo Enter ${func} ...
	${func} $*
	echo Leave ${func} .
	echo
}

function remove_old_agent()
{
    DEST_PATH="/opt/apigw/agent/"

    if [[ -f "${DEST_PATH}./qzkj_agent" ]] ; then
        sudo bash "${DEST_PATH}./qzkj_agent" stop
    fi
    sudo rm -rf DEST_PATH
}

function main()
{
    func_log remove_old_agent
}

main