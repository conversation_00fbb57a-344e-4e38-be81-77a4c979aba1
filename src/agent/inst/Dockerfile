FROM centos:7

RUN yum -y install net-tools* \
    && yum -y install tcpdump \
    && yum -y install curl \
    && yum -y install initscripts \
    && yum -y install psmisc \ 
    && yum -y install unzip \
    && yum -y install sudo \
    && yum -y install which 

ARG prefix=/opt/qzkj_apigw/agent/
ENV AGENT_HOME=$prefix

RUN sudo mkdir -p "${prefix}./bin/" \
    "${prefix}./conf/" \
    "${prefix}./doc/" \
    "${prefix}./log" \
    "${prefix}./tools/"

ADD qzkj_agent "${prefix}"
RUN sudo chmod +x "${prefix}./"qzkj_agent
ADD bin/* "${prefix}/./bin/"
RUN chmod +x "${prefix}/./bin/"*.sh
ADD conf/ag.ini "${prefix}/./conf/"
ADD doc/* "${prefix}/./doc/"

CMD sh -c "${AGENT_HOME}/qzkj_agent start && tail -f /bin/bash"