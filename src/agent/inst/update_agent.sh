#! /bin/bash
#INSTALL_PATH="/opt/agent/"
BAK_PATH=/opt/agent/bak/
INSTALL_PATH=/opt/agent/install/
OLD_VERSION=`grep "VERSION=" /opt/agent/agent | awk -F= {'print $2'}`
#echo "old version $OLD_VERSION"
NEW_VERSION=`grep "VERSION=" agent | awk -F '=' '{print $2}'`
#echo "new version $NEW_VERSION"

#版本比较
function version_ge()          #大于等于
{
    test "$(echo "$@" | tr " " "\n" | sort -rV | head -n 1)" == "$1"
}

function check_version()  
{
    #判断agent服务是否开启
    if [[ `systemctl status agent | grep "running" | wc -l` -eq 1 ]];then
        systemctl stop agent
    fi

    if version_ge ${OLD_VERSION} ${NEW_VERSION};then
        echo -e "current version ${OLD_VERSION} greater than update version ${NEW_VERSION}\c"
        systemctl start gwhw      
        exit -1
    fi

    return 0
}

function bak()
{
    #cd /opt/agent/tools/
    #sh bak_agent.sh
    #cd -
    #return 0

    cd ${INSTALL_PATH}
    #找到当前版本的安装包
    file_name_set=`ls -rlt  | grep "^-" | awk '{print $9}'`
    file_name_arr=($file_name_set)

    for file_name in ${file_name_arr[*]}
    do 
        if [[ `echo "$file_name" | grep -E ".*zip$" | wc -l` -eq 1 ]];then
            mv $file_name $BAK_PATH
            cd - 1>/dev/null
            cd ${BAK_PATH}
	        #echo "bak file name  $file_name"
            unzip $file_name 1>/dev/null
            cp -f /opt/agent/conf/* inst/conf/       #拷贝当前版本的配置文件到inst目录
            rm -f $file_name
            zip -r "${file_name}" inst/ 1>/dev/null
            rm -rf inst/
            cd - 1>/dev/null
	    break
        fi
    done
}

function update()
{
    #cp -f agent "${INSTALL_PATH}"
    #cp -f bin/* "${INSTALL_PATH}bin"
    #cp -f doc/* "${INSTALL_PATH}doc"
    #cp -f tools/* "${INSTALL_PATH}tools"
    sh ./inst.sh 1>/dev/null 2>/dev/null

    sh tools/update_conf.sh "/opt/agent/conf/ag.ini" "conf/ag.ini" 1>/dev/null 2>/dev/null

    #systemctl start agent
}

function del_bak()
{
    cd ${BAK_PATH}
    pwd
    file_num=`ls -l | grep "^-" | wc -l`
    if [[ $file_num -gt 1 ]];then
        file_name_set=`ls -rlt  | grep "^-" | awk '{print $9}'`
        file_name_arr=($file_name_set)
	    #echo "${file_name_arr[*]}"
	    for ((i=0;i<$file_num-1;++i))
        do
	        #echo "file_name ${file_name_arr[$i]}"
            if [[ `echo "${file_name_arr[$i]}" | grep -E ".*zip$" | wc -l` -eq 1 ]];then
		        #echo "rm -f ${file_name_arr[$i]}"
                rm -f ${file_name_arr[$i]}
            fi
        done
        
    fi

    echo -e "update agent successfully\c"
    cd ${INSTALL_PATH}
    rm -rf inst/
}


function main()
{
    check_version

    bak

    update

    del_bak
}

main
