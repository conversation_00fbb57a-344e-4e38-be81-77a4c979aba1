#! /bin/bash

AGENT_PATH=/opt/agent/
AGENT_BAK=/opt/agent/bak/
INSTALL_PATH=/opt/agent/install/

if [[ `systemctl status agent | grep "running" | wc -l` -eq 1 ]];then
    systemctl stop agent
fi

sh ./inst.sh

if [[ -f conf/ag.ini ]];then
    sudo cp -f conf/ag.ini  "${AGENT_PATH}./conf/"
fi

#cp -f ../agent ${AGENT_PATH}
#cp -f ../bin/* "${AGENT_PATH}bin/"
#cp -f ../conf/* "${AGENT_PATH}conf/"
#cp -f ../doc/* "${AGENT_PATH}doc/"
#cp -f ../tools/* "${AGENT_PATH}tools/"

#systemctl start agent
#rm -rf ${AGENT_BAK}/*

cd ${INSTALL_PATH}
#删除当前版本的安装包
rm -f *
cd -
cd ${AGENT_BAK}

file_name_set=`ls -lt  | grep "^-" | awk '{print $9}'`
file_name_arr=($file_name_set)

for file_name in ${file_name_arr[*]}
do 
    if [[ `echo "$file_name" | grep -E ".*zip$" | wc -l` -eq 1 ]];then
        mv ${file_name} ${INSTALL_PATH}
    fi 
done

echo -e "rollback agent successfully\c"

rm -rf inst/
