#! /bin/bash

USE_PFRING=$1

# 函数调用日志
function func_log()
{
	func=$1
	shift
	# ${func} $*
	# return
	echo Enter ${func} ...
	${func} $*
	echo Leave ${func} .
	echo
}

function version_ge() { test "$(echo "$@" | tr " " "\n" | sort -rV | head -n 1)" == "$1"; }

function inst_command
{
    cd tools
    md5sum -c rpm.md5 > /dev/null || return

    if [[ -f libpcap-1.5.3-12.el7.x86_64.rpm ]];then
        rpm --force -ivh libpcap-1.5.3-12.el7.x86_64.rpm
    fi
    if [[ -f tcpdump-4.9.2-3.el7.x86_64.rpm ]];then
        rpm --force -ivh tcpdump-4.9.2*.rpm
    fi

    # md5 校验保证了文件完整和文件存在,这里if条件可以不需要
    if [[ -f libcurl-7.29.0-57.el7.x86_64.rpm && -f curl-7.29.0-57.el7.x86_64.rpm ]];then
	rpm --force -ivh nss-softokn-freebl-3.44.0-8.el7_7.x86_64.rpm \
			nss-softokn-3.44.0-8.el7_7.x86_64.rpm \
			nspr-4.21.0-1.el7.x86_64.rpm \
			nss-util-3.44.0-4.el7_7.x86_64.rpm \
			nss-3.44.0-7.el7_7.x86_64.rpm \
			nss-pem-1.0.3-7.el7.x86_64.rpm \
			libssh2-1.8.0-3.el7.x86_64.rpm \
			libcurl-7.29.0-57.el7.x86_64.rpm \
			curl-7.29.0*.rpm
    fi

    if [[ -f net-tools-2.0-0.25.20131004git.el7.x86_64.rpm ]];then
        rpm --force -ivh net-tools-2.0*.rpm
    fi

    if [[ -f psmisc-22.20-15.el7.x86_64.rpm ]];then
        rpm --force -ivh psmisc-22.20*.rpm
    fi
    cd -
}

function inst_agent()
{
    DEST_PATH="/opt/qzkj_apigw/agent/"

    if [[ -f "${DEST_PATH}./qzkj_agent" ]] ; then
        sudo bash "${DEST_PATH}./qzkj_agent" stop
    fi

    sudo mkdir -p "${DEST_PATH}./"
    sudo mkdir -p "${DEST_PATH}./bin/"
    sudo mkdir -p "${DEST_PATH}./conf/"
    sudo mkdir -p "${DEST_PATH}./doc/"
    sudo mkdir -p "${DEST_PATH}./log"
    sudo mkdir -p "${DEST_PATH}./tools/"

    sudo cp -f qzkj_agent "${DEST_PATH}./"
    sudo chmod +x "${DEST_PATH}./"qzkj_agent
    sudo cp -f bin/* "${DEST_PATH}/./bin/"
    sudo chmod +x "${DEST_PATH}/./bin/"*.sh
    if [[ ! -f "${DEST_PATH}/./conf/"ag.ini ]]; then
        sudo cp -f conf/ag.ini "${DEST_PATH}/./conf/"	
    fi
   
    sudo cp -f doc/* "${DEST_PATH}/./doc/"

    command -v fuser &>/dev/null && \
	command -v tcpdump &>/dev/null && \
	command -v curl &>/dev/null && \
	command -v ifconfig &>/dev/null || inst_command
    
    rm -f "${DEST_PATH}./tools/tcpdump"

    if [[ $USE_PFRING == "1" ]];then
        if [[ -f /etc/redhat-release ]];then
            KERNEL_VERSION=3.10.0-514.26.2
            if version_ge "$(uname -r | sed s,.el7.x86_64,,)" "${KERNEL_VERSION}"; then
                sudo cp -f tools/tcpdump-514 "${DEST_PATH}/./tools/"tcpdump
            fi
        fi
    fi
    #cat tools/agent_crontab.sh >> /var/spool/cron/root
    #sudo service crond restart 2>>/dev/null

    grep "bash ${DEST_PATH}/qzkj_agent start" /etc/rc.local || echo "sudo bash \"${DEST_PATH}./qzkj_agent\" start" >> /etc/rc.local
    sudo bash "${DEST_PATH}./qzkj_agent" start
    

}

function main()
{
    func_log inst_agent
}

main
