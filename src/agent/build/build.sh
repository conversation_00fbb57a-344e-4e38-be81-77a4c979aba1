#!/bin/bash
#VERSION=`grep "VERSION=" ../agent | awk -F= {'print $2'}`
#rm -fr agent_$VERSION
#mkdir -p agent_$VERSION
#mkdir out/ -p

#rsync -av ../* agent_$VERSION --exclude-from=exclude.lst

#zip -r out/agent_$VERSION.zip agent_$VERSION 


#rm -fr agent_$VERSION

BASEDIR=$(dirname $0)/
if [[ "${BASEDIR}" = "/" ]]; then
	echo "basedir error"
	exit 0
fi

function build_agent()
{
    VERSION=`grep "VERSION=" ${BASEDIR}../agent | awk -F= {'print $2'}`
    rm -rf "${BASEDIR}./agent"
    mkdir -p "${BASEDIR}./agent/qzkj_inst/bin"
    mkdir -p "${BASEDIR}./agent/qzkj_inst/conf"
    mkdir -p "${BASEDIR}./agent/qzkj_inst/doc"
    mkdir -p "${BASEDIR}./agent/qzkj_inst/tools"
    
    cp -f "${BASEDIR}/../agent" "${BASEDIR}/./agent/qzkj_inst/qzkj_agent"
    cp -f "${BASEDIR}/../bin/"* "${BASEDIR}/./agent/qzkj_inst/bin/"
    cp -f "${BASEDIR}/../conf/ag.ini" "${BASEDIR}/./agent/qzkj_inst/conf/"
    cp -f "${BASEDIR}/../doc/agent.pdf" "${BASEDIR}/./agent/qzkj_inst/doc/"
    cp -f "${BASEDIR}/../inst/inst.sh" "${BASEDIR}/./agent/qzkj_inst/"
    cp -f "${BASEDIR}/../inst/Dockerfile" "${BASEDIR}/./agent/qzkj_inst/"
    cp -f "${BASEDIR}/../inst/remove_old_agent.sh" "${BASEDIR}/./agent/qzkj_inst/"
    cp -f "${BASEDIR}/../tools/tcpdump-693" "${BASEDIR}/./agent/qzkj_inst/tools/" 
    cp -f "${BASEDIR}/../tools/tcpdump-514" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/curl-7.29"* "${BASEDIR}/./agent/qzkj_inst/tools/" 
    cp -f "${BASEDIR}/../tools/net-tools-2.0"* "${BASEDIR}/./agent/qzkj_inst/tools/" 
    cp -f "${BASEDIR}/../tools/psmisc-22.20"* "${BASEDIR}/./agent/qzkj_inst/tools/" 
    cp -f "${BASEDIR}/../tools/tcpdump-4.9.2"* "${BASEDIR}/./agent/qzkj_inst/tools/" 
    cp -f "${BASEDIR}/../tools/libpcap-1.5.3"* "${BASEDIR}/./agent/qzkj_inst/tools/" 
    cp -f "${BASEDIR}/../tools/libcurl-7.29.0"* "${BASEDIR}/./agent/qzkj_inst/tools/" 
    cp -f "${BASEDIR}/../tools/nss-pem-1.0.3-7.el7.x86_64.rpm" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/nss-3.44.0-7.el7_7.x86_64.rpm" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/nss-softokn-3.44.0-8.el7_7.x86_64.rpm" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/nss-softokn-freebl-3.44.0-8.el7_7.x86_64.rpm" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/nss-sysinit-3.44.0-7.el7_7.x86_64.rpm" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/nss-tools-3.44.0-7.el7_7.x86_64.rpm" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/nss-util-3.44.0-4.el7_7.x86_64.rpm" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/nspr-4.21.0-1.el7.x86_64.rpm" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/libssh2-1.8.0-3.el7.x86_64.rpm" "${BASEDIR}/./agent/qzkj_inst/tools/"
    cp -f "${BASEDIR}/../tools/rpm.md5" "${BASEDIR}/./agent/qzkj_inst/tools/"
    
    #cp -f "${BASEDIR}/../inst/update_agent.sh" "${BASEDIR}/./agent/inst"
    #cp -f "${BASEDIR}/../inst/rollback_agent.sh" "${BASEDIR}/./agent/inst"
    #cp -f "${BASEDIR}/../tools/"* "${BASEDIR}/./agent/inst/tools/" 

    chmod +x "${BASEDIR}/./agent/qzkj_inst/inst.sh"

    cd "${BASEDIR}/./agent"
    zip -r inst-${VERSION}-$(date -u '+%Y%m%dT%H%M%SZ').zip qzkj_inst/ -x .DS_Store -x \*/.DS_Store
    cd -
}

function build_out()
{
    mkdir -p "${BASEDIR}/./out"
    if [[ `ls "${BASEDIR}./agent/inst"*.zip 2>/dev/null | wc -l` -eq 1 ]]; then
        cp -f "${BASEDIR}/./agent/inst"*.zip "${BASEDIR}/./out/agent_inst.zip"
        cp -f "${BASEDIR}/./agent/inst"*.zip "${BASEDIR}/./out/"
    fi
}

function main()
{
    build_agent
    build_out
}

main
