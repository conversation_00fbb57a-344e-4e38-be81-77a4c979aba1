#! /bin/bash

CONF_PATH_OLD=$1
CONF_PATH_NEW=$2

echo "old conf ${CONF_PATH_OLD}"
echo "new conf ${CONF_PATH_NEW}"

function TraversalArray()
{
    arr=$1
    value=$2
    #echo "Traversal ${arr[*]}"
    #echo "Traversal $value"
    for arr_value in ${arr[*]}
    do
        if [[ $value == $arr_value ]];then
            return 0
        fi
    done

    return 1
}

#拿到所有旧配置文件的section
i=0
while read line
do
	if [[ `echo $line | grep -E '^\[.*\]$'` ]];then
		value=`echo $line | awk -F '[' '{print $2}' | awk -F ']' '{print $1}'`
		section_arr[i]=$value
		let i+=1	
	fi 
done < $CONF_PATH_OLD
#echo "old conf section ${section_arr[*]}"

#拿到所有新配置文件的section
j=0
while read line_new
do
    if [[ `echo $line_new | grep -E '^\[.*\]$'` ]];then
            value=`echo $line_new | awk -F '[' '{print $2}' | awk -F ']' '{print $1}'`
            section_new_arr[j]=$value
            let j+=1
    fi
done < $CONF_PATH_NEW
#echo "new conf section ${section_new_arr[*]}"

#echo "begin parser old conf"
for section in ${section_arr[*]}
do
	item_set=$(awk -F '=' '/^\[.*\]$/{a=0} /\['${section}'\]/{a=1} (a==1 && $1!~/;/ && $1!~/\[/){print $1}' ${CONF_PATH_OLD}) 
	#echo "======"
	#echo "section $section"
	#echo $item_set
	
	item_arr=($item_set)

    #判断新配置文件是否有该$section
    TraversalArray "${section_new_arr[*]}" $section
    if [[ $? -eq 0 ]];then
        for item in ${item_arr[*]}
        do 
	        #echo "$item"
            value_old=$(awk -F '=' '/\['${section}'\]/{a=1} (a==1 && "'${item}'"==$1){a=0;print $2}' ${CONF_PATH_OLD})
	        #echo "value old $value_old"
            value_new=$(awk -F '=' '/\['${section}'\]/{a=1} (a==1 && "'${item}'"==$1){a=0;print $2}' ${CONF_PATH_NEW})
	        #echo "value_new $value_new"
            if [[ x"$value_new" == x"" ]];then   #新配置文件没有的配置项，但是旧配置文件有的，需要删除旧文件配置
                #获取旧配置文件行号
                row_num=$(awk -F '=' '/\['${section}'\]/{a=1} (a==1 && "'${item}'"==$1){a=0;print NR}' ${CONF_PATH_OLD})
         	    #echo "row num $row_num"
	            sed -i "$row_num"d ${CONF_PATH_OLD}
                while [ $(sed -n "$row_num"p ${CONF_PATH_OLD} | awk '$1~/;/{print $1}' | wc -l) -ne 0 ]
                do
		            #echo "$row_num"
                    sed -i "$row_num"d ${CONF_PATH_OLD}
                done
            fi
        done
    else
        #全部删除
	    #echo "all del $item"
        for item in ${item_arr[*]}
        do  
            row_num=$(awk -F '=' '/\['${section}'\]/{a=1} (a==1 && "'${item}'"==$1){a=0;print NR}' ${CONF_PATH_OLD})
            sed -i "$row_num"d ${CONF_PATH_OLD}
            while [ $(sed -n "$row_num"p ${CONF_PATH_OLD} | awk '$1~/;/' | wc -l) -ne 0 ]
            do
                sed -i "$row_num"d ${CONF_PATH_OLD}
            done
        done
        section_row_num=$(awk -F '=' '/\['${section}'\]/ {print NR}' ${CONF_PATH_OLD})
        sed -i "$section_row_num"d ${CONF_PATH_OLD}
    fi
	
	#echo "======="
done
#echo "end parser old conf"
#echo "\n"

#echo "start parser new conf"

for section_new in ${section_new_arr[*]}
do
	item_new_set=$(awk -F '=' '/^\[.*\]$/{a=0} /\['$section_new'\]/{a=1} (a==1 && $1!~/;/ && $1!~/\[/){print $1}' ${CONF_PATH_NEW})
	#echo "<====>"
	#echo $item_new_set

    #判断旧配置文件是否有该$section
    TraversalArray "${section_arr[*]}" $section_new
    if [[ $? -eq 0 ]];then
        item_new_arr=($item_new_set)
        for item_new in ${item_new_set[*]}
        do
            value_new=$(awk -F '=' '/\['${section_new}'\]/{a=1} (a==1 && "'${item_new}'"==$1){a=0;print $2}' ${CONF_PATH_NEW})
	        #echo "value new $value_new"
            value_old=$(awk -F '=' '/\['${section_new}'\]/{a=1} (a==1 && "'${item_new}'"==$1){a=0;print $2}' ${CONF_PATH_OLD})
	        #echo "vale old $value_old"
            if [[ x"$value_old" == x"" ]];then #新配置文件有的配置项，但是旧文件没有配置，则需要在旧配置添加一行
                section_row_num=$(awk -F '=' '/\['$section_new'\]/{print NR}' ${CONF_PATH_OLD}) #获取section的行号，在section后添加信息
		        #echo "section row num $section_row_num"
                content=$(awk -F '=' '/\['${section_new}'\]/{a=1} (a==1 && "'${item_new}'"==$1){a=0;print $0}' ${CONF_PATH_NEW}) #获取要添加的信息
                sed "$section_row_num a$content" -i ${CONF_PATH_OLD}
		        let section_row_num+=1
                #注释同样拷贝
                content_row_num=$(awk -F '=' '/\['${section_new}'\]/{a=1} (a==1 && "'${item_new}'"==$1){a=0;print NR}' ${CONF_PATH_NEW}) #获取添加信息的行号
                let content_row_num+=1
                while [ `sed -n "$content_row_num"p ${CONF_PATH_NEW} | awk '$1~/;/' | wc -l` -ne 0 ]
                do 
		            #echo "content row num $content_row_num"
                    content=`sed -n "$content_row_num"p ${CONF_PATH_NEW}`
                    sed "$section_row_num a$content" -i ${CONF_PATH_OLD}
                    let section_row_num+=1
                    let content_row_num+=1
                done
            fi
        done
    else
        #在旧的文件末尾添加信息
        sed -i '$a\'"[$section_new\]"'' ${CONF_PATH_OLD}
        for item_new in ${item_new_set[*]}
        do
            item_row_num=$(awk -F '=' '/\['${section_new}'\]/{a=1} (a==1 && "'${item_new}'"==$1){a=0;print NR}' ${CONF_PATH_NEW}) #获取添加信息的行号
            item_content=`sed -n "$item_row_num"p ${CONF_PATH_NEW}`   #获取添加信息的内容
	        #echo "item row num $item_row_num"
	        #echo "item content $item_content"
            sed '$a\'"$item_content"'' -i ${CONF_PATH_OLD}
            let item_row_num+=1
            while [ `sed -n "$item_row_num"p ${CONF_PATH_NEW} | awk '$1~/;/' | wc -l` -ne 0 ]
            do 
                #echo "content row num $item_row_num"
                content=`sed -n "$item_row_num"p ${CONF_PATH_NEW}`
                sed '$a\'"$content"'' -i ${CONF_PATH_OLD}
                let item_row_num+=1
            done
        done
    fi
	#echo "<====>"


done	

#echo "end parser new conf"


