#! /bin/bash

CAPTURE_PATH="/opt/agent/bin/agcapture.sh"  #需要固定
UPLOAD_PATH="/opt/agent/bin/agupload.sh"
MONITOR_PATH="/opt/agent/bin/agmonitor.sh"
STATS_PATH="/opt/agent/bin/agstats.sh"
DAEMON_PATH="/opt/agent/bin/agdaemon.sh"
SERVICE_NAME="agent"

#if [[ `systemctl status $SERVICE_NAME | grep "running" | wc -l` -eq 1 ]];then
#    systemctl stop $SERVICE_NAME
#fi

systemctl start $SERVICE_NAME
if [[ `systemctl status ${SERVICE_NAME} | grep "running" | wc -l` -ne 1 ]];then
    echo -e "start agent failed\c"
    exit -1
fi


capture_id=`ps -ef | grep "agcapture.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$capture_id" == x"" ]];then
    echo -e "capture subgroup not start\c"
    exit -2
fi

upload_id=`ps -ef | grep "agupload.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$upload_id" == x"" ]];then
    echo -e "upload subgroup not start\c"
    exit -3
fi

monitor_id=`ps -ef | grep "agmonitor.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$monitor_id" == x"" ]];then
    echo -e "monitor subgroup not start\c"
    exit -4
fi

stats_pid=`ps -ef | grep "agstats.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$stats_pid" == x"" ]];then
    echo -e "stats subgroup not start\c"
    exit -5
fi

daemon_pid=`ps -ef | grep "agdaemon.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$daemon_pid" == x"" ]];then
    echo -e "daemon subgroup not start\c"
    exit -6
fi

echo -e "start agent successfully\c"