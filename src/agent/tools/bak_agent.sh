#! /bin/bash


VERSION=`grep "VERSION=" /opt/agent/agent | awk -F= {'print $2'}`
BAK_PATH=/opt/agent/bak/   #备份目录名称
BAK_NAME=agent_${VERSION}

mkdir -p "${BAK_PATH}/${BAK_NAME}"

mkdir -p "${BAK_PATH}/${BAK_NAME}/bin"
mkdir -p "${BAK_PATH}/${BAK_NAME}/conf"
mkdir -p "${BAK_PATH}/${BAK_NAME}/doc"
mkdir -p "${BAK_PATH}/${BAK_NAME}/tools"

cp -f /opt/agent/agent "${BAK_PATH}/${BAK_NAME}"
cp -f /opt/agent/bin/* "${BAK_PATH}/${BAK_NAME}/bin"
cp -f /opt/agent/doc/* "${BAK_PATH}/${BAK_NAME}/doc"
cp -f /opt/agent/tools/* "${BAK_PATH}/${BAK_NAME}/tools"
cp -f /opt/agent/conf/* "${BAK_PATH}/${BAK_NAME}/conf"

cd ${BAK_PATH}
zip -r "${BAK_NAME}.zip" ${BAK_NAME}/*

rm -rf ${BAK_NAME}
cd -