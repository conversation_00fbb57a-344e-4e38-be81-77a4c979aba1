#! /bin/bash

SERVICE_NAME="agent"

if [[ `systemctl status $SERVICE_NAME | grep "running" | wc -l ` -ne 1 ]];then
    echo "dead"
    exit -1
fi

capture_id=`ps -ef | grep "agcapture.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$capture_id" == x"" ]];then
    echo "capture subgroup dead"
else
    echo "capture subgroup running,  pid [$capture_id]"
fi

upload_id=`ps -ef | grep "agupload.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$upload_id" == x"" ]];then
    echo "upload subgroup dead"
else
    echo "upload subgroup running, pid [$upload_id]"
fi

monitor_id=`ps -ef | grep "agmonitor.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$monitor_id" == x"" ]];then
    echo "monitor subgroup dead"
else
    echo "monitor subgroup running, pid [$monitor_id]"
fi

stats_pid=`ps -ef | grep "agstats.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$stats_pid" == x"" ]];then
    echo "stats subgroup dead"
else
    echo "stats subgroup running, pid [$stats_pid]"
fi

daemon_pid=`ps -ef | grep "agdaemon.sh" | grep -v grep | awk '{print $2}'`
if [[ x"$daemon_pid" == x"" ]];then
    echo "daemon subgroup dead"
else
    echo "daemon subgroup running, pid [$daemon_pid]"
fi