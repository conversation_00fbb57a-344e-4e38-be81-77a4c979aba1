#!/usr/bin/python
#coding=utf-8

import socket 
import os
import urllib
import urllib2
import zipfile
import shutil
import subprocess

AGENT_CONF_PATH="/opt/agent/conf/ag.ini"
AGENT_LOG_PATH="/opt/agent/log/"
UPDATE_AGENT_PATH="/opt/agent/install/"
ROLLBACK_AGENT_PATH="/opt/agent/bak/"

def get_host_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
    finally:
        s.close()

    return ip


def get_service_info():
    dst_ip = None
    command_event = None
    data_event = None
    if not os.path.isfile(AGENT_CONF_PATH):
        return dst_ip,command_event,data_event
    
    fp = open(AGENT_CONF_PATH, 'r')
    lines = fp.readlines()

    find_section = 0
    for line in lines:
        if line.startswith('['):
            if line.startswith('[STATS]'):
                find_section = 1 
                #print (find_section, line)
                continue
            else:
                find_section = 0
                #print (find_section, line)
                continue

        if find_section == 1 and line.startswith('STATS_DST_IP'):
            dst_ip = line.split('=')[1].strip()
            #print dst_ip
            continue
        
        if find_section == 1 and line.startswith('COMMAND_EVENTS'):
            command_event = line.split('=')[1].strip()
            #print command_event
            continue

        if find_section == 1 and line.startswith('DATA_EVENTS'):
            data_event = line.split('=')[1].strip()
            #print data_event
            continue
    
    fp.close()
    return dst_ip, command_event, data_event

def start_agent():
    if not os.path.isfile("/opt/agent/tools/start_agent.sh"):
        return "start_agent.sh file not exist"

    p = subprocess.Popen(args=['sh /opt/agent/tools/start_agent.sh'], shell=True, stdout=subprocess.PIPE)
    start_agent_str = p.stdout.read()
    return start_agent_str

def stop_agent():
    if not os.path.isfile("/opt/agent/tools/stop_agent.sh"):
        return "stop_agent.sh file not exist"

    p = subprocess.Popen(args=['sh /opt/agent/tools/stop_agent.sh'], shell=True, stdout=subprocess.PIPE)
    stop_agent_str = p.stdout.read()
    return stop_agent_str

def status_agent():
    if not os.path.isfile("/opt/agent/tools/status_agent.sh"):
        return "status_agent.sh file not exist"

    status_agent_dict={}
    p = subprocess.Popen(args=['sh /opt/agent/tools/status_agent.sh'], shell=True, stdout=subprocess.PIPE)
    status_agent_str = p.stdout.read()
    status_agent_list = status_agent_str.split('\n')
    
    if len(status_agent_list) == 2:
        status_agent_dict.update({'agent_status': status_agent_list[0]})
    else:
        status_agent_dict.update({'capture_status':status_agent_list[0]})
        status_agent_dict.update({'upload_status':status_agent_list[1]})
        status_agent_dict.update({'monitor_status':status_agent_list[2]})
        status_agent_dict.update({'stats_status':status_agent_list[3]})
        status_agent_dict.update({'daemon_status':status_agent_list[4]})

    return status_agent_dict

def read_conf():
    if not os.path.isfile(AGENT_CONF_PATH):
        return "agent conf file not exist"
    
    conf_value_list = []
    fp = open(AGENT_CONF_PATH, 'r')
    lines = fp.readlines()

    for line in lines:
        conf_value_list.append(line)

    fp.close()
    return conf_value_list

def write_conf(data_url):
    handle = urllib2.urlopen(data_url)
    conf_data = handle.read()
    if len(conf_data) == 0:
        return "modify agent conf data len is zero"

    if not os.path.isfile("../conf/ag.ini"):
        return "conf file not exist"

    os.rename("../conf/ag.ini", "../conf/ag.ini_bak")
    try:
        fp = open("../conf/ag.ini", "w")
        fp.write(conf_data)
    except IOError:
        os.rename("../conf/ag.ini_bak", "../conf/ag.ini")
        return "write conf file failed"
    else:
        os.remove("../conf/ag.ini_bak")
        fp.close()
        return "write conf successfully"

def log_list():
    if not os.path.isdir(AGENT_LOG_PATH):
        return "log dir not exist"
    
    log_file = os.listdir(AGENT_LOG_PATH)

    return log_file

def download_log(inputfile, row_line):
    if not os.path.isfile(AGENT_LOG_PATH + inputfile):
        return "log file not exist"
    
    last_line_list = {}
    fp = open(AGENT_LOG_PATH + inputfile, 'r')
    lines = fp.readlines()
    count = len(lines)
    if count == 0:
        fp.close()
        return last_line_list
    #print count
    if count > row_line:
        if row_line == 0:
            num = count
        else:
            num = row_line
    else:
        num = count
    #print num
    for i in range(1, (num+1)):
        n = -(num+1-i)
        last_line = lines[n].strip()
        last_line_list.append(last_line)

    fp.close()
    return last_line_list

def update_agent():
    if not os.path.isdir(UPDATE_AGENT_PATH):
        return "update dir not exist"

    os.chdir(UPDATE_AGENT_PATH)
    if os.path.isdir("inst"): #判断是否有inst目录
        shutil.rmtree("inst")   #删除inst目录 

    #目录下的文件按照修改时间逆序输出
    dir_list = os.listdir(UPDATE_AGENT_PATH)
    dir_list = sorted(dir_list,  key=lambda x: os.path.getmtime(os.path.join(UPDATE_AGENT_PATH, x)), reverse=True)
    update_file=None
    for file in dir_list:
        if os.path.isfile(file):
            # 判断是否是以.zip结尾的zip文件
            if file.endswith(".zip"):
                update_file = file
                break
            else:
                continue

    if not update_file:
        return "update zip file not exist"

    # 解压zip文件
    file_zip = zipfile.ZipFile(update_file, 'r')
    for file_list in file_zip.namelist():
        file_zip.extract(file_list, r'.')
    
    file_zip.close()

    if not os.path.isdir("inst"):
        return "unzip update zip file failed"
    
    os.chdir("inst")
    if not os.path.isfile("update_agent.sh"):
        return "update_agent.sh file not exist"
    p = subprocess.Popen(args=['sh update_agent.sh'], shell=True, stdout=subprocess.PIPE)
    re_value = p.stdout.read()

    return re_value

def rollback_agent():
    if not os.path.isdir(ROLLBACK_AGENT_PATH):
        return  "rollback path not exist"
    
    os.chdir(ROLLBACK_AGENT_PATH)
    if os.path.isdir("inst"): #判断是否有inst目录
        shutil.rmtree("inst")   #删除inst目录

    #目录下的文件按照修改时间逆序输出
    dir_list = os.listdir(ROLLBACK_AGENT_PATH)
    dir_list = sorted(dir_list,  key=lambda x: os.path.getmtime(os.path.join(ROLLBACK_AGENT_PATH, x)), reverse=True)
    rollback_file=None
    for file in dir_list:
        if os.path.isfile(file):
            if file.endswith(".zip"):
                rollback_file = file
                break
            else:
                continue
    
    if not rollback_file:
        return "rollback zip file not exist"
    
    #解压zip文件
    file_zip = zipfile.ZipFile(rollback_file, 'r')
    for file_list in file_zip.namelist():
        file_zip.extract(file_list, r'.')

    file_zip.close()

    if not os.path.isdir("inst"):
        return "unzip rollback zip file failed"

    os.chdir("inst")
    if not os.path.isfile("rollback_agent.sh"):
        return "rollback_gwhw.sh file not exist"
    p = subprocess.Popen(args=['sh rollback_agent.sh'], shell=True, stdout=subprocess.PIPE)
    re_value = p.stdout.read()
    return re_value

def get_operation_type():
    ip = get_host_ip()
    #print ip
    dst_ip,command_event,data_event = get_service_info()
    #print (dst_ip, command_event, data_event)
    if dst_ip == None or command_event == None or data_event == None:
        return
    
    params = urllib.urlencode({'ip':ip})
    command_url = "http://" + dst_ip + "/" + command_event + "?" + params
    #print command_url
    data_url = "http://" + dst_ip + "/" + data_event + "?" + params
    #print data_url
    
    r = urllib2.urlopen(command_url)
    data = r.read()
    if len(data) == 0:
        data_status = urllib.urlencode({'status':'no command'})
        urllib2.urlopen(command_url, data_status)
    data_list = data.split(',')

    re_value = None
    if data_list[0] == "start_agent":
        re_value = start_agent()
        start_agent_status = urllib.urlencode({'status':re_value})
        urllib2.urlopen(command_url, start_agent_status)

    elif data_list[0] == "stop_agent":
        re_value = stop_agent()
        stop_agent_status = urllib.urlencode({'status':re_value})
        urllib2.urlopen(command_url, stop_agent_status)

    elif data_list[0] == "status_agent":
        re_value = status_agent()
        status_agent_status = urllib.urlencode({'status':re_value})
        urllib2.urlopen(command_url, status_agent_status)

    elif data_list[0] == "read_conf":
        re_value = read_conf()
        read_conf_status = urllib.urlencode({'status':re_value})
        urllib2.urlopen(command_url, read_conf_status)

    elif data_list[0] == "write_conf":
        re_value = write_conf(data_url)
        write_conf_status = urllib.urlencode({'status':re_value})
        urllib2.urlopen(command_url, write_conf_status)

    elif data_list[0] == "log_list":
        re_value = log_list()
        log_list_status = urllib.urlencode({'status':re_value})
        urllib2.urlopen(command_url, log_list_status)

    elif data_list[0] == "download_log":
        if len(data_list) == 1:
            download_log_status = urllib.urlencode({'status':'no download log file name'})
            urllib2.urlopen(command_url, download_log_status)
        elif len(data_list) == 2:
            re_value = download_log(data_list[1], 0)
            download_log_status = urllib.urlencode({'status':re_value})
            urllib2.urlopen(command_url, download_log_status)
        else:
            try:
                row_line = int(data_list[2])
                re_value = download_log(data_list[1], data_list[2])
                download_log_status = urllib.urlencode({'status':re_value})
                urllib2.urlopen(command_url, download_log_status)
            except ValueError:
                download_log_status = urllib.urlencode({'status':'row line param invaild'})
                urllib2.urlopen(command_url, download_log_status)

    elif data_list[0] == "update_agent":
        re_value = update_agent()
        update_agent_status = urllib.urlencode({'status':re_value})
        urllib2.urlopen(command_url, update_agent_status)

    elif data_list[0] == "rollback_agent":
        re_value = rollback_agent()
        rollback_agent_status = urllib.urlencode({'status':re_value})
        urllib2.urlopen(command_url, rollback_agent_status)

    else:
        status = urllib.urlencode({'status':'invaild command'})
        urllib2.urlopen(command_url, status)

get_operation_type()

"""passman = urllib2.HTTPPasswordMgrWithDefaultRealm()
passman.add_password("Authentication Required", url, 'qzgw', 'conf1711')
auth_handler = urllib2.HTTPDigestAuthHandler(passman)
opener = urllib2.build_opener(urllib2.HTTPHandler, auth_handler)
urllib2.install_opener(opener)

err = False
try:
    data = urllib2.urlopen( url ).read()
    print data
except urllib2.URLError, e:
    if hasattr(e, "reason"):
        err = str(e.reason)
    elif hasattr(e, "code"):
        err = str(e.code)
if err is not False:
    print "[openWebIF] Fehler: %s" % err
else:
    print data"""